flowchart TD
    %% 项目创建流程
    subgraph "项目创建流程"
        A1[办展方创建项目] --> A2[设置项目信息]
        A2 --> A3[配置评选规则]
        A3 --> A4[邀请评委]
        A4 --> A5[发布项目]
    end

    %% 作品提交流程
    subgraph "作品提交流程"
        B1[艺术家发现项目] --> B2[查看参赛要求]
        B2 --> B3[准备作品]
        B3 --> B4[上传作品信息]
        B4 --> B5[等待审核]
        B5 --> B6{审核结果}
        B6 -->|通过| B7[进入评选]
        B6 -->|拒绝| B8[修改重提]
        B8 --> B4
    end

    %% 评审流程
    subgraph "评审流程"
        C1[评委接受邀请] --> C2[查看作品列表]
        C2 --> C3[逐一评分]
        C3 --> C4[填写评语]
        C4 --> C5[提交评分]
        C5 --> C6{是否完成}
        C6 -->|否| C2
        C6 -->|是| C7[评审完成]
    end

    %% 结果发布流程
    subgraph "结果发布流程"
        D1[统计所有评分] --> D2[计算最终成绩]
        D2 --> D3[确定获奖名单]
        D3 --> D4[生成获奖证书]
        D4 --> D5[公布结果]
        D5 --> D6[通知获奖者]
    end

    %% 流程连接
    A5 --> B1
    B7 --> C1
    C7 --> D1

    %% 样式定义
    classDef organizer fill:#45b7d1,stroke:#0984e3,stroke-width:2px,color:#fff
    classDef artist fill:#96ceb4,stroke:#00b894,stroke-width:2px,color:#fff
    classDef judge fill:#feca57,stroke:#e17055,stroke-width:2px,color:#fff
    classDef result fill:#fd79a8,stroke:#e84393,stroke-width:2px,color:#fff

    class A1,A2,A3,A4,A5 organizer
    class B1,B2,B3,B4,B5,B6,B7,B8 artist
    class C1,C2,C3,C4,C5,C6,C7 judge
    class D1,D2,D3,D4,D5,D6 result
