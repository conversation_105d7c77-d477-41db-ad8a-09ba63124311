import { Repository } from 'typeorm';
import { CreateArtworkDto } from './dto/create-artwork.dto';
import { UpdateArtworkDto } from './dto/update-artwork.dto';
import { BatchUploadDto } from './dto/batch-upload.dto';
import { Artwork, ArtworkStatus } from './entities/artwork.entity';
import { User } from '../users/entities/user.entity';
import { ProjectsService } from '../projects/projects.service';
import { FilesService } from '../files/files.service';
export declare class ArtworksService {
    private artworkRepository;
    private projectsService;
    private filesService;
    constructor(artworkRepository: Repository<Artwork>, projectsService: ProjectsService, filesService: FilesService);
    create(createArtworkDto: CreateArtworkDto, image: Express.Multer.File, user: User): Promise<Artwork>;
    batchUpload(batchUploadDto: BatchUploadDto, images: Express.Multer.File[], user: User): Promise<Artwork[]>;
    importFromExcel(projectId: string, excel: Express.Multer.File, user: User): Promise<{
        success: number;
        failed: number;
        errors: string[];
    }>;
    findAll(options: {
        page: number;
        limit: number;
        projectId?: string;
        status?: ArtworkStatus;
        authorName?: string;
        tags?: string[];
    }): Promise<{
        data: any;
        total: any;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    findByProject(projectId: string, options: {
        page: number;
        limit: number;
        status?: ArtworkStatus;
    }): Promise<{
        data: any;
        total: any;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    findOne(id: string): Promise<Artwork>;
    update(id: string, updateArtworkDto: UpdateArtworkDto, image: Express.Multer.File, user: User): Promise<Artwork>;
    updateStatus(id: string, status: ArtworkStatus, reviewComment: string, user: User): Promise<Artwork>;
    remove(id: string, user: User): Promise<void>;
    getProjectStats(projectId: string, user: User): Promise<{
        total: any;
        byStatus: any;
    }>;
    private validateProjectAccess;
    private generateArtworkNo;
}
