import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ControllersModule } from './controllers/controllers.module';
import { UsersModule } from './users/users.module';
import { ProjectsModule } from './projects/projects.module';
import { ArtworksModule } from './artworks/artworks.module';
import { ScoresModule } from './scores/scores.module';
import { ApplicationsModule } from './applications/applications.module';
import { FilesModule } from './files/files.module';
import { NotificationsModule } from './notifications/notifications.module';
import { AuthModule } from './auth/auth.module';
import { databaseConfig } from './config/database.config';

// 中间件
import { RateLimitMiddleware, CorsMiddleware, RequestIdMiddleware, SecurityMiddleware } from './common/middleware/rate-limit.middleware';

// 拦截器
import { ResponseInterceptor, LoggingInterceptor, SecurityInterceptor } from './common/interceptors/response.interceptor';

// 过滤器
import { HttpExceptionFilter } from './common/filters/http-exception.filter';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    TypeOrmModule.forRoot(databaseConfig),
    ControllersModule,
    AuthModule,
    UsersModule,
    ProjectsModule,
    ArtworksModule,
    ScoresModule,
    ApplicationsModule,
    FilesModule,
    NotificationsModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    // 全局拦截器
    {
      provide: APP_INTERCEPTOR,
      useClass: SecurityInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseInterceptor,
    },
    // 全局异常过滤器
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(RequestIdMiddleware, SecurityMiddleware, CorsMiddleware, RateLimitMiddleware)
      .forRoutes('*');
  }
}
