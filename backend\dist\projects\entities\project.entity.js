"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Project = exports.ProjectStatus = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../users/entities/user.entity");
var ProjectStatus;
(function (ProjectStatus) {
    ProjectStatus["PREPARING"] = "preparing";
    ProjectStatus["COLLECTING"] = "collecting";
    ProjectStatus["REVIEWING"] = "reviewing";
    ProjectStatus["JUDGING"] = "judging";
    ProjectStatus["DISPLAYING"] = "displaying";
    ProjectStatus["FINISHED"] = "finished";
})(ProjectStatus || (exports.ProjectStatus = ProjectStatus = {}));
let Project = class Project {
    id;
    name;
    description;
    coverImage;
    organizerId;
    status;
    collectStartTime;
    collectEndTime;
    judgeStartTime;
    judgeEndTime;
    displayStartTime;
    displayEndTime;
    qrCode;
    isPublic;
    createdAt;
    updatedAt;
    organizer;
};
exports.Project = Project;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Project.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100 }),
    __metadata("design:type", String)
], Project.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Project.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cover_image', nullable: true }),
    __metadata("design:type", String)
], Project.prototype, "coverImage", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'organizer_id' }),
    __metadata("design:type", String)
], Project.prototype, "organizerId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ProjectStatus,
        default: ProjectStatus.PREPARING,
    }),
    __metadata("design:type", String)
], Project.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'collect_start_time', type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], Project.prototype, "collectStartTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'collect_end_time', type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], Project.prototype, "collectEndTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'judge_start_time', type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], Project.prototype, "judgeStartTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'judge_end_time', type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], Project.prototype, "judgeEndTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'display_start_time', type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], Project.prototype, "displayStartTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'display_end_time', type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], Project.prototype, "displayEndTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'qr_code', nullable: true }),
    __metadata("design:type", String)
], Project.prototype, "qrCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_public', default: 1 }),
    __metadata("design:type", Number)
], Project.prototype, "isPublic", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], Project.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], Project.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'organizer_id' }),
    __metadata("design:type", user_entity_1.User)
], Project.prototype, "organizer", void 0);
exports.Project = Project = __decorate([
    (0, typeorm_1.Entity)('projects')
], Project);
//# sourceMappingURL=project.entity.js.map