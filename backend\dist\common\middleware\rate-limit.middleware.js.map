{"version": 3, "file": "rate-limit.middleware.js", "sourceRoot": "", "sources": ["../../../src/common/middleware/rate-limit.middleware.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAA4D;AAQrD,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IACb,QAAQ,GAAG,IAAI,GAAG,EAAgD,CAAC;IAGnE,MAAM,GAAG;QACxB,SAAS,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,GAAG,IAAI,EAAE;QAC/C,IAAI,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,GAAG,IAAI,EAAE;QAC1C,KAAK,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,GAAG,IAAI,EAAE;QAC3C,SAAS,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,GAAG,IAAI,EAAE;QAC/C,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,GAAG,IAAI,EAAE;QAC5C,WAAW,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,GAAG,IAAI,EAAE;KACnD,CAAC;IAEF,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;QACjD,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACjC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAGvB,IAAI,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAGpC,IAAI,CAAC,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YACtC,MAAM,GAAG;gBACP,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM;aAC9B,CAAC;YACF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,KAAK,EAAE,CAAC;QACjB,CAAC;QAGD,GAAG,CAAC,SAAS,CAAC,mBAAmB,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;QACnD,GAAG,CAAC,SAAS,CAAC,uBAAuB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QACnF,GAAG,CAAC,SAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC;QAGvE,IAAI,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;YAClC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,IAAI,EAAE,GAAG;gBACT,OAAO,EAAE,cAAc;gBACvB,KAAK,EAAE;oBACL,IAAI,EAAE,kBAAkB;oBACxB,OAAO,EAAE;wBACP,KAAK,EAAE,KAAK,CAAC,QAAQ;wBACrB,MAAM,EAAE,KAAK,CAAC,MAAM,GAAG,IAAI;wBAC3B,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;qBACvD;iBACF;gBACD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,SAAS;aACpD,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAKO,MAAM,CAAC,GAAY;QACzB,MAAM,IAAI,GAAI,GAAW,CAAC,IAAI,CAAC;QAC/B,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa,IAAI,SAAS,CAAC;QAE/D,IAAI,IAAI,EAAE,CAAC;YACT,OAAO,QAAQ,IAAI,CAAC,EAAE,EAAE,CAAC;QAC3B,CAAC;QAED,OAAO,MAAM,EAAE,EAAE,CAAC;IACpB,CAAC;IAKO,QAAQ,CAAC,GAAY;QAC3B,MAAM,IAAI,GAAI,GAAW,CAAC,IAAI,CAAC;QAE/B,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;QAC/B,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IACpD,CAAC;IAKO,OAAO;QACb,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,KAAK,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;YACpD,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;gBAC3B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;IACH,CAAC;CACF,CAAA;AAlGY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;GACA,mBAAmB,CAkG/B;AAOM,IAAM,cAAc,GAApB,MAAM,cAAc;IACzB,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;QACjD,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC;QAClC,MAAM,cAAc,GAAG;YACrB,uBAAuB;YACvB,uBAAuB;YACvB,4BAA4B;YAC5B,0BAA0B;YAC1B,yBAAyB;SAC1B,CAAC;QAGF,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACpC,GAAG,CAAC,SAAS,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAC;QACvD,CAAC;QAED,GAAG,CAAC,SAAS,CAAC,8BAA8B,EAAE,wCAAwC,CAAC,CAAC;QACxF,GAAG,CAAC,SAAS,CAAC,8BAA8B,EAAE;YAC5C,QAAQ;YACR,kBAAkB;YAClB,cAAc;YACd,QAAQ;YACR,eAAe;YACf,cAAc;YACd,kBAAkB;YAClB,YAAY;SACb,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACb,GAAG,CAAC,SAAS,CAAC,kCAAkC,EAAE,MAAM,CAAC,CAAC;QAC1D,GAAG,CAAC,SAAS,CAAC,wBAAwB,EAAE,OAAO,CAAC,CAAC;QAGjD,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAC7B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;YACtB,OAAO;QACT,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;CACF,CAAA;AAtCY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;GACA,cAAc,CAsC1B;AAOM,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAC9B,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;QACjD,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;QACnE,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,SAAmB,CAAC;QAClD,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;QACzC,IAAI,EAAE,CAAC;IACT,CAAC;IAEO,UAAU;QAChB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACxE,CAAC;CACF,CAAA;AAXY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;GACA,mBAAmB,CAW/B;AAOM,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAC7B,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;QAEjD,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAC5C,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACxC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,IAAI,EAAE,GAAG;gBACT,OAAO,EAAE,OAAO;gBAChB,KAAK,EAAE;oBACL,IAAI,EAAE,gBAAgB;oBACtB,OAAO,EAAE,oBAAoB;iBAC9B;gBACD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,SAAS;aACpD,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAGD,MAAM,aAAa,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,GAAG,CAAC,CAAC;QACrE,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;QACjC,IAAI,aAAa,GAAG,OAAO,EAAE,CAAC;YAC5B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,IAAI,EAAE,GAAG;gBACT,OAAO,EAAE,OAAO;gBAChB,KAAK,EAAE;oBACL,IAAI,EAAE,gBAAgB;oBACtB,OAAO,EAAE,0BAA0B,OAAO,QAAQ;iBACnD;gBACD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,SAAS;aACpD,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAGD,MAAM,iBAAiB,GAAG,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;QAC3D,KAAK,MAAM,MAAM,IAAI,iBAAiB,EAAE,CAAC;YACvC,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAClC,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBACrE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,IAAI,EAAE,GAAG;oBACT,OAAO,EAAE,QAAQ;oBACjB,KAAK,EAAE;wBACL,IAAI,EAAE,gBAAgB;wBACtB,OAAO,EAAE,4BAA4B;qBACtC;oBACD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,SAAS;iBACpD,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;QACH,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;CACF,CAAA;AA3DY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;GACA,kBAAkB,CA2D9B"}