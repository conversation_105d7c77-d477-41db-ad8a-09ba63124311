import { ConfigService } from '@nestjs/config';
export declare class FilesService {
    private configService;
    private readonly uploadPath;
    private readonly maxFileSize;
    private readonly allowedTypes;
    constructor(configService: ConfigService);
    uploadImage(file: Express.Multer.File, folder?: string): Promise<string>;
    uploadFile(file: Express.Multer.File, folder?: string): Promise<string>;
    uploadMultipleFiles(files: Express.Multer.File[], folder?: string): Promise<string[]>;
    generateThumbnail(imageUrl: string, width?: number, height?: number): Promise<string>;
    deleteFile(fileUrl: string): Promise<void>;
    getFileInfo(fileUrl: string): Promise<{
        exists: boolean;
        size?: number;
        mimeType?: string;
        lastModified?: Date;
    }>;
    private validateFile;
    private isImage;
    private ensureUploadDir;
    private getFileUrl;
    private getLocalPath;
    private getMimeType;
}
