{"name": "filenamify", "version": "6.0.0", "description": "Convert a string to a valid safe filename", "license": "MIT", "repository": "sindresorhus/filenamify", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {".": "./index.js", "./browser": "./filenamify.js"}, "engines": {"node": ">=16"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["filenamify-path.d.ts", "filenamify-path.js", "filenamify.d.ts", "filenamify.js", "index.d.ts", "index.js"], "keywords": ["filename", "safe", "sanitize", "file", "name", "string", "path", "filepath", "convert", "valid", "dirname"], "dependencies": {"filename-reserved-regex": "^3.0.0"}, "devDependencies": {"ava": "^5.2.0", "tsd": "^0.28.1", "xo": "^0.54.1"}}