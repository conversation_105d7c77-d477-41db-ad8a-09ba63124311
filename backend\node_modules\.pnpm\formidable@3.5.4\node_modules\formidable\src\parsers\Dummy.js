/* eslint-disable no-underscore-dangle */

import { Transform } from 'node:stream';

class DummyParser extends Transform {
  constructor(incomingForm, options = {}) {
    super();
    this.globalOptions = { ...options };
    this.incomingForm = incomingForm;
  }

  _flush(callback) {
    this.incomingForm.ended = true;
    this.incomingForm._maybeEnd();
    callback();
  }
}

export default DummyParser;
