小程序应用----一个书画作品评选打分的管理系统
1、项目管理。项目发起人创建展览项目，包括项目名称、评选标准填写，邀请美工、邀请评委（美工组上传作品清单、评委组点评作品），生产带有二维码的宣传海报。
1.1、项目发起人，填写项目名称、投稿要求、投稿方式、评选标准、点评模式（包括评委实名、匿名点评；点评评语是否公开）。
1.2、项目发起人，通过分享小程序邀请，组建美工组、组建评委组；
1.3、项目发起人填写评选标准：客观标准，不符合的淘汰不录用（例如尺寸不能超、不能横向、不能违背主题的）；主观标准，择优录用（例如满分100，临帖传承项占30分、结体结构项占30分、创意项占40等等，，）；系统提供评选的标准模板，可编辑、可删除、可增加。
1.4、生成带有二维码识别的宣传海报，邀请更多人投稿。同步到首页【正在筹稿】
1.5、项目创建成功、同步到首页；不同状态同步更新到首页。（首页有搜索功能、状态筛选功能）
1.6、普通用户可以点击查看项目状态，公布下一步时间计划。
状态1【正在筹稿】（不显示实际内容）；
状态2【正在统计】；
状态3【正在评选】（公布所有作品照片和编号、是否可以用户点评了？，鼓励转发互动）；
状态4【评选查看】（公布所有作品照片编号、评语与分数）；
状态5【电子展厅】，公布线下展厅地址和时间。
2、作品上传（pc端）。美工组上传作品清单，清单包括作品图片和excl表格（作品编号、作品尺寸、作品名称、作品介绍，作者名称、作者电话、作者地址、更多……）；
3、系统根据图片和作品清单，自动匹配生成单图、多图预览的展示样式。包括作品图片、作品编号，隐藏其他非必要信息。
4、评选多图预览模式。展示系列作品图片，作品编号，点评记录；
5、评选单图编辑模式。展示单图，后续是点评评语的编辑；
6、作品汇总。项目发起人能看到作品汇总，包括作品图片、作品编号、作品尺寸、作者名称、作者电话、作者地址、
7、评选结果的统计与汇总。项目发起人能看到，按作品看、按评委看、汇总统计（作品编号、各项打分总分、平均分、平均分排名）；
7、；