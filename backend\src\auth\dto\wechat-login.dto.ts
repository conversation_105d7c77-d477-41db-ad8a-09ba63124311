import { IsString, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

class WechatUserInfo {
  @IsOptional()
  @IsString()
  nickname?: string;

  @IsOptional()
  @IsString()
  avatarUrl?: string;

  @IsOptional()
  @IsString()
  gender?: string;

  @IsOptional()
  @IsString()
  country?: string;

  @IsOptional()
  @IsString()
  province?: string;

  @IsOptional()
  @IsString()
  city?: string;
}

export class WechatLoginDto {
  @IsString()
  code: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => WechatUserInfo)
  userInfo?: WechatUserInfo;
}
