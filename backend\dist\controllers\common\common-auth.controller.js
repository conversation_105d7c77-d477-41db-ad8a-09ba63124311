"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommonAuthController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const base_controller_1 = require("../../common/base/base.controller");
const auth_service_1 = require("../../auth/auth.service");
const login_dto_1 = require("../../auth/dto/login.dto");
const wechat_login_dto_1 = require("../../auth/dto/wechat-login.dto");
const register_dto_1 = require("../../auth/dto/register.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let CommonAuthController = class CommonAuthController extends base_controller_1.BaseController {
    authService;
    constructor(authService) {
        super();
        this.authService = authService;
    }
    async login(loginDto, req) {
        try {
            const result = await this.authService.login(loginDto);
            return this.success(result, '登录成功', req);
        }
        catch (error) {
            if (error.message.includes('用户名或密码错误')) {
                return this.error('用户名或密码错误', this.BusinessCode.UNAUTHORIZED, 'AUTHENTICATION_ERROR', undefined, undefined, req);
            }
            if (error.message.includes('已被禁用')) {
                return this.error('账户已被禁用，请联系管理员', this.BusinessCode.USER_DISABLED, 'AUTHENTICATION_ERROR', undefined, undefined, req);
            }
            throw error;
        }
    }
    async wechatLogin(wechatLoginDto, req) {
        try {
            const result = await this.authService.wechatLogin(wechatLoginDto);
            return this.success(result, '微信登录成功', req);
        }
        catch (error) {
            if (error.message.includes('微信登录失败')) {
                return this.error('微信登录失败，请重试', this.BusinessCode.BAD_REQUEST, 'AUTHENTICATION_ERROR', undefined, undefined, req);
            }
            throw error;
        }
    }
    async register(registerDto, req) {
        try {
            const result = await this.authService.register(registerDto);
            return this.created(result, '注册成功', req);
        }
        catch (error) {
            if (error.message.includes('已存在')) {
                return this.conflict(error.message, req);
            }
            if (error.message.includes('验证失败')) {
                return this.validationError(error.details, error.message, req);
            }
            throw error;
        }
    }
    async getProfile(req) {
        try {
            const profile = await this.authService.getProfile(req.user.id);
            return this.success(profile, '用户信息获取成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('用户不存在', req);
            }
            throw error;
        }
    }
    async refreshToken(req) {
        try {
            const result = await this.authService.refreshToken(req.user);
            return this.success(result, 'Token刷新成功', req);
        }
        catch (error) {
            throw error;
        }
    }
    async logout(req) {
        try {
            await this.authService.logout(req.user.id);
            return this.success(null, '退出登录成功', req);
        }
        catch (error) {
            throw error;
        }
    }
    async changePassword(oldPassword, newPassword, req) {
        try {
            await this.authService.changePassword(req.user.id, oldPassword, newPassword);
            return this.success(null, '密码修改成功', req);
        }
        catch (error) {
            if (error.message.includes('原密码错误')) {
                return this.error('原密码错误', this.BusinessCode.BAD_REQUEST, 'BUSINESS_ERROR', undefined, undefined, req);
            }
            throw error;
        }
    }
    async forgotPassword(email, req) {
        try {
            await this.authService.sendPasswordResetEmail(email);
            return this.success(null, '密码重置邮件已发送，请查收', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('邮箱不存在', req);
            }
            throw error;
        }
    }
    async resetPassword(token, newPassword, req) {
        try {
            await this.authService.resetPassword(token, newPassword);
            return this.success(null, '密码重置成功', req);
        }
        catch (error) {
            if (error.message.includes('令牌无效') || error.message.includes('已过期')) {
                return this.error('重置令牌无效或已过期', this.BusinessCode.BAD_REQUEST, 'BUSINESS_ERROR', undefined, undefined, req);
            }
            throw error;
        }
    }
    async verifyToken(req) {
        return this.success({
            valid: true,
            user: {
                id: req.user.id,
                username: req.user.username,
                role: req.user.role
            }
        }, 'Token验证成功', req);
    }
    async getLoginHistory(req) {
        try {
            const history = await this.authService.getLoginHistory(req.user.id);
            return this.success(history, '登录历史获取成功', req);
        }
        catch (error) {
            throw error;
        }
    }
};
exports.CommonAuthController = CommonAuthController;
__decorate([
    (0, common_1.Post)('login'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '用户登录' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '登录成功' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: '用户名或密码错误' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: '账户已被禁用' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [login_dto_1.LoginDto, Object]),
    __metadata("design:returntype", Promise)
], CommonAuthController.prototype, "login", null);
__decorate([
    (0, common_1.Post)('wechat-login'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '微信小程序登录' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '登录成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '微信登录失败' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [wechat_login_dto_1.WechatLoginDto, Object]),
    __metadata("design:returntype", Promise)
], CommonAuthController.prototype, "wechatLogin", null);
__decorate([
    (0, common_1.Post)('register'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({ summary: '用户注册' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '注册成功' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '用户名或邮箱已存在' }),
    (0, swagger_1.ApiResponse)({ status: 422, description: '参数验证失败' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [register_dto_1.RegisterDto, Object]),
    __metadata("design:returntype", Promise)
], CommonAuthController.prototype, "register", null);
__decorate([
    (0, common_1.Get)('profile'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取当前用户信息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Token无效或已过期' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CommonAuthController.prototype, "getProfile", null);
__decorate([
    (0, common_1.Post)('refresh'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '刷新Token' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Token刷新成功' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Token无效或已过期' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CommonAuthController.prototype, "refreshToken", null);
__decorate([
    (0, common_1.Post)('logout'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '退出登录' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '退出成功' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CommonAuthController.prototype, "logout", null);
__decorate([
    (0, common_1.Post)('change-password'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '修改密码' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '密码修改成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '原密码错误' }),
    __param(0, (0, common_1.Body)('oldPassword')),
    __param(1, (0, common_1.Body)('newPassword')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], CommonAuthController.prototype, "changePassword", null);
__decorate([
    (0, common_1.Post)('forgot-password'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '忘记密码 - 发送重置邮件' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '重置邮件发送成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '邮箱不存在' }),
    __param(0, (0, common_1.Body)('email')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CommonAuthController.prototype, "forgotPassword", null);
__decorate([
    (0, common_1.Post)('reset-password'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '重置密码' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '密码重置成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '重置令牌无效或已过期' }),
    __param(0, (0, common_1.Body)('token')),
    __param(1, (0, common_1.Body)('newPassword')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], CommonAuthController.prototype, "resetPassword", null);
__decorate([
    (0, common_1.Get)('verify-token'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '验证Token有效性' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Token有效' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Token无效或已过期' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CommonAuthController.prototype, "verifyToken", null);
__decorate([
    (0, common_1.Get)('login-history'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取登录历史' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CommonAuthController.prototype, "getLoginHistory", null);
exports.CommonAuthController = CommonAuthController = __decorate([
    (0, swagger_1.ApiTags)('公共接口-用户认证'),
    (0, common_1.Controller)('common/auth'),
    __metadata("design:paramtypes", [auth_service_1.AuthService])
], CommonAuthController);
//# sourceMappingURL=common-auth.controller.js.map