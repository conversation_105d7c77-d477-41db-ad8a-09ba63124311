import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { BaseController } from '../../common/base/base.controller';
import { AdminAuth } from '../../auth/decorators/auth.decorator';
import { CurrentUser, UserId } from '../../auth/decorators/user.decorator';
import { User } from '../../users/entities/user.entity';
import { RoleManagementService } from '../../auth/services/role-management.service';
import {
  ApplyGlobalRoleDto,
  ReviewRoleApplicationDto,
  AssignGlobalRoleDto,
  BatchAssignRolesDto,
  CreatePermissionTemplateDto,
  QueryRoleApplicationsDto,
} from '../../users/dto/role-management.dto';
import { GlobalRole } from '../../users/entities/user-roles.entity';

/**
 * 管理员角色管理控制器
 * 处理全局角色的分配、审批、管理等功能
 */
@ApiTags('管理员-角色管理')
@Controller('admin/role-management')
@AdminAuth()
export class AdminRoleManagementController extends BaseController {
  constructor(private readonly roleManagementService: RoleManagementService) {
    super();
  }

  /**
   * 获取角色申请列表
   */
  @Get('applications')
  @ApiOperation({ summary: '获取角色申请列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getRoleApplications(@Query() query: QueryRoleApplicationsDto, @Request() req) {
    try {
      const result = await this.roleManagementService.getRoleApplications(query);
      
      return this.paginated(
        result.data,
        result.total,
        result.page,
        result.limit,
        '角色申请列表获取成功',
        req
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * 审批角色申请
   */
  @Patch('applications/:applicationId/review')
  @ApiOperation({ summary: '审批角色申请' })
  @ApiResponse({ status: 200, description: '审批成功' })
  @ApiResponse({ status: 404, description: '申请不存在' })
  async reviewRoleApplication(
    @Param('applicationId') applicationId: string,
    @Body() reviewDto: ReviewRoleApplicationDto,
    @UserId() reviewerId: string,
    @Request() req
  ) {
    try {
      const result = await this.roleManagementService.reviewRoleApplication(
        applicationId,
        reviewerId,
        reviewDto.approved,
        reviewDto.reviewComment
      );

      return this.success(
        {
          application: result,
          action: reviewDto.approved ? 'approved' : 'rejected',
        },
        `申请${reviewDto.approved ? '批准' : '拒绝'}成功`,
        req
      );
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('申请记录不存在', req);
      }
      if (error.message.includes('已被处理')) {
        return this.conflict('申请已被处理', req);
      }
      throw error;
    }
  }

  /**
   * 直接分配全局角色
   */
  @Post('assign-role')
  @ApiOperation({ summary: '直接分配全局角色' })
  @ApiResponse({ status: 201, description: '分配成功' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  @ApiResponse({ status: 409, description: '用户已拥有此角色' })
  async assignGlobalRole(
    @Body() assignDto: AssignGlobalRoleDto,
    @UserId() grantedBy: string,
    @Request() req
  ) {
    try {
      const result = await this.roleManagementService.addGlobalRole(
        assignDto.userId,
        assignDto.role,
        grantedBy,
        assignDto.metadata
      );

      return this.created(
        result,
        '角色分配成功',
        req
      );
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('用户不存在', req);
      }
      if (error.message.includes('已拥有')) {
        return this.conflict('用户已拥有此角色', req);
      }
      throw error;
    }
  }

  /**
   * 批量分配角色
   */
  @Post('batch-assign')
  @ApiOperation({ summary: '批量分配角色' })
  @ApiResponse({ status: 201, description: '批量分配完成' })
  async batchAssignRoles(
    @Body() batchDto: BatchAssignRolesDto,
    @UserId() grantedBy: string,
    @Request() req
  ) {
    try {
      const results = await this.roleManagementService.batchAssignGlobalRoles(
        batchDto.assignments,
        grantedBy
      );

      return this.created(
        {
          successful: results.length,
          total: batchDto.assignments.length,
          results,
        },
        '批量角色分配完成',
        req
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * 移除用户的全局角色
   */
  @Delete('users/:userId/roles/:role')
  @ApiOperation({ summary: '移除用户的全局角色' })
  @ApiResponse({ status: 200, description: '移除成功' })
  @ApiResponse({ status: 404, description: '用户没有此角色' })
  async removeGlobalRole(
    @Param('userId') userId: string,
    @Param('role') role: GlobalRole,
    @Body('reason') reason: string,
    @UserId() removedBy: string,
    @Request() req
  ) {
    try {
      await this.roleManagementService.removeGlobalRole(
        userId,
        role,
        removedBy,
        reason
      );

      return this.success(
        null,
        '角色移除成功',
        req
      );
    } catch (error) {
      if (error.message.includes('没有此角色')) {
        return this.notFound('用户没有此角色', req);
      }
      throw error;
    }
  }

  /**
   * 获取用户的所有角色
   */
  @Get('users/:userId/roles')
  @ApiOperation({ summary: '获取用户的所有角色' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getUserRoles(@Param('userId') userId: string, @Request() req) {
    try {
      const roles = await this.roleManagementService.getUserGlobalRoles(userId);

      return this.success(
        {
          userId,
          roles,
          totalRoles: roles.length,
        },
        '用户角色获取成功',
        req
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * 获取权限模板列表
   */
  @Get('permission-templates')
  @ApiOperation({ summary: '获取权限模板列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getPermissionTemplates(@Query('role') role: string, @Request() req) {
    try {
      const templates = await this.roleManagementService.getPermissionTemplates(role as any);

      return this.success(
        templates,
        '权限模板获取成功',
        req
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * 创建权限模板
   */
  @Post('permission-templates')
  @ApiOperation({ summary: '创建权限模板' })
  @ApiResponse({ status: 201, description: '创建成功' })
  async createPermissionTemplate(
    @Body() templateDto: CreatePermissionTemplateDto,
    @Request() req
  ) {
    try {
      const template = await this.roleManagementService.createPermissionTemplate(
        templateDto.name,
        templateDto.role,
        templateDto.permissions,
        templateDto.description,
        templateDto.isDefault
      );

      return this.created(
        template,
        '权限模板创建成功',
        req
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * 获取角色统计信息
   */
  @Get('statistics')
  @ApiOperation({ summary: '获取角色统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getRoleStatistics(@Request() req) {
    try {
      const stats = await this.roleManagementService.getRoleStatistics();

      return this.success(
        stats,
        '角色统计信息获取成功',
        req
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * 获取所有用户的角色概览
   */
  @Get('users-overview')
  @ApiOperation({ summary: '获取所有用户的角色概览' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getUsersRoleOverview(
    @Query() query: { page?: number; limit?: number; role?: GlobalRole; search?: string },
    @Request() req
  ) {
    try {
      const { page = 1, limit = 20, role, search } = query;

      // 这里需要实现用户角色概览查询
      // 暂时返回示例数据
      const mockData = {
        data: [],
        total: 0,
        page,
        limit
      };

      return this.paginated(
        mockData.data,
        mockData.total,
        mockData.page,
        mockData.limit,
        '用户角色概览获取成功',
        req
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * 导出角色数据
   */
  @Get('export')
  @ApiOperation({ summary: '导出角色数据' })
  @ApiResponse({ status: 200, description: '导出成功' })
  async exportRoleData(
    @Query('format') format: 'excel' | 'csv' = 'excel',
    @Request() req
  ) {
    try {
      // 这里需要实现数据导出功能
      // 暂时返回成功响应
      return this.success(
        {
          format,
          downloadUrl: '/api/v1/admin/role-management/download/roles.xlsx',
          generatedAt: new Date(),
        },
        '角色数据导出成功',
        req
      );
    } catch (error) {
      throw error;
    }
  }
}
