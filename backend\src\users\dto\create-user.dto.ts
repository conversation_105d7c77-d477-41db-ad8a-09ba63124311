import {
  IsString,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  Is<PERSON>num,
  IsPhone<PERSON>umber,
  <PERSON><PERSON>eng<PERSON>,
  MaxLength,
} from 'class-validator';
import { UserRole, LoginType } from '../entities/user.entity';

export class CreateUserDto {
  @IsOptional()
  @IsString()
  openid?: string;

  @IsOptional()
  @IsString()
  unionid?: string;

  @IsOptional()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  username?: string;

  @IsOptional()
  @IsString()
  @MinLength(6)
  password?: string;

  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsString()
  @MaxLength(50)
  nickname?: string;

  @IsOptional()
  @IsString()
  avatarUrl?: string;

  @IsOptional()
  @IsPhoneNumber('CN')
  phone?: string;

  @IsOptional()
  @IsString()
  @MaxLength(50)
  realName?: string;

  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;

  @IsOptional()
  @IsEnum(LoginType)
  loginType?: LoginType;

  @IsOptional()
  status?: number;
}
