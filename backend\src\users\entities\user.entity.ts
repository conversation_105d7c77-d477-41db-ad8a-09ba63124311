import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { Exclude } from 'class-transformer';

export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  ORGANIZER = 'organizer',
  ARTIST = 'artist',
  JUDGE = 'judge',
  USER = 'user',
}

export enum LoginType {
  WECHAT = 'wechat',
  WEBSITE = 'website',
}

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true, nullable: true })
  openid: string;

  @Column({ nullable: true })
  unionid: string;

  @Column({ unique: true, nullable: true })
  username: string;

  @Column({ nullable: true })
  @Exclude()
  password: string;

  @Column({ nullable: true })
  email: string;

  @Column({ nullable: true })
  nickname: string;

  @Column({ name: 'avatar_url', nullable: true })
  avatarUrl: string;

  @Column({ nullable: true })
  phone: string;

  @Column({ name: 'real_name', nullable: true })
  realName: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.USER,
  })
  role: UserRole;

  @Column({
    name: 'login_type',
    type: 'enum',
    enum: LoginType,
    default: LoginType.WECHAT,
  })
  loginType: LoginType;

  @Column({ default: 1 })
  status: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  // @OneToMany(() => Project, project => project.organizer)
  // projects: Project[];

  // @OneToMany(() => Score, score => score.judge)
  // scores: Score[];

  // @OneToMany(() => OrganizerApplication, application => application.user)
  // applications: OrganizerApplication[];
}
