import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { UserGlobalRole, UserProjectRole } from './user-roles.entity';

export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  ORGANIZER = 'organizer',
  ARTIST = 'artist',
  JUDGE = 'judge',
  USER = 'user',
}

export enum UserStatus {
  ACTIVE = 1,      // 正常
  INACTIVE = 0,    // 禁用
  PENDING = 2,     // 待审核
  REJECTED = 3     // 已拒绝
}

export enum LoginType {
  WECHAT = 'wechat',
  WEBSITE = 'website',
}

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true, nullable: true })
  openid: string;

  @Column({ nullable: true })
  unionid: string;

  @Column({ unique: true, nullable: true })
  username: string;

  @Column({ nullable: true })
  @Exclude()
  password: string;

  @Column({ nullable: true })
  email: string;

  @Column({ nullable: true })
  nickname: string;

  @Column({ name: 'avatar_url', nullable: true })
  avatarUrl: string;

  @Column({ nullable: true })
  phone: string;

  @Column({ name: 'real_name', nullable: true })
  realName: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.USER,
  })
  role: UserRole;

  @Column({
    name: 'login_type',
    type: 'enum',
    enum: LoginType,
    default: LoginType.WECHAT,
  })
  loginType: LoginType;

  @Column({ type: 'text', nullable: true })
  bio: string;

  @Column({ type: 'json', nullable: true })
  profileData: {
    address?: string;
    birthDate?: string;
    gender?: 'male' | 'female' | 'other';
    profession?: string;
    education?: string;
    website?: string;
    socialMedia?: {
      wechat?: string;
      weibo?: string;
      qq?: string;
    };
  };

  @Column({ type: 'json', nullable: true })
  specialties: string[]; // ['书法', '绘画', '篆刻']

  @Column({ type: 'int', default: 0 })
  certificationLevel: number; // 认证等级

  @Column({ nullable: true })
  organizationId: string; // 所属机构

  @Column({ type: 'enum', enum: UserStatus, default: UserStatus.ACTIVE })
  status: UserStatus;

  @Column({ type: 'timestamp', nullable: true })
  lastLoginAt: Date;

  @Column({ type: 'int', default: 0 })
  loginCount: number;

  @Column({ type: 'json', nullable: true })
  settings: {
    language?: string;
    timezone?: string;
    emailNotifications?: boolean;
    smsNotifications?: boolean;
    theme?: 'light' | 'dark';
  };

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @OneToMany(() => UserGlobalRole, globalRole => globalRole.user)
  globalRoles: UserGlobalRole[];

  @OneToMany(() => UserProjectRole, projectRole => projectRole.user)
  projectRoles: UserProjectRole[];

  // 虚拟属性：获取所有全局角色
  get roles(): string[] {
    return this.globalRoles?.filter(gr => gr.isActive).map(gr => gr.role) || [];
  }

  // 虚拟属性：检查是否有特定全局角色
  hasGlobalRole(role: string): boolean {
    return this.roles.includes(role);
  }

  // 虚拟属性：检查是否是管理员
  get isAdmin(): boolean {
    return this.hasGlobalRole('super_admin') || this.hasGlobalRole('admin');
  }

  // 虚拟属性：获取主要角色（用于兼容性）
  get primaryRole(): string {
    if (this.hasGlobalRole('super_admin')) return 'super_admin';
    if (this.hasGlobalRole('admin')) return 'admin';
    if (this.hasGlobalRole('organizer')) return 'organizer';
    if (this.hasGlobalRole('judge')) return 'judge';
    if (this.hasGlobalRole('artist')) return 'artist';
    return 'user';
  }
}
