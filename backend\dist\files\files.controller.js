"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FilesController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const files_service_1 = require("./files.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const path = require("path");
const fs = require("fs");
let FilesController = class FilesController {
    filesService;
    constructor(filesService) {
        this.filesService = filesService;
    }
    async uploadImage(file, folder = 'images') {
        const fileUrl = await this.filesService.uploadImage(file, folder);
        return {
            success: true,
            data: {
                url: fileUrl,
                originalName: file.originalname,
                size: file.size,
            },
        };
    }
    async uploadDocument(file, folder = 'documents') {
        const fileUrl = await this.filesService.uploadFile(file, folder);
        return {
            success: true,
            data: {
                url: fileUrl,
                originalName: file.originalname,
                size: file.size,
            },
        };
    }
    async uploadMultiple(files, folder = 'batch') {
        const fileUrls = await this.filesService.uploadMultipleFiles(files, folder);
        return {
            success: true,
            data: fileUrls.map((url, index) => ({
                url,
                originalName: files[index].originalname,
                size: files[index].size,
            })),
        };
    }
    async generateThumbnail(imageUrl, width = 300, height = 300) {
        const thumbnailUrl = await this.filesService.generateThumbnail(imageUrl, width, height);
        return {
            success: true,
            data: {
                thumbnailUrl,
            },
        };
    }
    async getFileInfo(fileUrl) {
        const info = await this.filesService.getFileInfo(fileUrl);
        return {
            success: true,
            data: info,
        };
    }
    async deleteFile(fileUrl) {
        await this.filesService.deleteFile(fileUrl);
        return {
            success: true,
            message: '文件删除成功',
        };
    }
    async serveFile(filePath, res) {
        try {
            const fullPath = path.join(process.cwd(), 'uploads', filePath);
            if (!fs.existsSync(fullPath)) {
                return res.status(404).json({ message: '文件不存在' });
            }
            const stat = fs.statSync(fullPath);
            const fileExtension = path.extname(fullPath).toLowerCase();
            const mimeTypes = {
                '.jpg': 'image/jpeg',
                '.jpeg': 'image/jpeg',
                '.png': 'image/png',
                '.gif': 'image/gif',
                '.pdf': 'application/pdf',
                '.doc': 'application/msword',
                '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            };
            const contentType = mimeTypes[fileExtension] || 'application/octet-stream';
            res.setHeader('Content-Type', contentType);
            res.setHeader('Content-Length', stat.size);
            res.setHeader('Cache-Control', 'public, max-age=31536000');
            const stream = fs.createReadStream(fullPath);
            stream.pipe(res);
        }
        catch (error) {
            res.status(500).json({ message: '文件服务错误' });
        }
    }
};
exports.FilesController = FilesController;
__decorate([
    (0, common_1.Post)('upload/image'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Query)('folder')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof Express !== "undefined" && (_a = Express.Multer) !== void 0 && _a.File) === "function" ? _b : Object, String]),
    __metadata("design:returntype", Promise)
], FilesController.prototype, "uploadImage", null);
__decorate([
    (0, common_1.Post)('upload/document'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Query)('folder')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_d = typeof Express !== "undefined" && (_c = Express.Multer) !== void 0 && _c.File) === "function" ? _d : Object, String]),
    __metadata("design:returntype", Promise)
], FilesController.prototype, "uploadDocument", null);
__decorate([
    (0, common_1.Post)('upload/multiple'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)('files', 10)),
    __param(0, (0, common_1.UploadedFiles)()),
    __param(1, (0, common_1.Query)('folder')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String]),
    __metadata("design:returntype", Promise)
], FilesController.prototype, "uploadMultiple", null);
__decorate([
    (0, common_1.Post)('thumbnail'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Query)('imageUrl')),
    __param(1, (0, common_1.Query)('width')),
    __param(2, (0, common_1.Query)('height')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number]),
    __metadata("design:returntype", Promise)
], FilesController.prototype, "generateThumbnail", null);
__decorate([
    (0, common_1.Get)('info'),
    __param(0, (0, common_1.Query)('url')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FilesController.prototype, "getFileInfo", null);
__decorate([
    (0, common_1.Delete)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Query)('url')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FilesController.prototype, "deleteFile", null);
__decorate([
    (0, common_1.Get)('serve/*'),
    __param(0, (0, common_1.Param)('0')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], FilesController.prototype, "serveFile", null);
exports.FilesController = FilesController = __decorate([
    (0, common_1.Controller)('files'),
    __metadata("design:paramtypes", [files_service_1.FilesService])
], FilesController);
//# sourceMappingURL=files.controller.js.map