import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  Request,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { BaseController } from '../../common/base/base.controller';
import { UsersService } from '../../users/users.service';
import { CreateUserDto } from '../../users/dto/create-user.dto';
import { UpdateUserDto } from '../../users/dto/update-user.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/entities/user.entity';

/**
 * 管理端用户管理控制器
 * 路由前缀: /api/v1/admin/users
 */
@ApiTags('管理端-用户管理')
@Controller('admin/users')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.SUPER_ADMIN, UserRole.ADMIN)
@ApiBearerAuth()
export class AdminUsersController extends BaseController {
  constructor(private readonly usersService: UsersService) {
    super();
  }

  /**
   * 创建用户
   */
  @Post()
  @ApiOperation({ summary: '创建用户' })
  @ApiResponse({ status: 201, description: '用户创建成功' })
  @ApiResponse({ status: 400, description: '参数验证失败' })
  @ApiResponse({ status: 409, description: '用户已存在' })
  @Roles(UserRole.SUPER_ADMIN)
  async create(@Body() createUserDto: CreateUserDto, @Request() req) {
    try {
      const user = await this.usersService.create(createUserDto);
      return this.created(user, '用户创建成功', req);
    } catch (error) {
      if (error.message.includes('已存在')) {
        return this.conflict(error.message, req);
      }
      throw error;
    }
  }

  /**
   * 获取用户列表
   */
  @Get()
  @ApiOperation({ summary: '获取用户列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async findAll(@Query() query: any, @Request() req) {
    const { page, limit } = this.parsePagination(query);
    const { sort, order } = this.parseSort(query);
    
    const result = await this.usersService.findAll({
      page,
      limit,
      role: query.role,
      status: query.status ? parseInt(query.status) : undefined,
      search: query.search,
      sort,
      order
    });

    return this.paginated(
      result.data,
      result.total,
      result.page,
      result.limit,
      '用户列表获取成功',
      req
    );
  }

  /**
   * 获取用户详情
   */
  @Get(':id')
  @ApiOperation({ summary: '获取用户详情' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async findOne(@Param('id') id: string, @Request() req) {
    try {
      const user = await this.usersService.findOne(id);
      return this.success(user, '用户详情获取成功', req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('用户不存在', req);
      }
      throw error;
    }
  }

  /**
   * 更新用户信息
   */
  @Patch(':id')
  @ApiOperation({ summary: '更新用户信息' })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @Request() req
  ) {
    try {
      const user = await this.usersService.update(id, updateUserDto);
      return this.success(user, '用户信息更新成功', req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('用户不存在', req);
      }
      throw error;
    }
  }

  /**
   * 更新用户状态
   */
  @Patch(':id/status')
  @ApiOperation({ summary: '更新用户状态' })
  @ApiResponse({ status: 200, description: '状态更新成功' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async updateStatus(
    @Param('id') id: string,
    @Body('status') status: number,
    @Request() req
  ) {
    try {
      const user = await this.usersService.updateStatus(id, status);
      const statusText = status === 1 ? '启用' : '禁用';
      return this.success(user, `用户${statusText}成功`, req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('用户不存在', req);
      }
      throw error;
    }
  }

  /**
   * 更新用户角色
   */
  @Patch(':id/role')
  @ApiOperation({ summary: '更新用户角色' })
  @ApiResponse({ status: 200, description: '角色更新成功' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  @Roles(UserRole.SUPER_ADMIN)
  async updateRole(
    @Param('id') id: string,
    @Body('role') role: UserRole,
    @Request() req
  ) {
    try {
      const user = await this.usersService.updateRole(id, role);
      return this.success(user, '用户角色更新成功', req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('用户不存在', req);
      }
      throw error;
    }
  }

  /**
   * 删除用户
   */
  @Delete(':id')
  @ApiOperation({ summary: '删除用户' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  @Roles(UserRole.SUPER_ADMIN)
  async remove(@Param('id') id: string, @Request() req) {
    try {
      await this.usersService.remove(id);
      return this.success(null, '用户删除成功', req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('用户不存在', req);
      }
      throw error;
    }
  }

  /**
   * 批量删除用户
   */
  @Delete('batch')
  @ApiOperation({ summary: '批量删除用户' })
  @ApiResponse({ status: 200, description: '批量删除成功' })
  @Roles(UserRole.SUPER_ADMIN)
  async batchRemove(@Body('ids') ids: string[], @Request() req) {
    try {
      const result = await this.usersService.batchRemove(ids);
      return this.success(result, '批量删除完成', req);
    } catch (error) {
      throw error;
    }
  }

  /**
   * 重置用户密码
   */
  @Patch(':id/reset-password')
  @ApiOperation({ summary: '重置用户密码' })
  @ApiResponse({ status: 200, description: '密码重置成功' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  @Roles(UserRole.SUPER_ADMIN)
  async resetPassword(@Param('id') id: string, @Request() req) {
    try {
      const newPassword = await this.usersService.resetPassword(id);
      return this.success(
        { temporaryPassword: newPassword },
        '密码重置成功，请通知用户及时修改',
        req
      );
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('用户不存在', req);
      }
      throw error;
    }
  }

  /**
   * 获取用户统计信息
   */
  @Get('stats/overview')
  @ApiOperation({ summary: '获取用户统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getStats(@Request() req) {
    const stats = await this.usersService.getUserStats();
    return this.success(stats, '用户统计信息获取成功', req);
  }

  /**
   * 导出用户数据
   */
  @Get('export')
  @ApiOperation({ summary: '导出用户数据' })
  @ApiResponse({ status: 200, description: '导出成功' })
  async exportUsers(@Query() query: any, @Request() req) {
    const exportData = await this.usersService.exportUsers({
      role: query.role,
      status: query.status ? parseInt(query.status) : undefined,
      startDate: query.startDate,
      endDate: query.endDate,
      format: query.format || 'excel'
    });

    return this.success(exportData, '用户数据导出成功', req);
  }
}
