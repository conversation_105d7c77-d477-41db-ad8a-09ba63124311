"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthExamplesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const base_controller_1 = require("../../common/base/base.controller");
const auth_decorator_1 = require("../../auth/decorators/auth.decorator");
const user_decorator_1 = require("../../auth/decorators/user.decorator");
const user_entity_1 = require("../../users/entities/user.entity");
let AuthExamplesController = class AuthExamplesController extends base_controller_1.BaseController {
    async publicEndpoint(req) {
        return this.success({
            message: '这是一个公开接口，任何人都可以访问',
            timestamp: new Date().toISOString(),
            hasUser: !!req.user,
        }, '公开数据获取成功', req);
    }
    async protectedEndpoint(user, userId, userRole, req) {
        return this.success({
            message: '这是一个受保护的接口，需要登录才能访问',
            user: {
                id: user.id,
                username: user.username,
                role: user.role,
            },
            userId,
            userRole,
        }, '受保护数据获取成功', req);
    }
    async optionalEndpoint(user, isAuth, safeUser, clientIP, userAgent, req) {
        if (user) {
            return this.success({
                message: '欢迎回来！这里是为登录用户定制的内容',
                user: {
                    id: user.id,
                    username: user.username,
                    nickname: user.nickname,
                    role: user.role,
                },
                safeUser,
                isAuthenticated: isAuth,
                personalizedContent: [
                    '个性化推荐1',
                    '个性化推荐2',
                    '个性化推荐3',
                ],
                clientInfo: {
                    ip: clientIP,
                    userAgent,
                },
            }, '个性化内容获取成功', req);
        }
        else {
            return this.success({
                message: '这是公开内容，登录后可以看到更多个性化内容',
                isAuthenticated: isAuth,
                publicContent: [
                    '公开内容1',
                    '公开内容2',
                    '公开内容3',
                ],
                loginTip: '登录后可以获得个性化推荐',
                clientInfo: {
                    ip: clientIP,
                    userAgent,
                },
            }, '公开内容获取成功', req);
        }
    }
    async adminOnlyEndpoint(user, isAdmin, req) {
        return this.success({
            message: '这是管理员专用接口',
            adminUser: {
                id: user.id,
                username: user.username,
                role: user.role,
            },
            isAdmin,
            adminFeatures: [
                '用户管理',
                '系统配置',
                '数据统计',
            ],
        }, '管理员数据获取成功', req);
    }
    async superAdminOnlyEndpoint(user, isSuperAdmin, req) {
        return this.success({
            message: '这是超级管理员专用接口',
            superAdminUser: {
                id: user.id,
                username: user.username,
                role: user.role,
            },
            isSuperAdmin,
            superAdminFeatures: [
                '系统维护',
                '危险操作',
                '全局配置',
            ],
        }, '超级管理员数据获取成功', req);
    }
    async organizerOnlyEndpoint(user, req) {
        return this.success({
            message: '这是办展方专用接口',
            organizerUser: {
                id: user.id,
                username: user.username,
                role: user.role,
            },
            organizerFeatures: [
                '项目管理',
                '作品管理',
                '评分查看',
            ],
        }, '办展方数据获取成功', req);
    }
    async judgeOnlyEndpoint(user, req) {
        return this.success({
            message: '这是评委专用接口',
            judgeUser: {
                id: user.id,
                username: user.username,
                role: user.role,
            },
            judgeFeatures: [
                '作品评分',
                '评语录入',
                '评分历史',
            ],
        }, '评委数据获取成功', req);
    }
    async customRolesEndpoint(user, req) {
        return this.success({
            message: '这个接口只允许办展方和美工组访问',
            user: {
                id: user.id,
                username: user.username,
                role: user.role,
            },
            allowedRoles: ['organizer', 'artist'],
        }, '自定义角色数据获取成功', req);
    }
    async createSomething(data, user, userId, req) {
        return this.success({
            message: '数据创建成功',
            createdBy: {
                id: user.id,
                username: user.username,
            },
            userId,
            data,
            createdAt: new Date().toISOString(),
        }, '数据创建成功', req);
    }
    async errorExample(user, req) {
        if (user.role === 'user') {
            return this.forbidden('普通用户无权访问此功能', req);
        }
        return this.success({
            message: '这是一个可能出错的接口',
            user: {
                id: user.id,
                role: user.role,
            },
        }, '操作成功', req);
    }
};
exports.AuthExamplesController = AuthExamplesController;
__decorate([
    (0, auth_decorator_1.NoAuth)(),
    (0, common_1.Get)('public'),
    (0, swagger_1.ApiOperation)({ summary: '公开接口示例' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthExamplesController.prototype, "publicEndpoint", null);
__decorate([
    (0, auth_decorator_1.RequiredAuth)(),
    (0, common_1.Get)('protected'),
    (0, swagger_1.ApiOperation)({ summary: '受保护接口示例' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: '未授权' }),
    __param(0, (0, user_decorator_1.CurrentUser)()),
    __param(1, (0, user_decorator_1.UserId)()),
    __param(2, (0, user_decorator_1.UserRole)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, String, String, Object]),
    __metadata("design:returntype", Promise)
], AuthExamplesController.prototype, "protectedEndpoint", null);
__decorate([
    (0, auth_decorator_1.OptionalAuth)(),
    (0, common_1.Get)('optional'),
    (0, swagger_1.ApiOperation)({ summary: '可选认证接口示例' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, user_decorator_1.CurrentUser)()),
    __param(1, (0, user_decorator_1.IsAuthenticated)()),
    __param(2, (0, user_decorator_1.SafeUser)()),
    __param(3, (0, user_decorator_1.ClientIP)()),
    __param(4, (0, user_decorator_1.UserAgent)()),
    __param(5, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Boolean, Object, String, String, Object]),
    __metadata("design:returntype", Promise)
], AuthExamplesController.prototype, "optionalEndpoint", null);
__decorate([
    (0, auth_decorator_1.AdminAuth)(),
    (0, common_1.Get)('admin-only'),
    (0, swagger_1.ApiOperation)({ summary: '管理员专用接口' }),
    __param(0, (0, user_decorator_1.CurrentUser)()),
    __param(1, (0, user_decorator_1.IsAdmin)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Boolean, Object]),
    __metadata("design:returntype", Promise)
], AuthExamplesController.prototype, "adminOnlyEndpoint", null);
__decorate([
    (0, auth_decorator_1.SuperAdminAuth)(),
    (0, common_1.Get)('super-admin-only'),
    (0, swagger_1.ApiOperation)({ summary: '超级管理员专用接口' }),
    __param(0, (0, user_decorator_1.CurrentUser)()),
    __param(1, (0, user_decorator_1.IsSuperAdmin)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Boolean, Object]),
    __metadata("design:returntype", Promise)
], AuthExamplesController.prototype, "superAdminOnlyEndpoint", null);
__decorate([
    (0, auth_decorator_1.OrganizerAuth)(),
    (0, common_1.Get)('organizer-only'),
    (0, swagger_1.ApiOperation)({ summary: '办展方专用接口' }),
    __param(0, (0, user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], AuthExamplesController.prototype, "organizerOnlyEndpoint", null);
__decorate([
    (0, auth_decorator_1.JudgeAuth)(),
    (0, common_1.Get)('judge-only'),
    (0, swagger_1.ApiOperation)({ summary: '评委专用接口' }),
    __param(0, (0, user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], AuthExamplesController.prototype, "judgeOnlyEndpoint", null);
__decorate([
    (0, auth_decorator_1.RoleAuth)([user_entity_1.UserRole.ORGANIZER, user_entity_1.UserRole.ARTIST]),
    (0, common_1.Get)('custom-roles'),
    (0, swagger_1.ApiOperation)({ summary: '自定义角色权限接口' }),
    __param(0, (0, user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], AuthExamplesController.prototype, "customRolesEndpoint", null);
__decorate([
    (0, auth_decorator_1.RequiredAuth)(),
    (0, common_1.Post)('create-something'),
    (0, swagger_1.ApiOperation)({ summary: 'POST请求认证示例' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, user_decorator_1.CurrentUser)()),
    __param(2, (0, user_decorator_1.UserId)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, user_entity_1.User, String, Object]),
    __metadata("design:returntype", Promise)
], AuthExamplesController.prototype, "createSomething", null);
__decorate([
    (0, auth_decorator_1.RequiredAuth)(),
    (0, common_1.Get)('error-example'),
    (0, swagger_1.ApiOperation)({ summary: '错误处理示例' }),
    __param(0, (0, user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], AuthExamplesController.prototype, "errorExample", null);
exports.AuthExamplesController = AuthExamplesController = __decorate([
    (0, swagger_1.ApiTags)('认证示例'),
    (0, common_1.Controller)('examples/auth')
], AuthExamplesController);
//# sourceMappingURL=auth-examples.controller.js.map