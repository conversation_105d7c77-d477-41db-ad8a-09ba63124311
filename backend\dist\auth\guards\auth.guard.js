"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SmartAuthGuard = exports.JwtAuthGuard = exports.OptionalAuthGuard = exports.RequiredAuthGuard = exports.NoAuthGuard = exports.BaseAuthGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const jwt_1 = require("@nestjs/jwt");
const users_service_1 = require("../../users/users.service");
let BaseAuthGuard = class BaseAuthGuard {
    jwtService;
    usersService;
    reflector;
    constructor(jwtService, usersService, reflector) {
        this.jwtService = jwtService;
        this.usersService = usersService;
        this.reflector = reflector;
    }
    extractTokenFromHeader(request) {
        const authorization = request.headers.authorization;
        if (!authorization) {
            return undefined;
        }
        const [type, token] = authorization.split(' ');
        return type === 'Bearer' ? token : undefined;
    }
    async validateToken(token) {
        try {
            const payload = this.jwtService.verify(token);
            const user = await this.usersService.findOne(payload.sub);
            if (!user) {
                throw new common_1.UnauthorizedException('用户不存在');
            }
            if (user.status !== 1) {
                throw new common_1.UnauthorizedException('账户已被禁用');
            }
            return user;
        }
        catch (error) {
            if (error.name === 'JsonWebTokenError') {
                throw new common_1.UnauthorizedException('Token格式错误');
            }
            if (error.name === 'TokenExpiredError') {
                throw new common_1.UnauthorizedException('Token已过期');
            }
            throw error;
        }
    }
};
exports.BaseAuthGuard = BaseAuthGuard;
exports.BaseAuthGuard = BaseAuthGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof jwt_1.JwtService !== "undefined" && jwt_1.JwtService) === "function" ? _a : Object, users_service_1.UsersService,
        core_1.Reflector])
], BaseAuthGuard);
let NoAuthGuard = class NoAuthGuard {
    canActivate(context) {
        return true;
    }
};
exports.NoAuthGuard = NoAuthGuard;
exports.NoAuthGuard = NoAuthGuard = __decorate([
    (0, common_1.Injectable)()
], NoAuthGuard);
let RequiredAuthGuard = class RequiredAuthGuard extends BaseAuthGuard {
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const token = this.extractTokenFromHeader(request);
        if (!token) {
            throw new common_1.UnauthorizedException('请先登录');
        }
        try {
            const user = await this.validateToken(token);
            request.user = user;
            return true;
        }
        catch (error) {
            throw error;
        }
    }
};
exports.RequiredAuthGuard = RequiredAuthGuard;
exports.RequiredAuthGuard = RequiredAuthGuard = __decorate([
    (0, common_1.Injectable)()
], RequiredAuthGuard);
let OptionalAuthGuard = class OptionalAuthGuard extends BaseAuthGuard {
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const token = this.extractTokenFromHeader(request);
        if (!token) {
            request.user = null;
            return true;
        }
        try {
            const user = await this.validateToken(token);
            request.user = user;
            return true;
        }
        catch (error) {
            request.user = null;
            return true;
        }
    }
};
exports.OptionalAuthGuard = OptionalAuthGuard;
exports.OptionalAuthGuard = OptionalAuthGuard = __decorate([
    (0, common_1.Injectable)()
], OptionalAuthGuard);
let JwtAuthGuard = class JwtAuthGuard extends RequiredAuthGuard {
    constructor(jwtService, usersService, reflector) {
        super(jwtService, usersService, reflector);
    }
};
exports.JwtAuthGuard = JwtAuthGuard;
exports.JwtAuthGuard = JwtAuthGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_b = typeof jwt_1.JwtService !== "undefined" && jwt_1.JwtService) === "function" ? _b : Object, users_service_1.UsersService,
        core_1.Reflector])
], JwtAuthGuard);
let SmartAuthGuard = class SmartAuthGuard extends BaseAuthGuard {
    async canActivate(context) {
        const isPublic = this.reflector.getAllAndOverride('isPublic', [
            context.getHandler(),
            context.getClass(),
        ]);
        if (isPublic) {
            return true;
        }
        const isOptional = this.reflector.getAllAndOverride('isOptionalAuth', [
            context.getHandler(),
            context.getClass(),
        ]);
        const request = context.switchToHttp().getRequest();
        const token = this.extractTokenFromHeader(request);
        if (isOptional) {
            if (!token) {
                request.user = null;
                return true;
            }
            try {
                const user = await this.validateToken(token);
                request.user = user;
                return true;
            }
            catch (error) {
                request.user = null;
                return true;
            }
        }
        if (!token) {
            throw new common_1.UnauthorizedException('请先登录');
        }
        try {
            const user = await this.validateToken(token);
            request.user = user;
            return true;
        }
        catch (error) {
            throw error;
        }
    }
};
exports.SmartAuthGuard = SmartAuthGuard;
exports.SmartAuthGuard = SmartAuthGuard = __decorate([
    (0, common_1.Injectable)()
], SmartAuthGuard);
//# sourceMappingURL=auth.guard.js.map