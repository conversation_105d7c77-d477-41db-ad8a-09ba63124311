"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Comment = exports.Score = exports.ScoreStatus = void 0;
const typeorm_1 = require("typeorm");
const project_entity_1 = require("../../projects/entities/project.entity");
const artwork_entity_1 = require("../../artworks/entities/artwork.entity");
const user_entity_1 = require("../../users/entities/user.entity");
var ScoreStatus;
(function (ScoreStatus) {
    ScoreStatus["DRAFT"] = "draft";
    ScoreStatus["SUBMITTED"] = "submitted";
})(ScoreStatus || (exports.ScoreStatus = ScoreStatus = {}));
let Score = class Score {
    id;
    projectId;
    artworkId;
    judgeId;
    objectivePass;
    subjectiveScores;
    totalScore;
    status;
    scoredAt;
    updatedAt;
    project;
    artwork;
    judge;
};
exports.Score = Score;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Score.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'project_id' }),
    __metadata("design:type", String)
], Score.prototype, "projectId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'artwork_id' }),
    __metadata("design:type", String)
], Score.prototype, "artworkId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'judge_id' }),
    __metadata("design:type", String)
], Score.prototype, "judgeId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'objective_pass', type: 'tinyint', nullable: true }),
    __metadata("design:type", Number)
], Score.prototype, "objectivePass", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'subjective_scores', type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Score.prototype, "subjectiveScores", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_score', type: 'decimal', precision: 5, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Score.prototype, "totalScore", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ScoreStatus,
        default: ScoreStatus.DRAFT,
    }),
    __metadata("design:type", String)
], Score.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'scored_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' }),
    __metadata("design:type", Date)
], Score.prototype, "scoredAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], Score.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => project_entity_1.Project),
    (0, typeorm_1.JoinColumn)({ name: 'project_id' }),
    __metadata("design:type", project_entity_1.Project)
], Score.prototype, "project", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => artwork_entity_1.Artwork),
    (0, typeorm_1.JoinColumn)({ name: 'artwork_id' }),
    __metadata("design:type", artwork_entity_1.Artwork)
], Score.prototype, "artwork", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'judge_id' }),
    __metadata("design:type", user_entity_1.User)
], Score.prototype, "judge", void 0);
exports.Score = Score = __decorate([
    (0, typeorm_1.Entity)('scores')
], Score);
let Comment = class Comment {
    id;
    scoreId;
    content;
    audioUrl;
    annotations;
    isPublic;
    createdAt;
    updatedAt;
    score;
};
exports.Comment = Comment;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Comment.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'score_id' }),
    __metadata("design:type", String)
], Comment.prototype, "scoreId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Comment.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'audio_url', nullable: true }),
    __metadata("design:type", String)
], Comment.prototype, "audioUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Comment.prototype, "annotations", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_public', type: 'tinyint', default: 1 }),
    __metadata("design:type", Number)
], Comment.prototype, "isPublic", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], Comment.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], Comment.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Score),
    (0, typeorm_1.JoinColumn)({ name: 'score_id' }),
    __metadata("design:type", Score)
], Comment.prototype, "score", void 0);
exports.Comment = Comment = __decorate([
    (0, typeorm_1.Entity)('comments')
], Comment);
//# sourceMappingURL=score.entity.js.map