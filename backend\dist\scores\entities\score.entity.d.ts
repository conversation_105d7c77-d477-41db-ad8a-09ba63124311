import { Project } from '../../projects/entities/project.entity';
import { Artwork } from '../../artworks/entities/artwork.entity';
import { User } from '../../users/entities/user.entity';
export declare enum ScoreStatus {
    DRAFT = "draft",
    SUBMITTED = "submitted"
}
export declare class Score {
    id: string;
    projectId: string;
    artworkId: string;
    judgeId: string;
    objectivePass: number;
    subjectiveScores: Record<string, number>;
    totalScore: number;
    status: ScoreStatus;
    scoredAt: Date;
    updatedAt: Date;
    project: Project;
    artwork: Artwork;
    judge: User;
}
export declare class Comment {
    id: string;
    scoreId: string;
    content: string;
    audioUrl: string;
    annotations: any[];
    isPublic: number;
    createdAt: Date;
    updatedAt: Date;
    score: Score;
}
