import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  NotFoundException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ProjectPermissionService } from '../services/project-permission.service';
import { ProjectRole } from '../../users/entities/user-roles.entity';

/**
 * 项目权限守卫
 * 检查用户在特定项目中的权限
 */
@Injectable()
export class ProjectPermissionGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private projectPermissionService: ProjectPermissionService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new ForbiddenException('用户未认证');
    }

    // 获取项目ID
    const projectId = this.extractProjectId(request);
    if (!projectId) {
      throw new NotFoundException('项目ID未找到');
    }

    // 检查是否需要特定权限
    const requiredPermission = this.reflector.get<string>('project_permission', context.getHandler());
    const requiredRoles = this.reflector.get<ProjectRole[]>('project_roles', context.getHandler());
    const requireMember = this.reflector.get<boolean>('project_member', context.getHandler());

    // 超级管理员和系统管理员拥有所有项目权限
    if (user.hasGlobalRole('super_admin') || user.hasGlobalRole('admin')) {
      await this.attachProjectInfo(request, user.id, projectId);
      return true;
    }

    // 获取用户在项目中的角色和权限
    const projectRoles = await this.projectPermissionService.getUserProjectRoles(user.id, projectId);

    if (projectRoles.length === 0) {
      throw new ForbiddenException('您不是该项目的成员');
    }

    // 将项目角色和权限附加到请求对象
    await this.attachProjectInfo(request, user.id, projectId, projectRoles);

    // 如果只需要是项目成员，直接通过
    if (requireMember && !requiredPermission && !requiredRoles) {
      return true;
    }

    // 检查特定权限
    if (requiredPermission) {
      const hasPermission = await this.projectPermissionService.hasProjectPermission(
        user.id,
        projectId,
        requiredPermission
      );

      if (!hasPermission) {
        throw new ForbiddenException(`缺少项目权限: ${requiredPermission}`);
      }
    }

    // 检查特定角色
    if (requiredRoles && requiredRoles.length > 0) {
      const userRoles = projectRoles.map(pr => pr.role);
      const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));

      if (!hasRequiredRole) {
        throw new ForbiddenException(`需要以下角色之一: ${requiredRoles.join(', ')}`);
      }
    }

    return true;
  }

  /**
   * 从请求中提取项目ID
   */
  private extractProjectId(request: any): string | null {
    // 从路径参数中获取
    if (request.params.projectId) {
      return request.params.projectId;
    }

    // 从路径参数中获取 id（如果路由是 /projects/:id）
    if (request.params.id && request.route?.path?.includes('project')) {
      return request.params.id;
    }

    // 从查询参数中获取
    if (request.query.projectId) {
      return request.query.projectId;
    }

    // 从请求体中获取
    if (request.body?.projectId) {
      return request.body.projectId;
    }

    return null;
  }

  /**
   * 将项目信息附加到请求对象
   */
  private async attachProjectInfo(
    request: any,
    userId: string,
    projectId: string,
    projectRoles?: any[]
  ): Promise<void> {
    if (!projectRoles) {
      projectRoles = await this.projectPermissionService.getUserProjectRoles(userId, projectId);
    }

    // 附加项目角色
    request.projectId = projectId;
    request.projectRoles = projectRoles.map(pr => pr.role);

    // 合并所有权限
    const mergedPermissions = this.projectPermissionService.mergePermissions(projectRoles);
    request.projectPermissions = mergedPermissions;

    // 附加项目角色详情
    request.projectRoleDetails = projectRoles;
  }


}

/**
 * 多角色权限守卫
 * 检查用户是否拥有多个全局角色中的任意一个
 */
@Injectable()
export class MultiRoleGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.get<string[]>('multi_roles', context.getHandler());
    
    if (!requiredRoles) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      return false;
    }

    // 检查用户是否拥有任意一个所需角色
    return requiredRoles.some(role => user.hasGlobalRole(role));
  }
}

/**
 * 资源所有者守卫
 * 检查用户是否是资源的所有者
 */
@Injectable()
export class ResourceOwnerGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private permissionService: PermissionService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      return false;
    }

    // 超级管理员可以访问所有资源
    if (user.hasGlobalRole('super_admin')) {
      return true;
    }

    const resourceType = this.reflector.get<string>('resource_type', context.getHandler());
    const resourceId = this.extractResourceId(request, resourceType);

    if (!resourceId) {
      throw new NotFoundException('资源ID未找到');
    }

    // 检查资源所有权
    return await this.checkResourceOwnership(user.id, resourceType, resourceId);
  }

  private extractResourceId(request: any, resourceType: string): string | null {
    // 根据资源类型提取ID
    switch (resourceType) {
      case 'project':
        return request.params.projectId || request.params.id;
      case 'artwork':
        return request.params.artworkId || request.params.id;
      case 'score':
        return request.params.scoreId || request.params.id;
      default:
        return request.params.id;
    }
  }

  private async checkResourceOwnership(
    userId: string,
    resourceType: string,
    resourceId: string
  ): Promise<boolean> {
    // 这里需要根据具体的资源类型实现所有权检查
    // 暂时返回 true，具体实现需要在各个服务中完成
    return true;
  }
}
