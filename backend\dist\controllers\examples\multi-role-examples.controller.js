"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MultiRoleExamplesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const base_controller_1 = require("../../common/base/base.controller");
const auth_decorator_1 = require("../../auth/decorators/auth.decorator");
const project_auth_decorator_1 = require("../../auth/decorators/project-auth.decorator");
const user_decorator_1 = require("../../auth/decorators/user.decorator");
const user_entity_1 = require("../../users/entities/user.entity");
const permission_service_1 = require("../../auth/services/permission.service");
let MultiRoleExamplesController = class MultiRoleExamplesController extends base_controller_1.BaseController {
    permissionService;
    constructor(permissionService) {
        super();
        this.permissionService = permissionService;
    }
    async getMyRoles(user, req) {
        return this.success({
            userId: user.id,
            username: user.username,
            globalRoles: user.roles,
            primaryRole: user.primaryRole,
            isAdmin: user.isAdmin,
            specialties: user.specialties,
            certificationLevel: user.certificationLevel,
        }, '用户角色信息获取成功', req);
    }
    async getOwnerDashboard(projectId, user, projectRoles, req) {
        return this.success({
            message: '项目所有者仪表板',
            projectId,
            owner: {
                id: user.id,
                username: user.username,
            },
            projectRoles,
            ownerFeatures: [
                '项目设置管理',
                '成员权限管理',
                '项目删除',
                '数据导出',
            ],
        }, '所有者仪表板获取成功', req);
    }
    async getProjectManagement(projectId, user, isOwner, isManager, req) {
        return this.success({
            message: '项目管理功能',
            projectId,
            manager: {
                id: user.id,
                username: user.username,
            },
            permissions: {
                isOwner,
                isManager,
            },
            managementFeatures: [
                '邀请成员',
                '管理作品',
                '查看评分',
                '内容审核',
            ],
        }, '项目管理信息获取成功', req);
    }
    async getJudgingPanel(projectId, user, canScore, canViewScores, req) {
        return this.success({
            message: '评委评审面板',
            projectId,
            judge: {
                id: user.id,
                username: user.username,
                specialties: user.specialties,
            },
            permissions: {
                canScore,
                canViewScores,
            },
            judgingFeatures: [
                '作品评分',
                '评语录入',
                '评分历史',
                '评审进度',
            ],
        }, '评审面板获取成功', req);
    }
    async getMemberInfo(projectId, user, projectRoles, permissions, req) {
        return this.success({
            message: '项目成员信息',
            projectId,
            member: {
                id: user.id,
                username: user.username,
            },
            projectRoles,
            permissions,
            memberFeatures: [
                '查看项目信息',
                '参与讨论',
                '接收通知',
            ],
        }, '成员信息获取成功', req);
    }
    async exportProjectData(projectId, user, req) {
        return this.success({
            message: '项目数据导出',
            projectId,
            exporter: {
                id: user.id,
                username: user.username,
            },
            exportData: {
                projects: '项目基本信息',
                artworks: '作品列表',
                scores: '评分数据',
                members: '成员信息',
            },
        }, '数据导出成功', req);
    }
    async inviteUser(projectId, inviteData, user, req) {
        try {
            const defaultPermissions = this.permissionService.getDefaultProjectPermissions(inviteData.role);
            const permissions = { ...defaultPermissions, ...inviteData.permissions };
            const invitation = await this.permissionService.inviteUserToProject(projectId, inviteData.userId, inviteData.role, permissions, user.id, {
                role_description: `邀请担任${inviteData.role}`,
                message: inviteData.message,
            });
            return this.created({
                invitation,
                inviter: {
                    id: user.id,
                    username: user.username,
                },
                invitedRole: inviteData.role,
                permissions,
            }, '用户邀请发送成功', req);
        }
        catch (error) {
            if (error.message.includes('已存在')) {
                return this.conflict('用户已在项目中', req);
            }
            throw error;
        }
    }
    async acceptInvitation(projectId, userId, req) {
        try {
            await this.permissionService.acceptProjectInvitation(userId, projectId);
            return this.success({
                projectId,
                userId,
                status: 'accepted',
            }, '项目邀请接受成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('邀请不存在或已过期', req);
            }
            throw error;
        }
    }
    async updateMemberPermissions(projectId, targetUserId, newPermissions, user, req) {
        try {
            await this.permissionService.updateProjectRolePermissions(targetUserId, projectId, newPermissions);
            return this.success({
                projectId,
                targetUserId,
                newPermissions,
                updatedBy: {
                    id: user.id,
                    username: user.username,
                },
            }, '成员权限更新成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('用户不在项目中', req);
            }
            throw error;
        }
    }
    async getProjectMembers(projectId, canViewScores, req) {
        try {
            const members = await this.permissionService.getProjectMembers(projectId);
            const filteredMembers = members.map(member => ({
                id: member.id,
                userId: member.userId,
                role: member.role,
                status: member.status,
                user: {
                    id: member.user.id,
                    username: member.user.username,
                    nickname: member.user.nickname,
                    avatar: member.user.avatarUrl,
                },
                permissions: canViewScores ? member.permissions : undefined,
                metadata: {
                    invitedAt: member.metadata?.invitedAt,
                    acceptedAt: member.metadata?.acceptedAt,
                },
            }));
            return this.success({
                projectId,
                members: filteredMembers,
                totalMembers: members.length,
                canViewDetails: canViewScores,
            }, '项目成员列表获取成功', req);
        }
        catch (error) {
            throw error;
        }
    }
    async getConditionalData(projectId, user, isOwner, isManager, canScore, canViewScores, canExport, req) {
        const responseData = {
            projectId,
            user: {
                id: user.id,
                username: user.username,
            },
            permissions: {
                isOwner,
                isManager,
                canScore,
                canViewScores,
                canExport,
            },
        };
        if (isOwner) {
            responseData.ownerData = {
                projectSettings: '项目配置信息',
                financialData: '财务数据',
                memberManagement: '成员管理',
            };
        }
        if (isManager) {
            responseData.managerData = {
                memberList: '成员列表',
                activityLog: '活动日志',
                contentModeration: '内容审核',
            };
        }
        if (canScore) {
            responseData.judgeData = {
                artworksList: '待评审作品',
                scoringHistory: '评分历史',
                judgingGuidelines: '评审指南',
            };
        }
        if (canViewScores) {
            responseData.scoresData = {
                allScores: '所有评分',
                statistics: '统计数据',
                rankings: '排名信息',
            };
        }
        if (canExport) {
            responseData.exportOptions = {
                formats: ['Excel', 'PDF', 'CSV'],
                dataTypes: ['作品', '评分', '成员'],
            };
        }
        responseData.basicData = {
            projectInfo: '项目基本信息',
            publicArtworks: '公开作品',
            announcements: '公告信息',
        };
        return this.success(responseData, '条件数据获取成功', req);
    }
};
exports.MultiRoleExamplesController = MultiRoleExamplesController;
__decorate([
    (0, auth_decorator_1.RequiredAuth)(),
    (0, common_1.Get)('my-roles'),
    (0, swagger_1.ApiOperation)({ summary: '查看我的所有角色' }),
    __param(0, (0, user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], MultiRoleExamplesController.prototype, "getMyRoles", null);
__decorate([
    (0, project_auth_decorator_1.ProjectOwner)(),
    (0, common_1.Get)('projects/:projectId/owner-dashboard'),
    (0, swagger_1.ApiOperation)({ summary: '项目所有者仪表板' }),
    __param(0, (0, project_auth_decorator_1.ProjectId)()),
    __param(1, (0, user_decorator_1.CurrentUser)()),
    __param(2, (0, project_auth_decorator_1.CurrentProjectRole)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, user_entity_1.User, Array, Object]),
    __metadata("design:returntype", Promise)
], MultiRoleExamplesController.prototype, "getOwnerDashboard", null);
__decorate([
    (0, project_auth_decorator_1.ProjectManager)(),
    (0, common_1.Get)('projects/:projectId/management'),
    (0, swagger_1.ApiOperation)({ summary: '项目管理功能' }),
    __param(0, (0, project_auth_decorator_1.ProjectId)()),
    __param(1, (0, user_decorator_1.CurrentUser)()),
    __param(2, (0, project_auth_decorator_1.IsProjectOwner)()),
    __param(3, (0, project_auth_decorator_1.IsProjectManager)()),
    __param(4, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, user_entity_1.User, Boolean, Boolean, Object]),
    __metadata("design:returntype", Promise)
], MultiRoleExamplesController.prototype, "getProjectManagement", null);
__decorate([
    (0, project_auth_decorator_1.ProjectJudge)(),
    (0, common_1.Get)('projects/:projectId/judging'),
    (0, swagger_1.ApiOperation)({ summary: '评委评审功能' }),
    __param(0, (0, project_auth_decorator_1.ProjectId)()),
    __param(1, (0, user_decorator_1.CurrentUser)()),
    __param(2, (0, project_auth_decorator_1.HasProjectPermission)('score')),
    __param(3, (0, project_auth_decorator_1.HasProjectPermission)('view_scores')),
    __param(4, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, user_entity_1.User, Boolean, Boolean, Object]),
    __metadata("design:returntype", Promise)
], MultiRoleExamplesController.prototype, "getJudgingPanel", null);
__decorate([
    (0, project_auth_decorator_1.ProjectMember)(),
    (0, common_1.Get)('projects/:projectId/member-info'),
    (0, swagger_1.ApiOperation)({ summary: '项目成员信息' }),
    __param(0, (0, project_auth_decorator_1.ProjectId)()),
    __param(1, (0, user_decorator_1.CurrentUser)()),
    __param(2, (0, project_auth_decorator_1.CurrentProjectRole)()),
    __param(3, (0, project_auth_decorator_1.CurrentProjectPermissions)()),
    __param(4, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, user_entity_1.User, Array, Object, Object]),
    __metadata("design:returntype", Promise)
], MultiRoleExamplesController.prototype, "getMemberInfo", null);
__decorate([
    (0, project_auth_decorator_1.ProjectPermission)('export'),
    (0, common_1.Get)('projects/:projectId/export-data'),
    (0, swagger_1.ApiOperation)({ summary: '导出项目数据' }),
    __param(0, (0, project_auth_decorator_1.ProjectId)()),
    __param(1, (0, user_decorator_1.CurrentUser)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], MultiRoleExamplesController.prototype, "exportProjectData", null);
__decorate([
    (0, project_auth_decorator_1.ProjectPermission)('invite'),
    (0, common_1.Post)('projects/:projectId/invite'),
    (0, swagger_1.ApiOperation)({ summary: '邀请用户加入项目' }),
    __param(0, (0, project_auth_decorator_1.ProjectId)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, user_decorator_1.CurrentUser)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], MultiRoleExamplesController.prototype, "inviteUser", null);
__decorate([
    (0, auth_decorator_1.RequiredAuth)(),
    (0, common_1.Post)('projects/:projectId/accept-invitation'),
    (0, swagger_1.ApiOperation)({ summary: '接受项目邀请' }),
    __param(0, (0, project_auth_decorator_1.ProjectId)()),
    __param(1, (0, user_decorator_1.UserId)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], MultiRoleExamplesController.prototype, "acceptInvitation", null);
__decorate([
    (0, project_auth_decorator_1.ProjectManager)(),
    (0, common_1.Patch)('projects/:projectId/members/:userId/permissions'),
    (0, swagger_1.ApiOperation)({ summary: '更新成员权限' }),
    __param(0, (0, project_auth_decorator_1.ProjectId)()),
    __param(1, (0, common_1.Param)('userId')),
    __param(2, (0, common_1.Body)()),
    __param(3, (0, user_decorator_1.CurrentUser)()),
    __param(4, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object, user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], MultiRoleExamplesController.prototype, "updateMemberPermissions", null);
__decorate([
    (0, project_auth_decorator_1.ProjectMember)(),
    (0, common_1.Get)('projects/:projectId/members'),
    (0, swagger_1.ApiOperation)({ summary: '获取项目成员列表' }),
    __param(0, (0, project_auth_decorator_1.ProjectId)()),
    __param(1, (0, project_auth_decorator_1.HasProjectPermission)('view_scores')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Boolean, Object]),
    __metadata("design:returntype", Promise)
], MultiRoleExamplesController.prototype, "getProjectMembers", null);
__decorate([
    (0, project_auth_decorator_1.ProjectMember)(),
    (0, common_1.Get)('projects/:projectId/conditional-data'),
    (0, swagger_1.ApiOperation)({ summary: '根据权限返回不同数据' }),
    __param(0, (0, project_auth_decorator_1.ProjectId)()),
    __param(1, (0, user_decorator_1.CurrentUser)()),
    __param(2, (0, project_auth_decorator_1.IsProjectOwner)()),
    __param(3, (0, project_auth_decorator_1.IsProjectManager)()),
    __param(4, (0, project_auth_decorator_1.HasProjectPermission)('score')),
    __param(5, (0, project_auth_decorator_1.HasProjectPermission)('view_scores')),
    __param(6, (0, project_auth_decorator_1.HasProjectPermission)('export')),
    __param(7, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, user_entity_1.User, Boolean, Boolean, Boolean, Boolean, Boolean, Object]),
    __metadata("design:returntype", Promise)
], MultiRoleExamplesController.prototype, "getConditionalData", null);
exports.MultiRoleExamplesController = MultiRoleExamplesController = __decorate([
    (0, swagger_1.ApiTags)('多角色权限示例'),
    (0, common_1.Controller)('examples/multi-role'),
    __metadata("design:paramtypes", [permission_service_1.PermissionService])
], MultiRoleExamplesController);
//# sourceMappingURL=multi-role-examples.controller.js.map