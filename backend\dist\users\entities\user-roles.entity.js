"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleApplication = exports.PermissionTemplate = exports.UserProjectRole = exports.UserGlobalRole = exports.ProjectRole = exports.GlobalRole = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("./user.entity");
var GlobalRole;
(function (GlobalRole) {
    GlobalRole["SUPER_ADMIN"] = "super_admin";
    GlobalRole["ADMIN"] = "admin";
    GlobalRole["ORGANIZER"] = "organizer";
    GlobalRole["ARTIST"] = "artist";
    GlobalRole["JUDGE"] = "judge";
    GlobalRole["USER"] = "user";
})(GlobalRole || (exports.GlobalRole = GlobalRole = {}));
var ProjectRole;
(function (ProjectRole) {
    ProjectRole["PROJECT_OWNER"] = "project_owner";
    ProjectRole["PROJECT_ADMIN"] = "project_admin";
    ProjectRole["PROJECT_JUDGE"] = "project_judge";
    ProjectRole["PROJECT_ARTIST"] = "project_artist";
    ProjectRole["PROJECT_VIEWER"] = "project_viewer";
    ProjectRole["PROJECT_MODERATOR"] = "project_moderator";
    ProjectRole["PROJECT_ASSISTANT"] = "project_assistant";
})(ProjectRole || (exports.ProjectRole = ProjectRole = {}));
let UserGlobalRole = class UserGlobalRole {
    id;
    userId;
    user;
    role;
    metadata;
    isActive;
    createdAt;
    updatedAt;
};
exports.UserGlobalRole = UserGlobalRole;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], UserGlobalRole.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id' }),
    __metadata("design:type", String)
], UserGlobalRole.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, user => user.globalRoles, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", user_entity_1.User)
], UserGlobalRole.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: GlobalRole }),
    __metadata("design:type", String)
], UserGlobalRole.prototype, "role", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], UserGlobalRole.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: true }),
    __metadata("design:type", Boolean)
], UserGlobalRole.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], UserGlobalRole.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], UserGlobalRole.prototype, "updatedAt", void 0);
exports.UserGlobalRole = UserGlobalRole = __decorate([
    (0, typeorm_1.Entity)('user_global_roles'),
    (0, typeorm_1.Index)(['userId', 'role'], { unique: true })
], UserGlobalRole);
let UserProjectRole = class UserProjectRole {
    id;
    userId;
    user;
    projectId;
    role;
    permissions;
    metadata;
    status;
    expiresAt;
    createdAt;
    updatedAt;
};
exports.UserProjectRole = UserProjectRole;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], UserProjectRole.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id' }),
    __metadata("design:type", String)
], UserProjectRole.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", user_entity_1.User)
], UserProjectRole.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'project_id' }),
    __metadata("design:type", String)
], UserProjectRole.prototype, "projectId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: ProjectRole }),
    __metadata("design:type", String)
], UserProjectRole.prototype, "role", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], UserProjectRole.prototype, "permissions", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], UserProjectRole.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: ['pending', 'accepted', 'declined', 'removed'], default: 'pending' }),
    __metadata("design:type", String)
], UserProjectRole.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], UserProjectRole.prototype, "expiresAt", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], UserProjectRole.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], UserProjectRole.prototype, "updatedAt", void 0);
exports.UserProjectRole = UserProjectRole = __decorate([
    (0, typeorm_1.Entity)('user_project_roles'),
    (0, typeorm_1.Index)(['userId', 'projectId', 'role'], { unique: true })
], UserProjectRole);
let PermissionTemplate = class PermissionTemplate {
    id;
    name;
    role;
    permissions;
    description;
    isDefault;
    isActive;
    createdAt;
    updatedAt;
};
exports.PermissionTemplate = PermissionTemplate;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], PermissionTemplate.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], PermissionTemplate.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: ProjectRole }),
    __metadata("design:type", String)
], PermissionTemplate.prototype, "role", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Object)
], PermissionTemplate.prototype, "permissions", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], PermissionTemplate.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: true }),
    __metadata("design:type", Boolean)
], PermissionTemplate.prototype, "isDefault", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: true }),
    __metadata("design:type", Boolean)
], PermissionTemplate.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], PermissionTemplate.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], PermissionTemplate.prototype, "updatedAt", void 0);
exports.PermissionTemplate = PermissionTemplate = __decorate([
    (0, typeorm_1.Entity)('permission_templates')
], PermissionTemplate);
let RoleApplication = class RoleApplication {
    id;
    userId;
    user;
    targetRole;
    applicationData;
    status;
    reviewComment;
    reviewerId;
    reviewer;
    appliedAt;
    reviewedAt;
};
exports.RoleApplication = RoleApplication;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], RoleApplication.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id' }),
    __metadata("design:type", String)
], RoleApplication.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", user_entity_1.User)
], RoleApplication.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: GlobalRole }),
    __metadata("design:type", String)
], RoleApplication.prototype, "targetRole", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Object)
], RoleApplication.prototype, "applicationData", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: ['pending', 'reviewing', 'approved', 'rejected'], default: 'pending' }),
    __metadata("design:type", String)
], RoleApplication.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], RoleApplication.prototype, "reviewComment", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'reviewer_id', nullable: true }),
    __metadata("design:type", String)
], RoleApplication.prototype, "reviewerId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'reviewer_id' }),
    __metadata("design:type", user_entity_1.User)
], RoleApplication.prototype, "reviewer", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], RoleApplication.prototype, "appliedAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], RoleApplication.prototype, "reviewedAt", void 0);
exports.RoleApplication = RoleApplication = __decorate([
    (0, typeorm_1.Entity)('role_applications')
], RoleApplication);
//# sourceMappingURL=user-roles.entity.js.map