import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { User } from '../users/entities/user.entity';
import { Project } from '../projects/entities/project.entity';
import { OrganizerApplication } from '../applications/entities/organizer-application.entity';

export const databaseConfig: TypeOrmModuleOptions = {
  type: 'mysql',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  username: process.env.DB_USERNAME || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_DATABASE || 'art_score',
  entities: [
    User,
    Project,
    OrganizerApplication,
    // 其他实体将在后续添加
  ],
  synchronize: process.env.NODE_ENV !== 'production', // 生产环境中应该设为false
  logging: process.env.NODE_ENV === 'development',
  timezone: '+08:00',
};
