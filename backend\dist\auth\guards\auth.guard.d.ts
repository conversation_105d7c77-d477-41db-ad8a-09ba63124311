import { CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { UsersService } from '../../users/users.service';
export declare abstract class BaseAuthGuard implements CanActivate {
    protected jwtService: JwtService;
    protected usersService: UsersService;
    protected reflector: Reflector;
    constructor(jwtService: JwtService, usersService: UsersService, reflector: Reflector);
    protected extractTokenFromHeader(request: Request): string | undefined;
    protected validateToken(token: string): Promise<any>;
    abstract canActivate(context: ExecutionContext): boolean | Promise<boolean>;
}
export declare class NoAuthGuard implements CanActivate {
    canActivate(context: ExecutionContext): boolean;
}
export declare class RequiredAuthGuard extends BaseAuthGuard {
    canActivate(context: ExecutionContext): Promise<boolean>;
}
export declare class OptionalAuthGuard extends BaseAuthGuard {
    canActivate(context: ExecutionContext): Promise<boolean>;
}
export declare class JwtAuthGuard extends RequiredAuthGuard {
    constructor(jwtService: JwtService, usersService: UsersService, reflector: Reflector);
}
export declare class SmartAuthGuard extends BaseAuthGuard {
    canActivate(context: ExecutionContext): Promise<boolean>;
}
