import {getStreamContents} from './contents.js';
import {identity, noop, getContentsProp} from './utils.js';

export async function getStreamAsArray(stream, options) {
	return getStreamContents(stream, arrayMethods, options);
}

const initArray = () => ({contents: []});

const increment = () => 1;

const addArrayChunk = (convertedChunk, {contents}) => {
	contents.push(convertedChunk);
	return contents;
};

const arrayMethods = {
	init: initArray,
	convertChunk: {
		string: identity,
		buffer: identity,
		arrayBuffer: identity,
		dataView: identity,
		typedArray: identity,
		others: identity,
	},
	getSize: increment,
	truncateChunk: noop,
	addChunk: addArrayChunk,
	getFinalChunk: noop,
	finalize: getContentsProp,
};
