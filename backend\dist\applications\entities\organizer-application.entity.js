"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizerApplication = exports.ApplicationStatus = exports.OrganizationType = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../users/entities/user.entity");
var OrganizationType;
(function (OrganizationType) {
    OrganizationType["GOVERNMENT"] = "government";
    OrganizationType["ASSOCIATION"] = "association";
    OrganizationType["COMPANY"] = "company";
    OrganizationType["SCHOOL"] = "school";
    OrganizationType["INDIVIDUAL"] = "individual";
})(OrganizationType || (exports.OrganizationType = OrganizationType = {}));
var ApplicationStatus;
(function (ApplicationStatus) {
    ApplicationStatus["PENDING"] = "pending";
    ApplicationStatus["REVIEWING"] = "reviewing";
    ApplicationStatus["APPROVED"] = "approved";
    ApplicationStatus["REJECTED"] = "rejected";
})(ApplicationStatus || (exports.ApplicationStatus = ApplicationStatus = {}));
let OrganizerApplication = class OrganizerApplication {
    id;
    userId;
    organizationName;
    organizationType;
    legalPerson;
    contactPerson;
    contactPhone;
    contactEmail;
    address;
    businessLicense;
    qualificationDocs;
    introduction;
    experience;
    applicationReason;
    status;
    reviewerId;
    reviewComment;
    appliedAt;
    reviewedAt;
    createdAt;
    updatedAt;
    user;
    reviewer;
};
exports.OrganizerApplication = OrganizerApplication;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], OrganizerApplication.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id' }),
    __metadata("design:type", String)
], OrganizerApplication.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'organization_name', length: 200 }),
    __metadata("design:type", String)
], OrganizerApplication.prototype, "organizationName", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'organization_type',
        type: 'enum',
        enum: OrganizationType,
    }),
    __metadata("design:type", String)
], OrganizerApplication.prototype, "organizationType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'legal_person', length: 50, nullable: true }),
    __metadata("design:type", String)
], OrganizerApplication.prototype, "legalPerson", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'contact_person', length: 50 }),
    __metadata("design:type", String)
], OrganizerApplication.prototype, "contactPerson", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'contact_phone', length: 20 }),
    __metadata("design:type", String)
], OrganizerApplication.prototype, "contactPhone", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'contact_email', length: 100, nullable: true }),
    __metadata("design:type", String)
], OrganizerApplication.prototype, "contactEmail", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], OrganizerApplication.prototype, "address", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'business_license', nullable: true }),
    __metadata("design:type", String)
], OrganizerApplication.prototype, "businessLicense", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'qualification_docs', type: 'json', nullable: true }),
    __metadata("design:type", Array)
], OrganizerApplication.prototype, "qualificationDocs", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], OrganizerApplication.prototype, "introduction", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], OrganizerApplication.prototype, "experience", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'application_reason', type: 'text', nullable: true }),
    __metadata("design:type", String)
], OrganizerApplication.prototype, "applicationReason", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ApplicationStatus,
        default: ApplicationStatus.PENDING,
    }),
    __metadata("design:type", String)
], OrganizerApplication.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'reviewer_id', nullable: true }),
    __metadata("design:type", String)
], OrganizerApplication.prototype, "reviewerId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'review_comment', type: 'text', nullable: true }),
    __metadata("design:type", String)
], OrganizerApplication.prototype, "reviewComment", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'applied_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' }),
    __metadata("design:type", Date)
], OrganizerApplication.prototype, "appliedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'reviewed_at', type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], OrganizerApplication.prototype, "reviewedAt", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], OrganizerApplication.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], OrganizerApplication.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", user_entity_1.User)
], OrganizerApplication.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'reviewer_id' }),
    __metadata("design:type", user_entity_1.User)
], OrganizerApplication.prototype, "reviewer", void 0);
exports.OrganizerApplication = OrganizerApplication = __decorate([
    (0, typeorm_1.Entity)('organizer_applications')
], OrganizerApplication);
//# sourceMappingURL=organizer-application.entity.js.map