import { Repository } from 'typeorm';
import { CreateApplicationDto } from './dto/create-application.dto';
import { UpdateApplicationDto } from './dto/update-application.dto';
import { ReviewApplicationDto } from './dto/review-application.dto';
import { OrganizerApplication, ApplicationStatus } from './entities/organizer-application.entity';
import { User } from '../users/entities/user.entity';
import { UsersService } from '../users/users.service';
export declare class ApplicationsService {
    private applicationRepository;
    private usersService;
    constructor(applicationRepository: Repository<OrganizerApplication>, usersService: UsersService);
    create(createApplicationDto: CreateApplicationDto, userId: string): Promise<OrganizerApplication>;
    findAll(options: {
        page: number;
        limit: number;
        status?: ApplicationStatus;
        organizationType?: string;
    }): Promise<{
        data: any;
        total: any;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    findByUser(userId: string): Promise<OrganizerApplication[]>;
    findOne(id: string, user: User): Promise<OrganizerApplication>;
    update(id: string, updateApplicationDto: UpdateApplicationDto, user: User): Promise<OrganizerApplication>;
    review(id: string, reviewApplicationDto: ReviewApplicationDto, reviewerId: string): Promise<OrganizerApplication>;
    remove(id: string, user: User): Promise<void>;
    getApplicationStats(): Promise<{
        total: any;
        byStatus: any;
        recent: any;
    }>;
}
