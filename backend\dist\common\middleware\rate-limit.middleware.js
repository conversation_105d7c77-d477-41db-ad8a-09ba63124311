"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityMiddleware = exports.RequestIdMiddleware = exports.CorsMiddleware = exports.RateLimitMiddleware = void 0;
const common_1 = require("@nestjs/common");
let RateLimitMiddleware = class RateLimitMiddleware {
    requests = new Map();
    limits = {
        anonymous: { requests: 100, window: 60 * 1000 },
        user: { requests: 200, window: 60 * 1000 },
        judge: { requests: 300, window: 60 * 1000 },
        organizer: { requests: 500, window: 60 * 1000 },
        admin: { requests: 1000, window: 60 * 1000 },
        super_admin: { requests: 2000, window: 60 * 1000 },
    };
    use(req, res, next) {
        const key = this.getKey(req);
        const limit = this.getLimit(req);
        const now = Date.now();
        let record = this.requests.get(key);
        if (!record || now > record.resetTime) {
            record = {
                count: 1,
                resetTime: now + limit.window,
            };
            this.requests.set(key, record);
        }
        else {
            record.count++;
        }
        res.setHeader('X-RateLimit-Limit', limit.requests);
        res.setHeader('X-RateLimit-Remaining', Math.max(0, limit.requests - record.count));
        res.setHeader('X-RateLimit-Reset', Math.ceil(record.resetTime / 1000));
        if (record.count > limit.requests) {
            res.status(429).json({
                success: false,
                code: 429,
                message: '请求频率过高，请稍后再试',
                error: {
                    type: 'RATE_LIMIT_ERROR',
                    details: {
                        limit: limit.requests,
                        window: limit.window / 1000,
                        retryAfter: Math.ceil((record.resetTime - now) / 1000),
                    },
                },
                timestamp: Date.now(),
                requestId: req.headers['x-request-id'] || 'unknown',
            });
            return;
        }
        next();
    }
    getKey(req) {
        const user = req.user;
        const ip = req.ip || req.connection.remoteAddress || 'unknown';
        if (user) {
            return `user:${user.id}`;
        }
        return `ip:${ip}`;
    }
    getLimit(req) {
        const user = req.user;
        if (!user) {
            return this.limits.anonymous;
        }
        return this.limits[user.role] || this.limits.user;
    }
    cleanup() {
        const now = Date.now();
        for (const [key, record] of this.requests.entries()) {
            if (now > record.resetTime) {
                this.requests.delete(key);
            }
        }
    }
};
exports.RateLimitMiddleware = RateLimitMiddleware;
exports.RateLimitMiddleware = RateLimitMiddleware = __decorate([
    (0, common_1.Injectable)()
], RateLimitMiddleware);
let CorsMiddleware = class CorsMiddleware {
    use(req, res, next) {
        const origin = req.headers.origin;
        const allowedOrigins = [
            'http://localhost:3000',
            'http://localhost:8080',
            'https://admin.artscore.com',
            'https://org.artscore.com',
            'https://mp.artscore.com',
        ];
        if (allowedOrigins.includes(origin)) {
            res.setHeader('Access-Control-Allow-Origin', origin);
        }
        res.setHeader('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', [
            'Origin',
            'X-Requested-With',
            'Content-Type',
            'Accept',
            'Authorization',
            'X-Request-ID',
            'X-Client-Version',
            'X-Platform',
        ].join(','));
        res.setHeader('Access-Control-Allow-Credentials', 'true');
        res.setHeader('Access-Control-Max-Age', '86400');
        if (req.method === 'OPTIONS') {
            res.status(200).end();
            return;
        }
        next();
    }
};
exports.CorsMiddleware = CorsMiddleware;
exports.CorsMiddleware = CorsMiddleware = __decorate([
    (0, common_1.Injectable)()
], CorsMiddleware);
let RequestIdMiddleware = class RequestIdMiddleware {
    use(req, res, next) {
        const requestId = req.headers['x-request-id'] || this.generateId();
        req.headers['x-request-id'] = requestId;
        res.setHeader('X-Request-ID', requestId);
        next();
    }
    generateId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
};
exports.RequestIdMiddleware = RequestIdMiddleware;
exports.RequestIdMiddleware = RequestIdMiddleware = __decorate([
    (0, common_1.Injectable)()
], RequestIdMiddleware);
let SecurityMiddleware = class SecurityMiddleware {
    use(req, res, next) {
        const userAgent = req.headers['user-agent'];
        if (!userAgent || userAgent.length < 10) {
            res.status(400).json({
                success: false,
                code: 400,
                message: '无效的请求',
                error: {
                    type: 'SECURITY_ERROR',
                    details: 'Invalid User-Agent',
                },
                timestamp: Date.now(),
                requestId: req.headers['x-request-id'] || 'unknown',
            });
            return;
        }
        const contentLength = parseInt(req.headers['content-length'] || '0');
        const maxSize = 50 * 1024 * 1024;
        if (contentLength > maxSize) {
            res.status(413).json({
                success: false,
                code: 413,
                message: '请求体过大',
                error: {
                    type: 'SECURITY_ERROR',
                    details: `Content-Length exceeds ${maxSize} bytes`,
                },
                timestamp: Date.now(),
                requestId: req.headers['x-request-id'] || 'unknown',
            });
            return;
        }
        const suspiciousHeaders = ['x-forwarded-for', 'x-real-ip'];
        for (const header of suspiciousHeaders) {
            const value = req.headers[header];
            if (value && typeof value === 'string' && value.includes('<script>')) {
                res.status(400).json({
                    success: false,
                    code: 400,
                    message: '无效的请求头',
                    error: {
                        type: 'SECURITY_ERROR',
                        details: 'Suspicious header detected',
                    },
                    timestamp: Date.now(),
                    requestId: req.headers['x-request-id'] || 'unknown',
                });
                return;
            }
        }
        next();
    }
};
exports.SecurityMiddleware = SecurityMiddleware;
exports.SecurityMiddleware = SecurityMiddleware = __decorate([
    (0, common_1.Injectable)()
], SecurityMiddleware);
//# sourceMappingURL=rate-limit.middleware.js.map