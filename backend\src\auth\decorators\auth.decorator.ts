import { SetMetadata, UseGuards, applyDecorators } from '@nestjs/common';
import { ApiBearerAuth, ApiUnauthorizedResponse } from '@nestjs/swagger';
import { 
  NoAuthGuard, 
  RequiredAuthGuard, 
  OptionalAuthGuard, 
  SmartAuthGuard 
} from '../guards/auth.guard';
import { RolesGuard } from '../guards/roles.guard';
import { UserRole } from '../../users/entities/user.entity';

/**
 * 公开接口装饰器
 * 标记接口为公开，不需要任何认证
 */
export const Public = () => SetMetadata('isPublic', true);

/**
 * 可选认证装饰器
 * 标记接口为可选认证，有Token时会解析用户信息
 */
export const OptionalAuth = () => SetMetadata('isOptionalAuth', true);

/**
 * 不需要认证装饰器
 * 用于完全公开的接口
 * 
 * @example
 * ```typescript
 * @NoAuth()
 * @Get('public-info')
 * async getPublicInfo() {
 *   return { message: '这是公开信息' };
 * }
 * ```
 */
export const NoAuth = () => {
  return applyDecorators(
    UseGuards(NoAuthGuard),
    Public(),
  );
};

/**
 * 必须认证装饰器
 * 用于需要登录才能访问的接口
 * 
 * @param roles - 可选的角色限制
 * 
 * @example
 * ```typescript
 * @RequiredAuth()
 * @Get('profile')
 * async getProfile(@Request() req) {
 *   return req.user; // 可以获取到用户信息
 * }
 * 
 * @RequiredAuth([UserRole.ADMIN])
 * @Get('admin-only')
 * async adminOnly(@Request() req) {
 *   return { message: '只有管理员可以访问' };
 * }
 * ```
 */
export const RequiredAuth = (roles?: UserRole[]) => {
  const decorators = [
    UseGuards(RequiredAuthGuard),
    ApiBearerAuth(),
    ApiUnauthorizedResponse({ description: '未授权访问' }),
  ];

  if (roles && roles.length > 0) {
    decorators.push(
      UseGuards(RolesGuard),
      SetMetadata('roles', roles),
    );
  }

  return applyDecorators(...decorators);
};

/**
 * 可选认证装饰器
 * 用于可以登录也可以不登录访问的接口
 * 如果有有效Token，会在req.user中提供用户信息
 * 如果没有Token或Token无效，req.user为null
 * 
 * @example
 * ```typescript
 * @OptionalAuth()
 * @Get('articles')
 * async getArticles(@Request() req) {
 *   const user = req.user; // 可能为null或用户对象
 *   if (user) {
 *     // 已登录用户的逻辑
 *     return this.getPersonalizedArticles(user.id);
 *   } else {
 *     // 未登录用户的逻辑
 *     return this.getPublicArticles();
 *   }
 * }
 * ```
 */
export const OptionalAuth = () => {
  return applyDecorators(
    UseGuards(OptionalAuthGuard),
    OptionalAuth(),
    ApiBearerAuth(),
  );
};

/**
 * 智能认证装饰器
 * 根据其他装饰器自动选择认证策略
 * 
 * @example
 * ```typescript
 * @SmartAuth()
 * @Public()
 * @Get('public')
 * async publicEndpoint() {
 *   // 自动识别为公开接口
 * }
 * 
 * @SmartAuth()
 * @OptionalAuth()
 * @Get('optional')
 * async optionalEndpoint(@Request() req) {
 *   // 自动识别为可选认证
 * }
 * 
 * @SmartAuth()
 * @Get('protected')
 * async protectedEndpoint(@Request() req) {
 *   // 自动识别为必须认证
 * }
 * ```
 */
export const SmartAuth = () => {
  return applyDecorators(
    UseGuards(SmartAuthGuard),
    ApiBearerAuth(),
  );
};

/**
 * 角色认证装饰器
 * 必须认证且限制特定角色
 * 
 * @param roles - 允许的角色列表
 * 
 * @example
 * ```typescript
 * @RoleAuth([UserRole.ADMIN, UserRole.SUPER_ADMIN])
 * @Get('admin-data')
 * async getAdminData(@Request() req) {
 *   return { message: '管理员数据' };
 * }
 * ```
 */
export const RoleAuth = (roles: UserRole[]) => {
  return applyDecorators(
    UseGuards(RequiredAuthGuard, RolesGuard),
    SetMetadata('roles', roles),
    ApiBearerAuth(),
    ApiUnauthorizedResponse({ description: '未授权访问' }),
  );
};

/**
 * 管理员认证装饰器
 * 只允许管理员和超级管理员访问
 * 
 * @example
 * ```typescript
 * @AdminAuth()
 * @Get('admin-panel')
 * async adminPanel(@Request() req) {
 *   return { message: '管理员面板' };
 * }
 * ```
 */
export const AdminAuth = () => {
  return RoleAuth([UserRole.ADMIN, UserRole.SUPER_ADMIN]);
};

/**
 * 超级管理员认证装饰器
 * 只允许超级管理员访问
 * 
 * @example
 * ```typescript
 * @SuperAdminAuth()
 * @Delete('dangerous-operation')
 * async dangerousOperation(@Request() req) {
 *   return { message: '危险操作完成' };
 * }
 * ```
 */
export const SuperAdminAuth = () => {
  return RoleAuth([UserRole.SUPER_ADMIN]);
};

/**
 * 办展方认证装饰器
 * 允许办展方、管理员和超级管理员访问
 * 
 * @example
 * ```typescript
 * @OrganizerAuth()
 * @Post('projects')
 * async createProject(@Request() req) {
 *   return { message: '项目创建成功' };
 * }
 * ```
 */
export const OrganizerAuth = () => {
  return RoleAuth([UserRole.ORGANIZER, UserRole.ADMIN, UserRole.SUPER_ADMIN]);
};

/**
 * 评委认证装饰器
 * 允许评委、管理员和超级管理员访问
 * 
 * @example
 * ```typescript
 * @JudgeAuth()
 * @Post('scores')
 * async createScore(@Request() req) {
 *   return { message: '评分提交成功' };
 * }
 * ```
 */
export const JudgeAuth = () => {
  return RoleAuth([UserRole.JUDGE, UserRole.ADMIN, UserRole.SUPER_ADMIN]);
};
