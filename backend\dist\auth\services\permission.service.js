"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PermissionService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("../../users/entities/user.entity");
const user_roles_entity_1 = require("../../users/entities/user-roles.entity");
let PermissionService = class PermissionService {
    userRepository;
    globalRoleRepository;
    projectRoleRepository;
    constructor(userRepository, globalRoleRepository, projectRoleRepository) {
        this.userRepository = userRepository;
        this.globalRoleRepository = globalRoleRepository;
        this.projectRoleRepository = projectRoleRepository;
    }
    async hasGlobalPermission(userId, resource, action) {
        const user = await this.userRepository.findOne({
            where: { id: userId },
            relations: ['globalRoles']
        });
        if (!user)
            return false;
        if (user.hasGlobalRole(user_roles_entity_1.GlobalRole.SUPER_ADMIN))
            return true;
        const userRoles = user.roles;
        return this.checkRolePermissions(userRoles, resource, action);
    }
    async hasProjectPermission(userId, projectId, permission) {
        const hasGlobal = await this.hasGlobalPermission(userId, 'project', 'manage');
        if (hasGlobal)
            return true;
        const projectRole = await this.projectRoleRepository.findOne({
            where: {
                userId,
                projectId,
                status: 'accepted'
            }
        });
        if (!projectRole)
            return false;
        if (projectRole.expiresAt && new Date() > projectRole.expiresAt) {
            return false;
        }
        return this.checkProjectPermission(projectRole, permission);
    }
    async addGlobalRole(userId, role, grantedBy, metadata) {
        const existing = await this.globalRoleRepository.findOne({
            where: { userId, role }
        });
        if (existing) {
            existing.isActive = true;
            existing.metadata = { ...existing.metadata, ...metadata, grantedBy, grantedAt: new Date() };
            return await this.globalRoleRepository.save(existing);
        }
        const globalRole = this.globalRoleRepository.create({
            userId,
            role,
            metadata: {
                grantedBy,
                grantedAt: new Date(),
                ...metadata
            }
        });
        return await this.globalRoleRepository.save(globalRole);
    }
    async removeGlobalRole(userId, role) {
        await this.globalRoleRepository.update({ userId, role }, { isActive: false });
    }
    async inviteUserToProject(projectId, userId, role, permissions, invitedBy, metadata) {
        const existing = await this.projectRoleRepository.findOne({
            where: { userId, projectId, role }
        });
        if (existing) {
            existing.permissions = permissions;
            existing.status = 'pending';
            existing.metadata = { ...existing.metadata, ...metadata, invitedBy, invitedAt: new Date() };
            return await this.projectRoleRepository.save(existing);
        }
        const projectRole = this.projectRoleRepository.create({
            userId,
            projectId,
            role,
            permissions,
            metadata: {
                invitedBy,
                invitedAt: new Date(),
                ...metadata
            },
            status: 'pending'
        });
        return await this.projectRoleRepository.save(projectRole);
    }
    async acceptProjectInvitation(userId, projectId) {
        await this.projectRoleRepository.update({ userId, projectId, status: 'pending' }, {
            status: 'accepted',
            metadata: () => `JSON_SET(metadata, '$.acceptedAt', '${new Date().toISOString()}')`
        });
    }
    async updateProjectRolePermissions(userId, projectId, permissions) {
        await this.projectRoleRepository.update({ userId, projectId }, { permissions });
    }
    async getUserProjectRoles(userId, projectId) {
        return await this.projectRoleRepository.find({
            where: {
                userId,
                projectId,
                status: 'accepted'
            }
        });
    }
    async getProjectMembers(projectId) {
        return await this.projectRoleRepository.find({
            where: { projectId, status: 'accepted' },
            relations: ['user']
        });
    }
    checkRolePermissions(roles, resource, action) {
        const rolePermissions = {
            [user_roles_entity_1.GlobalRole.SUPER_ADMIN]: ['*:*'],
            [user_roles_entity_1.GlobalRole.ADMIN]: [
                'user:read', 'user:update', 'user:manage',
                'project:read', 'project:audit',
                'application:review', 'application:approve'
            ],
            [user_roles_entity_1.GlobalRole.ORGANIZER]: [
                'project:create', 'project:manage',
                'artwork:manage', 'user:invite'
            ],
            [user_roles_entity_1.GlobalRole.ARTIST]: [
                'artwork:create', 'artwork:update',
                'project:read', 'project:join'
            ],
            [user_roles_entity_1.GlobalRole.JUDGE]: [
                'score:create', 'score:update',
                'artwork:read', 'project:read'
            ],
            [user_roles_entity_1.GlobalRole.USER]: [
                'project:read', 'artwork:read'
            ]
        };
        for (const role of roles) {
            const permissions = rolePermissions[role] || [];
            for (const permission of permissions) {
                if (permission === '*:*')
                    return true;
                const [permResource, permAction] = permission.split(':');
                if ((permResource === resource || permResource === '*') &&
                    (permAction === action || permAction === '*')) {
                    return true;
                }
            }
        }
        return false;
    }
    checkProjectPermission(projectRole, permission) {
        const permissions = projectRole.permissions || {};
        if (projectRole.role === user_roles_entity_1.ProjectRole.PROJECT_OWNER)
            return true;
        switch (permission) {
            case 'manage':
                return permissions.canManageProject || projectRole.role === user_roles_entity_1.ProjectRole.PROJECT_ADMIN;
            case 'invite':
                return permissions.canInviteUsers;
            case 'artwork_manage':
                return permissions.canManageArtworks;
            case 'score':
                return permissions.canScore;
            case 'view_scores':
                return permissions.canViewScores;
            case 'export':
                return permissions.canExportData;
            case 'moderate':
                return permissions.canModerateContent;
            case 'schedule':
                return permissions.canManageSchedule;
            default:
                return permissions.customPermissions?.includes(permission) || false;
        }
    }
    getDefaultProjectPermissions(role) {
        const defaultPermissions = {
            [user_roles_entity_1.ProjectRole.PROJECT_OWNER]: {
                canManageProject: true,
                canInviteUsers: true,
                canManageArtworks: true,
                canScore: false,
                canViewScores: true,
                canExportData: true,
                canModerateContent: true,
                canManageSchedule: true
            },
            [user_roles_entity_1.ProjectRole.PROJECT_ADMIN]: {
                canManageProject: true,
                canInviteUsers: true,
                canManageArtworks: true,
                canScore: false,
                canViewScores: true,
                canExportData: true,
                canModerateContent: true,
                canManageSchedule: true
            },
            [user_roles_entity_1.ProjectRole.PROJECT_JUDGE]: {
                canManageProject: false,
                canInviteUsers: false,
                canManageArtworks: false,
                canScore: true,
                canViewScores: true,
                canExportData: false,
                canModerateContent: false,
                canManageSchedule: false
            },
            [user_roles_entity_1.ProjectRole.PROJECT_ARTIST]: {
                canManageProject: false,
                canInviteUsers: false,
                canManageArtworks: false,
                canScore: false,
                canViewScores: false,
                canExportData: false,
                canModerateContent: false,
                canManageSchedule: false
            },
            [user_roles_entity_1.ProjectRole.PROJECT_VIEWER]: {
                canManageProject: false,
                canInviteUsers: false,
                canManageArtworks: false,
                canScore: false,
                canViewScores: false,
                canExportData: false,
                canModerateContent: false,
                canManageSchedule: false
            },
            [user_roles_entity_1.ProjectRole.PROJECT_MODERATOR]: {
                canManageProject: false,
                canInviteUsers: false,
                canManageArtworks: true,
                canScore: false,
                canViewScores: true,
                canExportData: false,
                canModerateContent: true,
                canManageSchedule: false
            }
        };
        return defaultPermissions[role] || {};
    }
};
exports.PermissionService = PermissionService;
exports.PermissionService = PermissionService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(user_roles_entity_1.UserGlobalRole)),
    __param(2, (0, typeorm_1.InjectRepository)(user_roles_entity_1.UserProjectRole)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object])
], PermissionService);
//# sourceMappingURL=permission.service.js.map