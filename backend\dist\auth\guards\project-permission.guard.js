"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResourceOwnerGuard = exports.MultiRoleGuard = exports.ProjectPermissionGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const project_permission_service_1 = require("../services/project-permission.service");
let ProjectPermissionGuard = class ProjectPermissionGuard {
    reflector;
    projectPermissionService;
    constructor(reflector, projectPermissionService) {
        this.reflector = reflector;
        this.projectPermissionService = projectPermissionService;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user) {
            throw new common_1.ForbiddenException('用户未认证');
        }
        const projectId = this.extractProjectId(request);
        if (!projectId) {
            throw new common_1.NotFoundException('项目ID未找到');
        }
        const requiredPermission = this.reflector.get('project_permission', context.getHandler());
        const requiredRoles = this.reflector.get('project_roles', context.getHandler());
        const requireMember = this.reflector.get('project_member', context.getHandler());
        if (user.hasGlobalRole('super_admin') || user.hasGlobalRole('admin')) {
            await this.attachProjectInfo(request, user.id, projectId);
            return true;
        }
        const projectRoles = await this.projectPermissionService.getUserProjectRoles(user.id, projectId);
        if (projectRoles.length === 0) {
            throw new common_1.ForbiddenException('您不是该项目的成员');
        }
        await this.attachProjectInfo(request, user.id, projectId, projectRoles);
        if (requireMember && !requiredPermission && !requiredRoles) {
            return true;
        }
        if (requiredPermission) {
            const hasPermission = await this.projectPermissionService.hasProjectPermission(user.id, projectId, requiredPermission);
            if (!hasPermission) {
                throw new common_1.ForbiddenException(`缺少项目权限: ${requiredPermission}`);
            }
        }
        if (requiredRoles && requiredRoles.length > 0) {
            const userRoles = projectRoles.map(pr => pr.role);
            const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));
            if (!hasRequiredRole) {
                throw new common_1.ForbiddenException(`需要以下角色之一: ${requiredRoles.join(', ')}`);
            }
        }
        return true;
    }
    extractProjectId(request) {
        if (request.params.projectId) {
            return request.params.projectId;
        }
        if (request.params.id && request.route?.path?.includes('project')) {
            return request.params.id;
        }
        if (request.query.projectId) {
            return request.query.projectId;
        }
        if (request.body?.projectId) {
            return request.body.projectId;
        }
        return null;
    }
    async attachProjectInfo(request, userId, projectId, projectRoles) {
        if (!projectRoles) {
            projectRoles = await this.projectPermissionService.getUserProjectRoles(userId, projectId);
        }
        request.projectId = projectId;
        request.projectRoles = projectRoles.map(pr => pr.role);
        const mergedPermissions = this.projectPermissionService.mergePermissions(projectRoles);
        request.projectPermissions = mergedPermissions;
        request.projectRoleDetails = projectRoles;
    }
};
exports.ProjectPermissionGuard = ProjectPermissionGuard;
exports.ProjectPermissionGuard = ProjectPermissionGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector,
        project_permission_service_1.ProjectPermissionService])
], ProjectPermissionGuard);
let MultiRoleGuard = class MultiRoleGuard {
    reflector;
    constructor(reflector) {
        this.reflector = reflector;
    }
    canActivate(context) {
        const requiredRoles = this.reflector.get('multi_roles', context.getHandler());
        if (!requiredRoles) {
            return true;
        }
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user) {
            return false;
        }
        return requiredRoles.some(role => user.hasGlobalRole(role));
    }
};
exports.MultiRoleGuard = MultiRoleGuard;
exports.MultiRoleGuard = MultiRoleGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector])
], MultiRoleGuard);
let ResourceOwnerGuard = class ResourceOwnerGuard {
    reflector;
    permissionService;
    constructor(reflector, permissionService) {
        this.reflector = reflector;
        this.permissionService = permissionService;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user) {
            return false;
        }
        if (user.hasGlobalRole('super_admin')) {
            return true;
        }
        const resourceType = this.reflector.get('resource_type', context.getHandler());
        const resourceId = this.extractResourceId(request, resourceType);
        if (!resourceId) {
            throw new common_1.NotFoundException('资源ID未找到');
        }
        return await this.checkResourceOwnership(user.id, resourceType, resourceId);
    }
    extractResourceId(request, resourceType) {
        switch (resourceType) {
            case 'project':
                return request.params.projectId || request.params.id;
            case 'artwork':
                return request.params.artworkId || request.params.id;
            case 'score':
                return request.params.scoreId || request.params.id;
            default:
                return request.params.id;
        }
    }
    async checkResourceOwnership(userId, resourceType, resourceId) {
        return true;
    }
};
exports.ResourceOwnerGuard = ResourceOwnerGuard;
exports.ResourceOwnerGuard = ResourceOwnerGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector, typeof (_a = typeof PermissionService !== "undefined" && PermissionService) === "function" ? _a : Object])
], ResourceOwnerGuard);
//# sourceMappingURL=project-permission.guard.js.map