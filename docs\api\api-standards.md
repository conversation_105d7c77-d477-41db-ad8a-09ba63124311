# API 接口开发规范

## 1. 接口前缀规范

### 1.1 路由前缀定义
```typescript
// 基础路径
const BASE_PATH = '/api/v1';

// 各端路由前缀
const API_PREFIXES = {
  ADMIN: '/admin',      // 管理端
  ORGANIZER: '/org',    // 办展方端（商家端）
  MINIPROGRAM: '/mp',   // 小程序端
  COMMON: '/common'     // 公共接口
};
```

### 1.2 完整路由示例
```
管理端：   /api/v1/admin/users
办展方端： /api/v1/org/projects  
小程序端： /api/v1/mp/artworks
公共接口： /api/v1/common/auth
```

## 2. 统一响应格式

### 2.1 成功响应格式
```typescript
interface ApiResponse<T = any> {
  success: true;
  code: number;           // 业务状态码
  message: string;        // 响应消息
  data: T;               // 响应数据
  timestamp: number;      // 时间戳
  requestId: string;      // 请求ID（用于追踪）
}

// 示例
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "uuid",
    "name": "项目名称"
  },
  "timestamp": 1704067200000,
  "requestId": "req_123456789"
}
```

### 2.2 错误响应格式
```typescript
interface ApiErrorResponse {
  success: false;
  code: number;           // 错误状态码
  message: string;        // 错误消息
  error: {
    type: string;         // 错误类型
    details?: any;        // 错误详情
    field?: string;       // 字段错误（表单验证）
  };
  timestamp: number;
  requestId: string;
}

// 示例
{
  "success": false,
  "code": 400,
  "message": "参数验证失败",
  "error": {
    "type": "VALIDATION_ERROR",
    "details": {
      "title": "作品标题不能为空",
      "authorName": "作者姓名格式不正确"
    },
    "field": "title"
  },
  "timestamp": 1704067200000,
  "requestId": "req_123456789"
}
```

## 3. 分页响应格式

### 3.1 分页数据结构
```typescript
interface PaginatedResponse<T> {
  success: true;
  code: number;
  message: string;
  data: {
    items: T[];           // 数据列表
    pagination: {
      page: number;       // 当前页码（从1开始）
      limit: number;      // 每页数量
      total: number;      // 总记录数
      totalPages: number; // 总页数
      hasNext: boolean;   // 是否有下一页
      hasPrev: boolean;   // 是否有上一页
    };
  };
  timestamp: number;
  requestId: string;
}

// 示例
{
  "success": true,
  "code": 200,
  "message": "获取成功",
  "data": {
    "items": [
      {
        "id": "artwork_1",
        "title": "作品1"
      },
      {
        "id": "artwork_2", 
        "title": "作品2"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3,
      "hasNext": true,
      "hasPrev": false
    }
  },
  "timestamp": 1704067200000,
  "requestId": "req_123456789"
}
```

### 3.2 分页请求参数
```typescript
interface PaginationQuery {
  page?: number;          // 页码，默认1
  limit?: number;         // 每页数量，默认10，最大100
  sort?: string;          // 排序字段
  order?: 'ASC' | 'DESC'; // 排序方向，默认DESC
}

// URL示例
GET /api/v1/org/artworks?page=1&limit=20&sort=createdAt&order=DESC
```

## 4. 请求参数规范

### 4.1 查询参数（Query Parameters）
```typescript
// 通用查询参数
interface BaseQuery extends PaginationQuery {
  search?: string;        // 搜索关键词
  status?: string;        // 状态筛选
  startDate?: string;     // 开始日期 (ISO 8601)
  endDate?: string;       // 结束日期 (ISO 8601)
  fields?: string;        // 返回字段（逗号分隔）
}

// 示例
GET /api/v1/org/artworks?search=书法&status=approved&startDate=2024-01-01&fields=id,title,authorName
```

### 4.2 路径参数（Path Parameters）
```typescript
// 资源ID必须使用UUID格式
GET /api/v1/org/projects/{projectId}
PATCH /api/v1/org/artworks/{artworkId}/status
DELETE /api/v1/admin/users/{userId}
```

### 4.3 请求体参数（Request Body）
```typescript
// 创建资源
interface CreateArtworkRequest {
  projectId: string;      // 必填
  title: string;          // 必填，最大长度200
  description?: string;   // 可选
  authorName: string;     // 必填，最大长度50
  tags?: string[];        // 可选，最大10个标签
}

// 更新资源（部分更新）
interface UpdateArtworkRequest {
  title?: string;
  description?: string;
  tags?: string[];
}
```

## 5. HTTP状态码规范

### 5.1 成功状态码
```
200 OK          - 请求成功
201 Created     - 资源创建成功
204 No Content  - 删除成功（无返回内容）
```

### 5.2 客户端错误状态码
```
400 Bad Request         - 请求参数错误
401 Unauthorized        - 未认证
403 Forbidden          - 权限不足
404 Not Found          - 资源不存在
409 Conflict           - 资源冲突（如重复创建）
422 Unprocessable Entity - 参数验证失败
429 Too Many Requests   - 请求频率超限
```

### 5.3 服务器错误状态码
```
500 Internal Server Error - 服务器内部错误
502 Bad Gateway          - 网关错误
503 Service Unavailable  - 服务不可用
```

## 6. 业务状态码规范

### 6.1 状态码分类
```typescript
enum BusinessCode {
  // 成功 (200-299)
  SUCCESS = 200,
  CREATED = 201,
  
  // 客户端错误 (400-499)
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  VALIDATION_ERROR = 422,
  
  // 业务错误 (1000-9999)
  USER_NOT_FOUND = 1001,
  USER_DISABLED = 1002,
  PROJECT_NOT_FOUND = 2001,
  PROJECT_STATUS_ERROR = 2002,
  ARTWORK_NOT_FOUND = 3001,
  SCORE_DUPLICATE = 4001,
  SCORE_TIME_EXPIRED = 4002,
  
  // 服务器错误 (500-599)
  INTERNAL_ERROR = 500,
  DATABASE_ERROR = 501,
  EXTERNAL_SERVICE_ERROR = 502
}
```

## 7. 请求头规范

### 7.1 必需请求头
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer {token}  // 需要认证的接口
```

### 7.2 可选请求头
```
X-Request-ID: req_123456789   // 请求追踪ID
X-Client-Version: 1.0.0       // 客户端版本
X-Platform: ios|android|web   // 平台标识
Accept-Language: zh-CN        // 语言偏好
```

## 8. 响应头规范

### 8.1 标准响应头
```
Content-Type: application/json; charset=utf-8
X-Request-ID: req_123456789
X-Response-Time: 150ms
X-Rate-Limit-Remaining: 99
X-Rate-Limit-Reset: 1704067200
```

## 9. 文件上传规范

### 9.1 单文件上传
```typescript
POST /api/v1/common/files/upload
Content-Type: multipart/form-data

{
  file: File,           // 文件对象
  folder?: string,      // 存储文件夹
  compress?: boolean    // 是否压缩（图片）
}

// 响应
{
  "success": true,
  "code": 201,
  "message": "上传成功",
  "data": {
    "url": "/uploads/artworks/uuid.jpg",
    "originalName": "artwork.jpg",
    "size": 1024000,
    "mimeType": "image/jpeg",
    "hash": "md5_hash"
  }
}
```

### 9.2 批量文件上传
```typescript
POST /api/v1/common/files/batch-upload
Content-Type: multipart/form-data

{
  files: File[],        // 文件数组
  folder?: string
}
```

## 10. 错误处理规范

### 10.1 错误类型定义
```typescript
enum ErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  BUSINESS_ERROR = 'BUSINESS_ERROR',
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  EXTERNAL_ERROR = 'EXTERNAL_ERROR'
}
```

### 10.2 错误消息国际化
```typescript
// 错误消息支持多语言
const ERROR_MESSAGES = {
  'zh-CN': {
    USER_NOT_FOUND: '用户不存在',
    VALIDATION_ERROR: '参数验证失败'
  },
  'en-US': {
    USER_NOT_FOUND: 'User not found',
    VALIDATION_ERROR: 'Validation failed'
  }
};
```

## 11. 接口版本控制

### 11.1 版本号规范
```
/api/v1/...  // 当前版本
/api/v2/...  // 新版本
```

### 11.2 版本兼容性
- 向后兼容：新版本必须兼容旧版本
- 废弃通知：提前通知接口废弃计划
- 迁移指南：提供版本升级指南

## 12. 接口文档规范

### 12.1 接口描述格式
```typescript
/**
 * @api {POST} /api/v1/org/artworks 创建作品
 * @apiName CreateArtwork
 * @apiGroup Artwork
 * @apiVersion 1.0.0
 * @apiPermission organizer
 * 
 * @apiParam {String} projectId 项目ID
 * @apiParam {String} title 作品标题
 * @apiParam {String} authorName 作者姓名
 * 
 * @apiSuccess {Boolean} success 请求是否成功
 * @apiSuccess {Object} data 作品信息
 * @apiSuccess {String} data.id 作品ID
 * @apiSuccess {String} data.title 作品标题
 * 
 * @apiError {Boolean} success=false 请求失败
 * @apiError {String} message 错误信息
 */
```

## 13. 性能规范

### 13.1 响应时间要求
```
- 查询接口：< 200ms
- 创建接口：< 500ms
- 文件上传：< 2s
- 批量操作：< 5s
```

### 13.2 并发限制
```
- 普通用户：100 req/min
- VIP用户：500 req/min
- 管理员：1000 req/min
```

## 14. 安全规范

### 14.1 输入验证
- 所有输入参数必须验证
- 防止SQL注入和XSS攻击
- 文件上传类型和大小限制

### 14.2 敏感信息处理
- 密码等敏感信息不得出现在响应中
- 使用HTTPS传输
- 日志中不记录敏感信息

## 15. 后端控制器分离规范

### 15.1 控制器文件结构
```
src/controllers/
├── admin/                    # 管理端控制器
│   ├── admin-users.controller.ts
│   ├── admin-projects.controller.ts
│   ├── admin-applications.controller.ts
│   └── admin-stats.controller.ts
├── organizer/               # 办展方端控制器
│   ├── organizer-projects.controller.ts
│   ├── organizer-artworks.controller.ts
│   └── organizer-scores.controller.ts
├── miniprogram/             # 小程序端控制器
│   ├── mp-projects.controller.ts
│   ├── mp-artworks.controller.ts
│   └── mp-scores.controller.ts
├── common/                  # 公共控制器
│   ├── common-auth.controller.ts
│   └── common-files.controller.ts
└── controllers.module.ts    # 控制器模块
```

### 15.2 控制器命名规范
- 管理端：`Admin{Resource}Controller`
- 办展方端：`Organizer{Resource}Controller`
- 小程序端：`Mp{Resource}Controller`
- 公共接口：`Common{Resource}Controller`

### 15.3 基础控制器使用
```typescript
import { BaseController } from '../../common/base/base.controller';

@Controller('admin/users')
export class AdminUsersController extends BaseController {
  constructor(private readonly usersService: UsersService) {
    super();
  }

  @Get()
  async findAll(@Query() query: any, @Request() req) {
    const { page, limit } = this.parsePagination(query);
    const result = await this.usersService.findAll({ page, limit });

    return this.paginated(
      result.data,
      result.total,
      result.page,
      result.limit,
      '获取成功',
      req
    );
  }
}
```

## 16. 开发实施指南

### 16.1 项目初始化
```bash
# 1. 安装依赖
npm install

# 2. 配置环境变量
cp .env.example .env

# 3. 启动开发服务器
npm run start:dev
```

### 16.2 新增接口开发流程
1. **确定接口归属端**：管理端/办展方端/小程序端/公共接口
2. **创建DTO类**：定义请求和响应数据结构
3. **实现Service方法**：编写业务逻辑
4. **创建Controller方法**：继承BaseController，使用统一响应格式
5. **添加权限控制**：使用@Roles装饰器
6. **编写API文档**：使用Swagger装饰器
7. **编写测试用例**：单元测试和集成测试

### 16.3 错误处理最佳实践
```typescript
// 在Service中抛出业务异常
throw new BusinessException('用户不存在', BusinessCode.USER_NOT_FOUND);

// 在Controller中统一处理
try {
  const result = await this.service.method();
  return this.success(result, '操作成功', req);
} catch (error) {
  if (error.message.includes('不存在')) {
    return this.notFound('资源不存在', req);
  }
  throw error; // 其他异常由全局过滤器处理
}
```

### 16.4 分页查询标准实现
```typescript
@Get()
async findAll(@Query() query: any, @Request() req) {
  // 解析分页参数
  const { page, limit } = this.parsePagination(query);
  const { sort, order } = this.parseSort(query);

  // 调用服务方法
  const result = await this.service.findAll({
    page,
    limit,
    sort,
    order,
    ...query // 其他查询条件
  });

  // 返回分页响应
  return this.paginated(
    result.data,
    result.total,
    result.page,
    result.limit,
    '获取成功',
    req
  );
}
```

### 16.5 文件上传标准实现
```typescript
@Post('upload')
@UseInterceptors(FileInterceptor('file'))
async upload(
  @UploadedFile() file: Express.Multer.File,
  @Request() req
) {
  try {
    const result = await this.filesService.upload(file);
    return this.success(result, '上传成功', req);
  } catch (error) {
    if (error.message.includes('格式不支持')) {
      return this.error('文件格式不支持', 400, 'VALIDATION_ERROR', undefined, undefined, req);
    }
    throw error;
  }
}
```

## 17. 代码质量规范

### 17.1 TypeScript规范
- 严格类型检查
- 接口定义完整
- 避免使用any类型
- 使用枚举定义常量

### 17.2 代码注释规范
```typescript
/**
 * 创建用户
 * @param createUserDto 用户创建数据
 * @param req 请求对象
 * @returns 创建的用户信息
 */
@Post()
@ApiOperation({ summary: '创建用户' })
@ApiResponse({ status: 201, description: '用户创建成功' })
async create(@Body() createUserDto: CreateUserDto, @Request() req) {
  // 实现逻辑
}
```

### 17.3 测试规范
- 单元测试覆盖率 > 80%
- 集成测试覆盖主要业务流程
- E2E测试覆盖关键用户场景

## 18. 部署和监控

### 18.1 环境配置
- 开发环境：完整日志，详细错误信息
- 测试环境：模拟生产配置
- 生产环境：最小日志，安全错误信息

### 18.2 监控指标
- API响应时间
- 错误率统计
- 请求频率监控
- 系统资源使用率

### 18.3 日志规范
- 结构化日志格式
- 包含请求ID追踪
- 敏感信息脱敏
- 日志级别合理分配
