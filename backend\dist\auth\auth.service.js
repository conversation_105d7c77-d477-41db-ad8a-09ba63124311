"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const users_service_1 = require("../users/users.service");
const user_entity_1 = require("../users/entities/user.entity");
let AuthService = class AuthService {
    usersService;
    jwtService;
    constructor(usersService, jwtService) {
        this.usersService = usersService;
        this.jwtService = jwtService;
    }
    async login(loginDto) {
        const { username, password } = loginDto;
        let user = await this.usersService.findByUsername(username);
        if (!user) {
            user = await this.usersService.findByEmail(username);
        }
        if (!user) {
            throw new common_1.UnauthorizedException('用户名或密码错误');
        }
        const isPasswordValid = await this.usersService.validatePassword(user, password);
        if (!isPasswordValid) {
            throw new common_1.UnauthorizedException('用户名或密码错误');
        }
        if (user.status !== 1) {
            throw new common_1.UnauthorizedException('账户已被禁用');
        }
        return this.generateTokens(user);
    }
    async register(registerDto) {
        const { username, email, password } = registerDto;
        const existingUser = await this.usersService.findByUsername(username);
        if (existingUser) {
            throw new common_1.ConflictException('用户名已存在');
        }
        if (email) {
            const existingEmail = await this.usersService.findByEmail(email);
            if (existingEmail) {
                throw new common_1.ConflictException('邮箱已存在');
            }
        }
        const user = await this.usersService.create({
            username,
            email,
            password,
            loginType: user_entity_1.LoginType.WEBSITE,
        });
        return this.generateTokens(user);
    }
    async wechatLogin(wechatLoginDto) {
        const { code, userInfo } = wechatLoginDto;
        const openid = `mock_openid_${Date.now()}`;
        let user = await this.usersService.findByOpenid(openid);
        if (!user) {
            user = await this.usersService.create({
                openid,
                nickname: userInfo?.nickname,
                avatarUrl: userInfo?.avatarUrl,
                loginType: user_entity_1.LoginType.WECHAT,
            });
        }
        else {
            if (userInfo) {
                await this.usersService.update(user.id, {
                    nickname: userInfo.nickname,
                    avatarUrl: userInfo.avatarUrl,
                });
            }
        }
        return this.generateTokens(user);
    }
    async getProfile(userId) {
        const user = await this.usersService.findOne(userId);
        const { password, ...profile } = user;
        return profile;
    }
    async refreshToken(user) {
        return this.generateTokens(user);
    }
    async logout(userId) {
        return { message: '退出登录成功' };
    }
    generateTokens(user) {
        const payload = {
            sub: user.id,
            username: user.username,
            role: user.role,
        };
        const accessToken = this.jwtService.sign(payload);
        const refreshToken = this.jwtService.sign(payload, { expiresIn: '7d' });
        return {
            accessToken,
            refreshToken,
            user: {
                id: user.id,
                username: user.username,
                nickname: user.nickname,
                role: user.role,
                avatarUrl: user.avatarUrl,
            },
        };
    }
    async validateUser(userId) {
        const user = await this.usersService.findOne(userId);
        if (!user || user.status !== 1) {
            throw new common_1.UnauthorizedException('用户不存在或已被禁用');
        }
        return user;
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [users_service_1.UsersService, typeof (_a = typeof jwt_1.JwtService !== "undefined" && jwt_1.JwtService) === "function" ? _a : Object])
], AuthService);
//# sourceMappingURL=auth.service.js.map