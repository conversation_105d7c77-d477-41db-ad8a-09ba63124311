# 角色系统实施计划

## 🎯 实施优先级规划

### **Phase 1: 核心角色实现 (Week 1-2)**
**目标**：实现基础的6个角色和权限控制

#### 1.1 用户实体完善
```typescript
// 更新用户实体
enum UserRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin', 
  ORGANIZER = 'organizer',
  ARTIST = 'artist',
  JUDGE = 'judge',
  USER = 'user'
}

// 添加用户状态
enum UserStatus {
  ACTIVE = 1,      // 正常
  INACTIVE = 0,    // 禁用
  PENDING = 2,     // 待审核
  REJECTED = 3     // 已拒绝
}
```

#### 1.2 权限装饰器扩展
```typescript
// 新增角色装饰器
@ArtistAuth()     // 美工组权限
@SuperAdminAuth() // 超级管理员权限

// 组合权限装饰器
@CreatorOrAdmin() // 创建者或管理员
@ProjectMember()  // 项目成员
```

#### 1.3 基础权限控制
- ✅ 实现6个角色的基础权限
- ✅ 完善角色继承关系
- ✅ 实现资源级权限控制

---

### **Phase 2: 申请审批系统 (Week 3)**
**目标**：实现角色申请和审批流程

#### 2.1 申请实体设计
```typescript
// 角色申请表
@Entity()
export class RoleApplication {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @Column({ type: 'enum', enum: UserRole })
  targetRole: UserRole;

  @Column({ type: 'enum', enum: ApplicationStatus })
  status: ApplicationStatus;

  @Column({ type: 'json', nullable: true })
  applicationData: any; // 申请材料

  @Column({ nullable: true })
  reviewComment: string;

  @Column({ nullable: true })
  reviewerId: string;

  @CreateDateColumn()
  appliedAt: Date;

  @UpdateDateColumn()
  reviewedAt: Date;
}
```

#### 2.2 申请流程实现
- ✅ 用户提交角色申请
- ✅ 管理员审核申请
- ✅ 自动角色升级
- ✅ 申请状态通知

---

### **Phase 3: 高级权限特性 (Week 4)**
**目标**：实现细粒度权限和特殊场景

#### 3.1 资源级权限
```typescript
// 项目权限检查
@ProjectPermission('manage')
async updateProject(@ProjectId() projectId: string) {
  // 只有项目创建者或管理员可以修改
}

// 作品权限检查  
@ArtworkPermission('edit')
async updateArtwork(@ArtworkId() artworkId: string) {
  // 只有作品创建者或项目管理者可以修改
}
```

#### 3.2 临时权限
```typescript
// 临时评委权限
@TemporaryJudge('projectId')
async scoreArtwork() {
  // 为特定项目临时授予评委权限
}
```

---

## 🔧 技术实现细节

### **1. 数据库设计更新**

#### 用户表扩展
```sql
ALTER TABLE users 
ADD COLUMN profile_data JSON,           -- 用户资料
ADD COLUMN specialties JSON,            -- 专业领域
ADD COLUMN certification_level INT,     -- 认证等级
ADD COLUMN organization_id VARCHAR(36), -- 所属机构
ADD COLUMN last_login_at TIMESTAMP,     -- 最后登录
ADD COLUMN login_count INT DEFAULT 0;   -- 登录次数
```

#### 权限表设计
```sql
CREATE TABLE role_permissions (
  id VARCHAR(36) PRIMARY KEY,
  role ENUM('super_admin', 'admin', 'organizer', 'artist', 'judge', 'user'),
  resource VARCHAR(50),  -- 资源类型 (user, project, artwork, score)
  action VARCHAR(50),    -- 操作类型 (create, read, update, delete, manage)
  allowed BOOLEAN DEFAULT TRUE,
  conditions JSON,       -- 权限条件
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 用户权限关联表
```sql
CREATE TABLE user_permissions (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36),
  resource_type VARCHAR(50),
  resource_id VARCHAR(36),
  permission VARCHAR(50),
  granted_by VARCHAR(36),
  expires_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### **2. 权限检查中间件**

```typescript
@Injectable()
export class PermissionMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // 检查用户基础权限
    const user = req.user;
    const resource = this.extractResource(req);
    const action = this.extractAction(req);
    
    if (this.hasPermission(user, resource, action)) {
      next();
    } else {
      throw new ForbiddenException('权限不足');
    }
  }
}
```

### **3. 动态权限装饰器**

```typescript
export const RequirePermission = (resource: string, action: string) => {
  return applyDecorators(
    SetMetadata('permission', { resource, action }),
    UseGuards(PermissionGuard)
  );
};

// 使用示例
@RequirePermission('project', 'manage')
@Patch(':id')
async updateProject() {
  // 需要项目管理权限
}
```

---

## 📋 具体实施步骤

### **Step 1: 更新用户实体和枚举**

```typescript
// 1. 更新 UserRole 枚举
export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  ORGANIZER = 'organizer', 
  ARTIST = 'artist',
  JUDGE = 'judge',
  USER = 'user'
}

// 2. 添加用户状态枚举
export enum UserStatus {
  ACTIVE = 1,
  INACTIVE = 0,
  PENDING = 2,
  REJECTED = 3
}

// 3. 更新用户实体
@Entity()
export class User {
  // ... 现有字段
  
  @Column({ type: 'json', nullable: true })
  profileData: {
    realName?: string;
    phone?: string;
    address?: string;
    bio?: string;
    avatar?: string;
  };

  @Column({ type: 'json', nullable: true })
  specialties: string[]; // ['书法', '绘画', '篆刻']

  @Column({ type: 'int', default: 0 })
  certificationLevel: number; // 认证等级

  @Column({ nullable: true })
  organizationId: string; // 所属机构

  @Column({ type: 'timestamp', nullable: true })
  lastLoginAt: Date;

  @Column({ type: 'int', default: 0 })
  loginCount: number;
}
```

### **Step 2: 实现角色申请系统**

```typescript
// 1. 创建申请实体
@Entity()
export class RoleApplication {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column()
  userId: string;

  @Column({ type: 'enum', enum: UserRole })
  targetRole: UserRole;

  @Column({ type: 'enum', enum: ApplicationStatus, default: ApplicationStatus.PENDING })
  status: ApplicationStatus;

  @Column({ type: 'json' })
  applicationData: {
    organizationName?: string;
    organizationType?: string;
    contactPerson?: string;
    contactPhone?: string;
    qualifications?: string[];
    experience?: string;
    reason?: string;
  };

  @Column({ nullable: true })
  reviewComment: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'reviewer_id' })
  reviewer: User;

  @Column({ nullable: true })
  reviewerId: string;

  @CreateDateColumn()
  appliedAt: Date;

  @UpdateDateColumn()
  reviewedAt: Date;
}

// 2. 申请状态枚举
export enum ApplicationStatus {
  PENDING = 'pending',
  REVIEWING = 'reviewing', 
  APPROVED = 'approved',
  REJECTED = 'rejected'
}
```

### **Step 3: 实现权限检查服务**

```typescript
@Injectable()
export class PermissionService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async hasPermission(
    userId: string, 
    resource: string, 
    action: string,
    resourceId?: string
  ): Promise<boolean> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    
    if (!user) return false;

    // 超级管理员拥有所有权限
    if (user.role === UserRole.SUPER_ADMIN) return true;

    // 基于角色的权限检查
    const rolePermissions = this.getRolePermissions(user.role);
    const hasRolePermission = rolePermissions.some(p => 
      p.resource === resource && p.action === action
    );

    if (!hasRolePermission) return false;

    // 资源级权限检查
    if (resourceId) {
      return await this.hasResourcePermission(user, resource, action, resourceId);
    }

    return true;
  }

  private async hasResourcePermission(
    user: User,
    resource: string,
    action: string, 
    resourceId: string
  ): Promise<boolean> {
    // 检查用户是否是资源的创建者或有特殊权限
    switch (resource) {
      case 'project':
        return await this.hasProjectPermission(user, action, resourceId);
      case 'artwork':
        return await this.hasArtworkPermission(user, action, resourceId);
      default:
        return false;
    }
  }

  private getRolePermissions(role: UserRole) {
    const permissions = {
      [UserRole.SUPER_ADMIN]: [
        { resource: '*', action: '*' }
      ],
      [UserRole.ADMIN]: [
        { resource: 'user', action: 'read' },
        { resource: 'user', action: 'update' },
        { resource: 'project', action: 'read' },
        { resource: 'application', action: 'review' }
      ],
      [UserRole.ORGANIZER]: [
        { resource: 'project', action: 'create' },
        { resource: 'project', action: 'manage' },
        { resource: 'artwork', action: 'manage' }
      ],
      [UserRole.ARTIST]: [
        { resource: 'artwork', action: 'create' },
        { resource: 'artwork', action: 'update' }
      ],
      [UserRole.JUDGE]: [
        { resource: 'score', action: 'create' },
        { resource: 'score', action: 'update' }
      ],
      [UserRole.USER]: [
        { resource: 'project', action: 'read' },
        { resource: 'artwork', action: 'read' }
      ]
    };

    return permissions[role] || [];
  }
}
```

### **Step 4: 创建新的权限装饰器**

```typescript
// 1. 艺术家权限装饰器
export const ArtistAuth = () => {
  return RoleAuth([UserRole.ARTIST, UserRole.ADMIN, UserRole.SUPER_ADMIN]);
};

// 2. 资源权限装饰器
export const ResourcePermission = (resource: string, action: string) => {
  return applyDecorators(
    SetMetadata('resource_permission', { resource, action }),
    UseGuards(RequiredAuthGuard, ResourcePermissionGuard)
  );
};

// 3. 项目成员权限装饰器
export const ProjectMember = () => {
  return applyDecorators(
    SetMetadata('project_member', true),
    UseGuards(RequiredAuthGuard, ProjectMemberGuard)
  );
};
```

---

## 🧪 测试策略

### **1. 单元测试**
```typescript
describe('PermissionService', () => {
  test('super admin should have all permissions', async () => {
    const hasPermission = await permissionService.hasPermission(
      superAdminId, 'any_resource', 'any_action'
    );
    expect(hasPermission).toBe(true);
  });

  test('organizer should manage own projects', async () => {
    const hasPermission = await permissionService.hasPermission(
      organizerId, 'project', 'manage', ownProjectId
    );
    expect(hasPermission).toBe(true);
  });
});
```

### **2. 集成测试**
```typescript
describe('Role-based Access Control', () => {
  test('artist can only edit own artworks', async () => {
    // 测试艺术家只能编辑自己的作品
  });

  test('judge can only score assigned projects', async () => {
    // 测试评委只能为指定项目评分
  });
});
```

---

## 📊 实施时间表

| 阶段 | 任务 | 预计时间 | 负责人 |
|------|------|----------|--------|
| Week 1 | 用户实体更新 + 基础权限 | 3天 | 后端开发 |
| Week 1 | 权限装饰器扩展 | 2天 | 后端开发 |
| Week 2 | 申请审批系统 | 4天 | 后端开发 |
| Week 2 | 权限检查服务 | 3天 | 后端开发 |
| Week 3 | 资源级权限控制 | 4天 | 后端开发 |
| Week 3 | 前端权限界面 | 3天 | 前端开发 |
| Week 4 | 测试和优化 | 5天 | 全栈开发 |
| Week 4 | 文档和部署 | 2天 | 全栈开发 |

这个实施计划确保了角色系统的逐步完善，既满足了当前需求，又为未来扩展留下了空间。
