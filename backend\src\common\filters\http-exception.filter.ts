import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { ApiErrorResponse, ErrorType, BusinessCode } from '../base/base.controller';

/**
 * 全局HTTP异常过滤器
 * 统一处理所有HTTP异常，返回标准格式的错误响应
 */
@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    // 获取请求ID
    const requestId = (request.headers['x-request-id'] as string) || uuidv4();

    let status: number;
    let errorResponse: ApiErrorResponse;

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();

      // 处理不同类型的异常响应
      if (typeof exceptionResponse === 'string') {
        errorResponse = this.createErrorResponse(
          status,
          exceptionResponse,
          ErrorType.SYSTEM_ERROR,
          requestId
        );
      } else if (typeof exceptionResponse === 'object') {
        const response = exceptionResponse as any;
        
        // 处理验证错误
        if (response.message && Array.isArray(response.message)) {
          errorResponse = this.createValidationErrorResponse(
            response.message,
            requestId
          );
        } else {
          errorResponse = this.createErrorResponse(
            status,
            response.message || response.error || '请求处理失败',
            this.getErrorType(status),
            requestId,
            response.details
          );
        }
      } else {
        errorResponse = this.createErrorResponse(
          status,
          '请求处理失败',
          this.getErrorType(status),
          requestId
        );
      }
    } else {
      // 处理非HTTP异常
      status = HttpStatus.INTERNAL_SERVER_ERROR;
      errorResponse = this.createErrorResponse(
        status,
        '服务器内部错误',
        ErrorType.SYSTEM_ERROR,
        requestId
      );

      // 记录未知异常
      this.logger.error(
        `Unhandled exception: ${exception}`,
        exception instanceof Error ? exception.stack : undefined,
        `Request ID: ${requestId}`
      );
    }

    // 记录错误日志
    this.logError(request, errorResponse, exception);

    // 设置响应头
    response.setHeader('X-Request-ID', requestId);
    response.setHeader('Content-Type', 'application/json; charset=utf-8');

    // 返回错误响应
    response.status(status).json(errorResponse);
  }

  /**
   * 创建标准错误响应
   */
  private createErrorResponse(
    code: number,
    message: string,
    type: ErrorType,
    requestId: string,
    details?: any
  ): ApiErrorResponse {
    return {
      success: false,
      code,
      message,
      error: {
        type,
        details,
      },
      timestamp: Date.now(),
      requestId,
    };
  }

  /**
   * 创建验证错误响应
   */
  private createValidationErrorResponse(
    validationErrors: string[],
    requestId: string
  ): ApiErrorResponse {
    const details: Record<string, string> = {};
    
    validationErrors.forEach(error => {
      // 解析验证错误信息，提取字段名
      const match = error.match(/^(\w+)\s+(.+)$/);
      if (match) {
        const [, field, message] = match;
        details[field] = message;
      } else {
        details['general'] = error;
      }
    });

    return {
      success: false,
      code: BusinessCode.VALIDATION_ERROR,
      message: '参数验证失败',
      error: {
        type: ErrorType.VALIDATION_ERROR,
        details,
      },
      timestamp: Date.now(),
      requestId,
    };
  }

  /**
   * 根据HTTP状态码获取错误类型
   */
  private getErrorType(status: number): ErrorType {
    if (status >= 400 && status < 500) {
      switch (status) {
        case 401:
          return ErrorType.AUTHENTICATION_ERROR;
        case 403:
          return ErrorType.AUTHORIZATION_ERROR;
        case 422:
          return ErrorType.VALIDATION_ERROR;
        default:
          return ErrorType.BUSINESS_ERROR;
      }
    } else if (status >= 500) {
      return ErrorType.SYSTEM_ERROR;
    }
    return ErrorType.SYSTEM_ERROR;
  }

  /**
   * 记录错误日志
   */
  private logError(
    request: Request,
    errorResponse: ApiErrorResponse,
    exception: unknown
  ) {
    const { method, url, ip, headers } = request;
    const userAgent = headers['user-agent'] || '';
    const userId = (request as any).user?.id || 'anonymous';

    const logMessage = [
      `HTTP ${errorResponse.code} Error`,
      `Method: ${method}`,
      `URL: ${url}`,
      `IP: ${ip}`,
      `User: ${userId}`,
      `User-Agent: ${userAgent}`,
      `Request ID: ${errorResponse.requestId}`,
      `Message: ${errorResponse.message}`,
    ].join(' | ');

    if (errorResponse.code >= 500) {
      this.logger.error(
        logMessage,
        exception instanceof Error ? exception.stack : undefined
      );
    } else if (errorResponse.code >= 400) {
      this.logger.warn(logMessage);
    }
  }
}

/**
 * 业务异常类
 * 用于抛出业务逻辑相关的异常
 */
export class BusinessException extends HttpException {
  constructor(
    message: string,
    code: BusinessCode = BusinessCode.BAD_REQUEST,
    details?: any
  ) {
    super(
      {
        message,
        code,
        details,
      },
      code >= 500 ? HttpStatus.INTERNAL_SERVER_ERROR : HttpStatus.BAD_REQUEST
    );
  }
}

/**
 * 验证异常类
 * 用于抛出参数验证相关的异常
 */
export class ValidationException extends HttpException {
  constructor(message: string, details?: any) {
    super(
      {
        message,
        code: BusinessCode.VALIDATION_ERROR,
        details,
      },
      HttpStatus.UNPROCESSABLE_ENTITY
    );
  }
}
