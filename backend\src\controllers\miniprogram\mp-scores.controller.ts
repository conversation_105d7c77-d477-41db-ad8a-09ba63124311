import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  Request,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { BaseController } from '../../common/base/base.controller';
import { ScoresService } from '../../scores/scores.service';
import { CreateScoreDto } from '../../scores/dto/create-score.dto';
import { UpdateScoreDto } from '../../scores/dto/update-score.dto';
import { CreateCommentDto } from '../../scores/dto/create-comment.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/entities/user.entity';
import { ScoreStatus } from '../../scores/entities/score.entity';

/**
 * 小程序端评分管理控制器
 * 路由前缀: /api/v1/mp/scores
 */
@ApiTags('小程序端-评分管理')
@Controller('mp/scores')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.JUDGE, UserRole.ADMIN, UserRole.SUPER_ADMIN)
@ApiBearerAuth()
export class MpScoresController extends BaseController {
  constructor(private readonly scoresService: ScoresService) {
    super();
  }

  /**
   * 创建评分
   */
  @Post()
  @ApiOperation({ summary: '创建评分' })
  @ApiResponse({ status: 201, description: '评分创建成功' })
  @ApiResponse({ status: 400, description: '参数验证失败' })
  @ApiResponse({ status: 409, description: '重复评分' })
  @Roles(UserRole.JUDGE)
  async create(@Body() createScoreDto: CreateScoreDto, @Request() req) {
    try {
      const score = await this.scoresService.create(createScoreDto, req.user.id);
      return this.created(score, '评分创建成功', req);
    } catch (error) {
      if (error.message.includes('已经评分')) {
        return this.conflict('您已经对此作品进行过评分', req);
      }
      if (error.message.includes('验证失败')) {
        return this.validationError(error.details, error.message, req);
      }
      if (error.message.includes('时间已过')) {
        return this.error(
          '评分时间已过，无法进行评分',
          this.BusinessCode.SCORE_TIME_EXPIRED,
          'BUSINESS_ERROR',
          undefined,
          undefined,
          req
        );
      }
      throw error;
    }
  }

  /**
   * 获取我的评分列表
   */
  @Get('my/:projectId')
  @ApiOperation({ summary: '获取我的评分列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @Roles(UserRole.JUDGE)
  async findMyScores(
    @Param('projectId') projectId: string,
    @Query() query: any,
    @Request() req
  ) {
    const { page, limit } = this.parsePagination(query);
    
    const result = await this.scoresService.findMyScores(
      projectId,
      req.user.id,
      {
        page,
        limit,
        status: query.status as ScoreStatus
      }
    );

    return this.paginated(
      result.data,
      result.total,
      result.page,
      result.limit,
      '我的评分列表获取成功',
      req
    );
  }

  /**
   * 获取评分详情
   */
  @Get(':id')
  @ApiOperation({ summary: '获取评分详情' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '评分不存在' })
  async findOne(@Param('id') id: string, @Request() req) {
    try {
      const score = await this.scoresService.findOne(id, req.user);
      return this.success(score, '评分详情获取成功', req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('评分记录不存在', req);
      }
      if (error.message.includes('权限')) {
        return this.forbidden(error.message, req);
      }
      throw error;
    }
  }

  /**
   * 更新评分
   */
  @Patch(':id')
  @ApiOperation({ summary: '更新评分' })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiResponse({ status: 404, description: '评分不存在' })
  @ApiResponse({ status: 403, description: '权限不足' })
  @ApiResponse({ status: 400, description: '评分已提交，无法修改' })
  @Roles(UserRole.JUDGE)
  async update(
    @Param('id') id: string,
    @Body() updateScoreDto: UpdateScoreDto,
    @Request() req
  ) {
    try {
      const score = await this.scoresService.update(id, updateScoreDto, req.user);
      return this.success(score, '评分更新成功', req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('评分记录不存在', req);
      }
      if (error.message.includes('权限')) {
        return this.forbidden('只能修改自己的评分', req);
      }
      if (error.message.includes('已提交')) {
        return this.error(
          '评分已提交，无法修改',
          400,
          'BUSINESS_ERROR',
          undefined,
          undefined,
          req
        );
      }
      throw error;
    }
  }

  /**
   * 提交评分
   */
  @Patch(':id/submit')
  @ApiOperation({ summary: '提交评分' })
  @ApiResponse({ status: 200, description: '提交成功' })
  @ApiResponse({ status: 400, description: '评分信息不完整' })
  @Roles(UserRole.JUDGE)
  async submit(@Param('id') id: string, @Request() req) {
    try {
      const score = await this.scoresService.submit(id, req.user);
      return this.success(score, '评分提交成功', req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('评分记录不存在', req);
      }
      if (error.message.includes('权限')) {
        return this.forbidden('只能提交自己的评分', req);
      }
      if (error.message.includes('不完整')) {
        return this.error(
          '评分信息不完整，无法提交',
          400,
          'BUSINESS_ERROR',
          undefined,
          undefined,
          req
        );
      }
      throw error;
    }
  }

  /**
   * 删除评分
   */
  @Delete(':id')
  @ApiOperation({ summary: '删除评分' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 404, description: '评分不存在' })
  @Roles(UserRole.JUDGE)
  async remove(@Param('id') id: string, @Request() req) {
    try {
      await this.scoresService.remove(id, req.user);
      return this.success(null, '评分删除成功', req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('评分记录不存在', req);
      }
      if (error.message.includes('权限')) {
        return this.forbidden('只能删除自己的评分', req);
      }
      throw error;
    }
  }

  /**
   * 添加评语
   */
  @Post(':scoreId/comments')
  @ApiOperation({ summary: '添加评语' })
  @ApiResponse({ status: 201, description: '评语添加成功' })
  @UseInterceptors(FileInterceptor('audio'))
  @Roles(UserRole.JUDGE)
  async createComment(
    @Param('scoreId') scoreId: string,
    @Body() createCommentDto: CreateCommentDto,
    @UploadedFile() audio: Express.Multer.File,
    @Request() req
  ) {
    try {
      const comment = await this.scoresService.createComment(
        scoreId,
        createCommentDto,
        req.user,
        audio
      );
      return this.created(comment, '评语添加成功', req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('评分记录不存在', req);
      }
      if (error.message.includes('权限')) {
        return this.forbidden('只能为自己的评分添加评语', req);
      }
      throw error;
    }
  }

  /**
   * 获取评语列表
   */
  @Get(':scoreId/comments')
  @ApiOperation({ summary: '获取评语列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getComments(@Param('scoreId') scoreId: string, @Request() req) {
    try {
      const comments = await this.scoresService.getComments(scoreId, req.user);
      return this.success(comments, '评语列表获取成功', req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('评分记录不存在', req);
      }
      if (error.message.includes('权限')) {
        return this.forbidden(error.message, req);
      }
      throw error;
    }
  }

  /**
   * 更新评语
   */
  @Patch('comments/:commentId')
  @ApiOperation({ summary: '更新评语' })
  @ApiResponse({ status: 200, description: '更新成功' })
  @UseInterceptors(FileInterceptor('audio'))
  @Roles(UserRole.JUDGE)
  async updateComment(
    @Param('commentId') commentId: string,
    @Body() updateData: Partial<CreateCommentDto>,
    @UploadedFile() audio: Express.Multer.File,
    @Request() req
  ) {
    try {
      const comment = await this.scoresService.updateComment(
        commentId,
        updateData,
        req.user,
        audio
      );
      return this.success(comment, '评语更新成功', req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('评语不存在', req);
      }
      if (error.message.includes('权限')) {
        return this.forbidden('只能修改自己的评语', req);
      }
      throw error;
    }
  }

  /**
   * 删除评语
   */
  @Delete('comments/:commentId')
  @ApiOperation({ summary: '删除评语' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @Roles(UserRole.JUDGE)
  async removeComment(@Param('commentId') commentId: string, @Request() req) {
    try {
      await this.scoresService.removeComment(commentId, req.user);
      return this.success(null, '评语删除成功', req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('评语不存在', req);
      }
      if (error.message.includes('权限')) {
        return this.forbidden('只能删除自己的评语', req);
      }
      throw error;
    }
  }

  /**
   * 获取评分进度
   */
  @Get('progress/:projectId')
  @ApiOperation({ summary: '获取评分进度' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @Roles(UserRole.JUDGE)
  async getScoreProgress(
    @Param('projectId') projectId: string,
    @Request() req
  ) {
    try {
      const progress = await this.scoresService.getScoreProgress(
        projectId,
        req.user.id
      );
      return this.success(progress, '评分进度获取成功', req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('项目不存在', req);
      }
      throw error;
    }
  }

  /**
   * 批量保存评分
   */
  @Post('batch')
  @ApiOperation({ summary: '批量保存评分' })
  @ApiResponse({ status: 201, description: '批量保存成功' })
  @Roles(UserRole.JUDGE)
  async batchSave(@Body() scores: CreateScoreDto[], @Request() req) {
    try {
      const result = await this.scoresService.batchSave(scores, req.user.id);
      return this.created(result, '批量保存成功', req);
    } catch (error) {
      if (error.message.includes('验证失败')) {
        return this.validationError(error.details, error.message, req);
      }
      throw error;
    }
  }
}
