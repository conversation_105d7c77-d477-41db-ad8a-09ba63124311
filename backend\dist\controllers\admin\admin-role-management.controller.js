"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminRoleManagementController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const base_controller_1 = require("../../common/base/base.controller");
const auth_decorator_1 = require("../../auth/decorators/auth.decorator");
const user_decorator_1 = require("../../auth/decorators/user.decorator");
const role_management_service_1 = require("../../auth/services/role-management.service");
const role_management_dto_1 = require("../../users/dto/role-management.dto");
const user_roles_entity_1 = require("../../users/entities/user-roles.entity");
let AdminRoleManagementController = class AdminRoleManagementController extends base_controller_1.BaseController {
    roleManagementService;
    constructor(roleManagementService) {
        super();
        this.roleManagementService = roleManagementService;
    }
    async getRoleApplications(query, req) {
        try {
            const result = await this.roleManagementService.getRoleApplications(query);
            return this.paginated(result.data, result.total, result.page, result.limit, '角色申请列表获取成功', req);
        }
        catch (error) {
            throw error;
        }
    }
    async reviewRoleApplication(applicationId, reviewDto, reviewerId, req) {
        try {
            const result = await this.roleManagementService.reviewRoleApplication(applicationId, reviewerId, reviewDto.approved, reviewDto.reviewComment);
            return this.success({
                application: result,
                action: reviewDto.approved ? 'approved' : 'rejected',
            }, `申请${reviewDto.approved ? '批准' : '拒绝'}成功`, req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('申请记录不存在', req);
            }
            if (error.message.includes('已被处理')) {
                return this.conflict('申请已被处理', req);
            }
            throw error;
        }
    }
    async assignGlobalRole(assignDto, grantedBy, req) {
        try {
            const result = await this.roleManagementService.addGlobalRole(assignDto.userId, assignDto.role, grantedBy, assignDto.metadata);
            return this.created(result, '角色分配成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('用户不存在', req);
            }
            if (error.message.includes('已拥有')) {
                return this.conflict('用户已拥有此角色', req);
            }
            throw error;
        }
    }
    async batchAssignRoles(batchDto, grantedBy, req) {
        try {
            const results = await this.roleManagementService.batchAssignGlobalRoles(batchDto.assignments, grantedBy);
            return this.created({
                successful: results.length,
                total: batchDto.assignments.length,
                results,
            }, '批量角色分配完成', req);
        }
        catch (error) {
            throw error;
        }
    }
    async removeGlobalRole(userId, role, reason, removedBy, req) {
        try {
            await this.roleManagementService.removeGlobalRole(userId, role, removedBy, reason);
            return this.success(null, '角色移除成功', req);
        }
        catch (error) {
            if (error.message.includes('没有此角色')) {
                return this.notFound('用户没有此角色', req);
            }
            throw error;
        }
    }
    async getUserRoles(userId, req) {
        try {
            const roles = await this.roleManagementService.getUserGlobalRoles(userId);
            return this.success({
                userId,
                roles,
                totalRoles: roles.length,
            }, '用户角色获取成功', req);
        }
        catch (error) {
            throw error;
        }
    }
    async getPermissionTemplates(role, req) {
        try {
            const templates = await this.roleManagementService.getPermissionTemplates(role);
            return this.success(templates, '权限模板获取成功', req);
        }
        catch (error) {
            throw error;
        }
    }
    async createPermissionTemplate(templateDto, req) {
        try {
            const template = await this.roleManagementService.createPermissionTemplate(templateDto.name, templateDto.role, templateDto.permissions, templateDto.description, templateDto.isDefault);
            return this.created(template, '权限模板创建成功', req);
        }
        catch (error) {
            throw error;
        }
    }
    async getRoleStatistics(req) {
        try {
            const stats = await this.roleManagementService.getRoleStatistics();
            return this.success(stats, '角色统计信息获取成功', req);
        }
        catch (error) {
            throw error;
        }
    }
    async getUsersRoleOverview(query, req) {
        try {
            const { page = 1, limit = 20, role, search } = query;
            const mockData = {
                data: [],
                total: 0,
                page,
                limit
            };
            return this.paginated(mockData.data, mockData.total, mockData.page, mockData.limit, '用户角色概览获取成功', req);
        }
        catch (error) {
            throw error;
        }
    }
    async exportRoleData(format = 'excel', req) {
        try {
            return this.success({
                format,
                downloadUrl: '/api/v1/admin/role-management/download/roles.xlsx',
                generatedAt: new Date(),
            }, '角色数据导出成功', req);
        }
        catch (error) {
            throw error;
        }
    }
};
exports.AdminRoleManagementController = AdminRoleManagementController;
__decorate([
    (0, common_1.Get)('applications'),
    (0, swagger_1.ApiOperation)({ summary: '获取角色申请列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [role_management_dto_1.QueryRoleApplicationsDto, Object]),
    __metadata("design:returntype", Promise)
], AdminRoleManagementController.prototype, "getRoleApplications", null);
__decorate([
    (0, common_1.Patch)('applications/:applicationId/review'),
    (0, swagger_1.ApiOperation)({ summary: '审批角色申请' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '审批成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '申请不存在' }),
    __param(0, (0, common_1.Param)('applicationId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, user_decorator_1.UserId)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, role_management_dto_1.ReviewRoleApplicationDto, String, Object]),
    __metadata("design:returntype", Promise)
], AdminRoleManagementController.prototype, "reviewRoleApplication", null);
__decorate([
    (0, common_1.Post)('assign-role'),
    (0, swagger_1.ApiOperation)({ summary: '直接分配全局角色' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '分配成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '用户已拥有此角色' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, user_decorator_1.UserId)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [role_management_dto_1.AssignGlobalRoleDto, String, Object]),
    __metadata("design:returntype", Promise)
], AdminRoleManagementController.prototype, "assignGlobalRole", null);
__decorate([
    (0, common_1.Post)('batch-assign'),
    (0, swagger_1.ApiOperation)({ summary: '批量分配角色' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '批量分配完成' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, user_decorator_1.UserId)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [role_management_dto_1.BatchAssignRolesDto, String, Object]),
    __metadata("design:returntype", Promise)
], AdminRoleManagementController.prototype, "batchAssignRoles", null);
__decorate([
    (0, common_1.Delete)('users/:userId/roles/:role'),
    (0, swagger_1.ApiOperation)({ summary: '移除用户的全局角色' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '移除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户没有此角色' }),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Param)('role')),
    __param(2, (0, common_1.Body)('reason')),
    __param(3, (0, user_decorator_1.UserId)()),
    __param(4, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, Object]),
    __metadata("design:returntype", Promise)
], AdminRoleManagementController.prototype, "removeGlobalRole", null);
__decorate([
    (0, common_1.Get)('users/:userId/roles'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户的所有角色' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AdminRoleManagementController.prototype, "getUserRoles", null);
__decorate([
    (0, common_1.Get)('permission-templates'),
    (0, swagger_1.ApiOperation)({ summary: '获取权限模板列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)('role')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AdminRoleManagementController.prototype, "getPermissionTemplates", null);
__decorate([
    (0, common_1.Post)('permission-templates'),
    (0, swagger_1.ApiOperation)({ summary: '创建权限模板' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [role_management_dto_1.CreatePermissionTemplateDto, Object]),
    __metadata("design:returntype", Promise)
], AdminRoleManagementController.prototype, "createPermissionTemplate", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({ summary: '获取角色统计信息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminRoleManagementController.prototype, "getRoleStatistics", null);
__decorate([
    (0, common_1.Get)('users-overview'),
    (0, swagger_1.ApiOperation)({ summary: '获取所有用户的角色概览' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AdminRoleManagementController.prototype, "getUsersRoleOverview", null);
__decorate([
    (0, common_1.Get)('export'),
    (0, swagger_1.ApiOperation)({ summary: '导出角色数据' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '导出成功' }),
    __param(0, (0, common_1.Query)('format')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AdminRoleManagementController.prototype, "exportRoleData", null);
exports.AdminRoleManagementController = AdminRoleManagementController = __decorate([
    (0, swagger_1.ApiTags)('管理员-角色管理'),
    (0, common_1.Controller)('admin/role-management'),
    (0, auth_decorator_1.AdminAuth)(),
    __metadata("design:paramtypes", [role_management_service_1.RoleManagementService])
], AdminRoleManagementController);
//# sourceMappingURL=admin-role-management.controller.js.map