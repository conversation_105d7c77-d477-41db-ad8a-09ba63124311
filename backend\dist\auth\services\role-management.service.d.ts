import { Repository } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { UserGlobalRole, UserProjectRole, RoleApplication, PermissionTemplate, GlobalRole, ProjectRole } from '../../users/entities/user-roles.entity';
export declare class RoleManagementService {
    private userRepository;
    private globalRoleRepository;
    private projectRoleRepository;
    private applicationRepository;
    private templateRepository;
    constructor(userRepository: Repository<User>, globalRoleRepository: Repository<UserGlobalRole>, projectRoleRepository: Repository<UserProjectRole>, applicationRepository: Repository<RoleApplication>, templateRepository: Repository<PermissionTemplate>);
    applyForGlobalRole(userId: string, targetRole: GlobalRole, applicationData: any): Promise<RoleApplication>;
    reviewRoleApplication(applicationId: string, reviewerId: string, approved: boolean, reviewComment?: string): Promise<RoleApplication>;
    addGlobalRole(userId: string, role: GlobalRole, grantedBy: string, metadata?: any): Promise<UserGlobalRole>;
    removeGlobalRole(userId: string, role: GlobalRole, removedBy: string, reason?: string): Promise<void>;
    getUserGlobalRoles(userId: string): Promise<UserGlobalRole[]>;
    getRoleApplications(filters: {
        status?: string;
        targetRole?: GlobalRole;
        userId?: string;
        page?: number;
        limit?: number;
    }): Promise<{
        data: RoleApplication[];
        total: number;
        page: number;
        limit: number;
    }>;
    getPermissionTemplates(role?: ProjectRole): Promise<PermissionTemplate[]>;
    createPermissionTemplate(name: string, role: ProjectRole, permissions: any, description?: string, isDefault?: boolean): Promise<PermissionTemplate>;
    getDefaultPermissions(role: ProjectRole): Promise<any>;
    private getHardcodedDefaultPermissions;
    batchAssignGlobalRoles(assignments: Array<{
        userId: string;
        role: GlobalRole;
        metadata?: any;
    }>, grantedBy: string): Promise<UserGlobalRole[]>;
    getRoleStatistics(): Promise<any>;
}
