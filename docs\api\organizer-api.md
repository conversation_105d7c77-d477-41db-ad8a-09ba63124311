# 办展方管理端 API 接口规范

## 概述
办展方管理端供通过审核的办展方使用，包含项目管理、作品管理、评分查看、数据统计等功能。

## 认证接口

### 用户登录
```http
POST /auth/login
```

**请求体**:
```json
{
  "username": "<EMAIL>",
  "password": "password123"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
    "user": {
      "id": "uuid",
      "username": "<EMAIL>",
      "nickname": "中国书法协会",
      "role": "organizer",
      "avatarUrl": "https://..."
    }
  }
}
```

## 项目管理接口

### 创建项目
```http
POST /projects
Authorization: Bearer {token}
```

**请求体**:
```json
{
  "name": "2024年春季书法大赛",
  "description": "面向全国的书法作品征集活动，旨在传承和发扬中华优秀传统文化...",
  "coverImage": "https://...",
  "collectStartTime": "2024-01-01T00:00:00Z",
  "collectEndTime": "2024-01-31T23:59:59Z",
  "judgeStartTime": "2024-02-01T00:00:00Z",
  "judgeEndTime": "2024-02-15T23:59:59Z",
  "displayStartTime": "2024-02-16T00:00:00Z",
  "displayEndTime": "2024-03-16T23:59:59Z",
  "isPublic": true
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "project_uuid",
    "name": "2024年春季书法大赛",
    "description": "面向全国的书法作品征集活动...",
    "status": "preparing",
    "organizerId": "organizer_uuid",
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```

### 获取我的项目列表
```http
GET /projects/my?page=1&limit=10
Authorization: Bearer {token}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "project_uuid",
        "name": "2024年春季书法大赛",
        "description": "面向全国的书法作品征集活动...",
        "coverImage": "https://...",
        "status": "collecting",
        "collectStartTime": "2024-01-01T00:00:00Z",
        "collectEndTime": "2024-01-31T23:59:59Z",
        "isPublic": true,
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 5,
    "page": 1,
    "limit": 10,
    "totalPages": 1
  }
}
```

### 更新项目
```http
PATCH /projects/{projectId}
Authorization: Bearer {token}
```

**请求体**:
```json
{
  "name": "2024年春季书法大赛（修订版）",
  "description": "更新后的描述...",
  "collectEndTime": "2024-02-05T23:59:59Z"
}
```

### 更新项目状态
```http
PATCH /projects/{projectId}/status
Authorization: Bearer {token}
```

**请求体**:
```json
{
  "status": "collecting"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "project_uuid",
    "status": "collecting",
    "updatedAt": "2024-01-01T08:00:00Z"
  },
  "message": "项目状态更新成功"
}
```

### 生成项目二维码
```http
POST /projects/{projectId}/generate-qr
Authorization: Bearer {token}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "qrCode": "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=project%3Auuid"
  }
}
```

### 删除项目
```http
DELETE /projects/{projectId}
Authorization: Bearer {token}
```

## 作品管理接口

### 上传单个作品
```http
POST /artworks
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

**请求体**:
```
projectId: project_uuid
title: 临王羲之兰亭序
description: 临摹王羲之兰亭序，注重笔法传承
size: 138cm×69cm
technique: 行书
creationYear: 2024
authorName: 王书法
authorPhone: 139****9999
authorAddress: 北京市朝阳区
authorBio: 中国书法家协会会员
tags: ["行书", "临帖", "王羲之"]
image: File
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "artwork_uuid",
    "artworkNo": "A001-0001",
    "title": "临王羲之兰亭序",
    "imageUrl": "/uploads/artworks/uuid.jpg",
    "thumbnailUrl": "/uploads/artworks/thumb_uuid.jpg",
    "status": "uploaded",
    "uploadTime": "2024-01-15T10:30:00Z"
  }
}
```

### 批量上传作品
```http
POST /artworks/batch-upload
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

**请求体**:
```
projectId: project_uuid
artworks: [
  {
    "title": "作品1",
    "authorName": "作者1",
    ...
  },
  {
    "title": "作品2",
    "authorName": "作者2",
    ...
  }
]
images: File[]
```

### Excel导入作品信息
```http
POST /artworks/import-excel
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

**请求体**:
```
projectId: project_uuid
excel: File (Excel文件)
```

**响应**:
```json
{
  "success": true,
  "data": {
    "success": 95,
    "failed": 5,
    "errors": [
      "第 3 行: 作品名称为必填项",
      "第 7 行: 作者姓名为必填项"
    ]
  }
}
```

### 获取项目作品列表
```http
GET /artworks/project/{projectId}?page=1&limit=20&status=uploaded&authorName=王
Authorization: Bearer {token}
```

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `status`: 作品状态 (uploaded, reviewing, approved, rejected)
- `authorName`: 作者姓名（模糊搜索）

**响应**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "artwork_uuid",
        "artworkNo": "A001-0001",
        "title": "临王羲之兰亭序",
        "thumbnailUrl": "https://...",
        "authorName": "王书法",
        "status": "approved",
        "uploadTime": "2024-01-15T10:30:00Z",
        "reviewTime": "2024-01-16T09:00:00Z"
      }
    ],
    "total": 200,
    "page": 1,
    "limit": 20,
    "totalPages": 10
  }
}
```

### 更新作品信息
```http
PATCH /artworks/{artworkId}
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

**请求体**:
```
title: 更新后的作品名称
description: 更新后的描述
image: File (可选，新图片)
```

### 更新作品状态
```http
PATCH /artworks/{artworkId}/status
Authorization: Bearer {token}
```

**请求体**:
```json
{
  "status": "approved",
  "reviewComment": "作品质量优秀，符合参赛要求"
}
```

### 删除作品
```http
DELETE /artworks/{artworkId}
Authorization: Bearer {token}
```

### 获取作品统计
```http
GET /artworks/stats/{projectId}
Authorization: Bearer {token}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "total": 200,
    "byStatus": {
      "uploaded": 50,
      "reviewing": 30,
      "approved": 100,
      "rejected": 20
    }
  }
}
```

## 评分查看接口

### 获取项目评分列表
```http
GET /scores/project/{projectId}?page=1&limit=10&judgeId={judgeId}&status=submitted
Authorization: Bearer {token}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "score_uuid",
        "artwork": {
          "id": "artwork_uuid",
          "artworkNo": "A001-0001",
          "title": "临王羲之兰亭序",
          "authorName": "王书法"
        },
        "judge": {
          "id": "judge_uuid",
          "nickname": "张评委"
        },
        "objectivePass": 1,
        "totalScore": 118,
        "status": "submitted",
        "scoredAt": "2024-02-05T14:30:00Z"
      }
    ],
    "total": 500,
    "page": 1,
    "limit": 10,
    "totalPages": 50
  }
}
```

### 获取作品评分详情
```http
GET /scores/artwork/{artworkId}
Authorization: Bearer {token}
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": "score_uuid",
      "judge": {
        "id": "judge_uuid",
        "nickname": "张评委"
      },
      "objectivePass": 1,
      "subjectiveScores": {
        "临帖传承": 25,
        "结体结构": 28,
        "艺术创意": 35,
        "整体效果": 30
      },
      "totalScore": 118,
      "status": "submitted",
      "scoredAt": "2024-02-05T14:30:00Z"
    }
  ]
}
```

### 获取项目评分统计
```http
GET /scores/stats/project/{projectId}
Authorization: Bearer {token}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "totalScores": 500,
    "submittedScores": 450,
    "averageScore": 85.6,
    "maxScore": 98,
    "minScore": 65
  }
}
```

## 文件管理接口

### 上传项目封面
```http
POST /files/upload/image?folder=projects
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

### 上传作品图片
```http
POST /files/upload/image?folder=artworks
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

### 批量上传作品图片
```http
POST /files/upload/multiple?folder=artworks
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

## 数据导出接口

### 导出作品列表
```http
GET /artworks/export/{projectId}?format=excel
Authorization: Bearer {token}
```

### 导出评分结果
```http
GET /scores/export/{projectId}?format=excel
Authorization: Bearer {token}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| PROJECT_001 | 项目不存在 |
| PROJECT_002 | 无权限操作此项目 |
| PROJECT_003 | 项目状态不允许此操作 |
| ARTWORK_001 | 作品不存在 |
| ARTWORK_002 | 作品状态不允许修改 |
| ARTWORK_003 | Excel格式错误 |
| FILE_001 | 文件上传失败 |
| FILE_002 | 文件格式不支持 |
| FILE_003 | 文件大小超限 |
| AUTH_001 | 登录失败 |
| AUTH_002 | Token已过期 |
| AUTH_003 | 权限不足 |
