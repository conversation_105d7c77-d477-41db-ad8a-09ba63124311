import { Response } from 'express';
import { FilesService } from './files.service';
export declare class FilesController {
    private readonly filesService;
    constructor(filesService: FilesService);
    uploadImage(file: Express.Multer.File, folder?: string): Promise<{
        success: boolean;
        data: {
            url: string;
            originalName: any;
            size: any;
        };
    }>;
    uploadDocument(file: Express.Multer.File, folder?: string): Promise<{
        success: boolean;
        data: {
            url: string;
            originalName: any;
            size: any;
        };
    }>;
    uploadMultiple(files: Express.Multer.File[], folder?: string): Promise<{
        success: boolean;
        data: {
            url: string;
            originalName: any;
            size: any;
        }[];
    }>;
    generateThumbnail(imageUrl: string, width?: number, height?: number): Promise<{
        success: boolean;
        data: {
            thumbnailUrl: string;
        };
    }>;
    getFileInfo(fileUrl: string): Promise<{
        success: boolean;
        data: {
            exists: boolean;
            size?: number;
            mimeType?: string;
            lastModified?: Date;
        };
    }>;
    deleteFile(fileUrl: string): Promise<{
        success: boolean;
        message: string;
    }>;
    serveFile(filePath: string, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
}
