"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var HttpExceptionFilter_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidationException = exports.BusinessException = exports.HttpExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const uuid_1 = require("uuid");
const base_controller_1 = require("../base/base.controller");
let HttpExceptionFilter = HttpExceptionFilter_1 = class HttpExceptionFilter {
    logger = new common_1.Logger(HttpExceptionFilter_1.name);
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        const requestId = request.headers['x-request-id'] || (0, uuid_1.v4)();
        let status;
        let errorResponse;
        if (exception instanceof common_1.HttpException) {
            status = exception.getStatus();
            const exceptionResponse = exception.getResponse();
            if (typeof exceptionResponse === 'string') {
                errorResponse = this.createErrorResponse(status, exceptionResponse, base_controller_1.ErrorType.SYSTEM_ERROR, requestId);
            }
            else if (typeof exceptionResponse === 'object') {
                const response = exceptionResponse;
                if (response.message && Array.isArray(response.message)) {
                    errorResponse = this.createValidationErrorResponse(response.message, requestId);
                }
                else {
                    errorResponse = this.createErrorResponse(status, response.message || response.error || '请求处理失败', this.getErrorType(status), requestId, response.details);
                }
            }
            else {
                errorResponse = this.createErrorResponse(status, '请求处理失败', this.getErrorType(status), requestId);
            }
        }
        else {
            status = common_1.HttpStatus.INTERNAL_SERVER_ERROR;
            errorResponse = this.createErrorResponse(status, '服务器内部错误', base_controller_1.ErrorType.SYSTEM_ERROR, requestId);
            this.logger.error(`Unhandled exception: ${exception}`, exception instanceof Error ? exception.stack : undefined, `Request ID: ${requestId}`);
        }
        this.logError(request, errorResponse, exception);
        response.setHeader('X-Request-ID', requestId);
        response.setHeader('Content-Type', 'application/json; charset=utf-8');
        response.status(status).json(errorResponse);
    }
    createErrorResponse(code, message, type, requestId, details) {
        return {
            success: false,
            code,
            message,
            error: {
                type,
                details,
            },
            timestamp: Date.now(),
            requestId,
        };
    }
    createValidationErrorResponse(validationErrors, requestId) {
        const details = {};
        validationErrors.forEach(error => {
            const match = error.match(/^(\w+)\s+(.+)$/);
            if (match) {
                const [, field, message] = match;
                details[field] = message;
            }
            else {
                details['general'] = error;
            }
        });
        return {
            success: false,
            code: base_controller_1.BusinessCode.VALIDATION_ERROR,
            message: '参数验证失败',
            error: {
                type: base_controller_1.ErrorType.VALIDATION_ERROR,
                details,
            },
            timestamp: Date.now(),
            requestId,
        };
    }
    getErrorType(status) {
        if (status >= 400 && status < 500) {
            switch (status) {
                case 401:
                    return base_controller_1.ErrorType.AUTHENTICATION_ERROR;
                case 403:
                    return base_controller_1.ErrorType.AUTHORIZATION_ERROR;
                case 422:
                    return base_controller_1.ErrorType.VALIDATION_ERROR;
                default:
                    return base_controller_1.ErrorType.BUSINESS_ERROR;
            }
        }
        else if (status >= 500) {
            return base_controller_1.ErrorType.SYSTEM_ERROR;
        }
        return base_controller_1.ErrorType.SYSTEM_ERROR;
    }
    logError(request, errorResponse, exception) {
        const { method, url, ip, headers } = request;
        const userAgent = headers['user-agent'] || '';
        const userId = request.user?.id || 'anonymous';
        const logMessage = [
            `HTTP ${errorResponse.code} Error`,
            `Method: ${method}`,
            `URL: ${url}`,
            `IP: ${ip}`,
            `User: ${userId}`,
            `User-Agent: ${userAgent}`,
            `Request ID: ${errorResponse.requestId}`,
            `Message: ${errorResponse.message}`,
        ].join(' | ');
        if (errorResponse.code >= 500) {
            this.logger.error(logMessage, exception instanceof Error ? exception.stack : undefined);
        }
        else if (errorResponse.code >= 400) {
            this.logger.warn(logMessage);
        }
    }
};
exports.HttpExceptionFilter = HttpExceptionFilter;
exports.HttpExceptionFilter = HttpExceptionFilter = HttpExceptionFilter_1 = __decorate([
    (0, common_1.Catch)()
], HttpExceptionFilter);
class BusinessException extends common_1.HttpException {
    constructor(message, code = base_controller_1.BusinessCode.BAD_REQUEST, details) {
        super({
            message,
            code,
            details,
        }, code >= 500 ? common_1.HttpStatus.INTERNAL_SERVER_ERROR : common_1.HttpStatus.BAD_REQUEST);
    }
}
exports.BusinessException = BusinessException;
class ValidationException extends common_1.HttpException {
    constructor(message, details) {
        super({
            message,
            code: base_controller_1.BusinessCode.VALIDATION_ERROR,
            details,
        }, common_1.HttpStatus.UNPROCESSABLE_ENTITY);
    }
}
exports.ValidationException = ValidationException;
//# sourceMappingURL=http-exception.filter.js.map