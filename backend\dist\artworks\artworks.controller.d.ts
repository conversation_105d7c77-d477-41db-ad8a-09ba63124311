import { ArtworksService } from './artworks.service';
import { CreateArtworkDto } from './dto/create-artwork.dto';
import { UpdateArtworkDto } from './dto/update-artwork.dto';
import { BatchUploadDto } from './dto/batch-upload.dto';
import { ArtworkStatus } from './entities/artwork.entity';
export declare class ArtworksController {
    private readonly artworksService;
    constructor(artworksService: ArtworksService);
    create(createArtworkDto: CreateArtworkDto, image: Express.Multer.File, req: any): Promise<import("./entities/artwork.entity").Artwork>;
    batchUpload(batchUploadDto: BatchUploadDto, images: Express.Multer.File[], req: any): Promise<import("./entities/artwork.entity").Artwork[]>;
    importFromExcel(projectId: string, excel: Express.Multer.File, req: any): Promise<{
        success: number;
        failed: number;
        errors: string[];
    }>;
    findAll(page?: number, limit?: number, projectId?: string, status?: ArtworkStatus, authorName?: string, tags?: string): Promise<{
        data: any;
        total: any;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    findByProject(projectId: string, page?: number, limit?: number, status?: ArtworkStatus): Promise<{
        data: any;
        total: any;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    findOne(id: string): Promise<import("./entities/artwork.entity").Artwork>;
    update(id: string, updateArtworkDto: UpdateArtworkDto, image: Express.Multer.File, req: any): Promise<import("./entities/artwork.entity").Artwork>;
    updateStatus(id: string, status: ArtworkStatus, reviewComment?: string, req: any): Promise<import("./entities/artwork.entity").Artwork>;
    remove(id: string, req: any): Promise<void>;
    getProjectStats(projectId: string, req: any): Promise<{
        total: any;
        byStatus: any;
    }>;
}
