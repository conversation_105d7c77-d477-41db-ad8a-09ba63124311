import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { Project, ProjectStatus } from './entities/project.entity';
import { User, UserRole } from '../users/entities/user.entity';

@Injectable()
export class ProjectsService {
  constructor(
    @InjectRepository(Project)
    private projectRepository: Repository<Project>,
  ) {}

  async create(createProjectDto: CreateProjectDto, organizerId: string): Promise<Project> {
    const project = this.projectRepository.create({
      ...createProjectDto,
      organizerId,
    });
    return await this.projectRepository.save(project);
  }

  async findAll(options: {
    page: number;
    limit: number;
    status?: ProjectStatus;
    organizerId?: string;
    isPublic?: boolean;
  }) {
    const { page, limit, status, organizerId, isPublic } = options;
    const queryBuilder = this.projectRepository
      .createQueryBuilder('project')
      .leftJoinAndSelect('project.organizer', 'organizer');

    if (status) {
      queryBuilder.andWhere('project.status = :status', { status });
    }

    if (organizerId) {
      queryBuilder.andWhere('project.organizerId = :organizerId', { organizerId });
    }

    if (isPublic !== undefined) {
      queryBuilder.andWhere('project.isPublic = :isPublic', { isPublic: isPublic ? 1 : 0 });
    }

    queryBuilder
      .orderBy('project.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    const [projects, total] = await queryBuilder.getManyAndCount();

    return {
      data: projects,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findByOrganizer(organizerId: string, page: number = 1, limit: number = 10) {
    return this.findAll({ page, limit, organizerId });
  }

  async findOne(id: string): Promise<Project> {
    const project = await this.projectRepository.findOne({
      where: { id },
      relations: ['organizer'],
    });

    if (!project) {
      throw new NotFoundException('项目不存在');
    }

    return project;
  }

  async update(id: string, updateProjectDto: UpdateProjectDto, user: User): Promise<Project> {
    const project = await this.findOne(id);

    // 检查权限
    if (
      user.role !== UserRole.SUPER_ADMIN &&
      user.role !== UserRole.ADMIN &&
      project.organizerId !== user.id
    ) {
      throw new ForbiddenException('无权限修改此项目');
    }

    Object.assign(project, updateProjectDto);
    return await this.projectRepository.save(project);
  }

  async updateStatus(id: string, status: ProjectStatus, user: User): Promise<Project> {
    const project = await this.findOne(id);

    // 检查权限
    if (
      user.role !== UserRole.SUPER_ADMIN &&
      user.role !== UserRole.ADMIN &&
      project.organizerId !== user.id
    ) {
      throw new ForbiddenException('无权限修改此项目状态');
    }

    // 验证状态流转的合法性
    this.validateStatusTransition(project.status, status);

    project.status = status;
    return await this.projectRepository.save(project);
  }

  async remove(id: string, user: User): Promise<void> {
    const project = await this.findOne(id);

    // 检查权限
    if (
      user.role !== UserRole.SUPER_ADMIN &&
      user.role !== UserRole.ADMIN &&
      project.organizerId !== user.id
    ) {
      throw new ForbiddenException('无权限删除此项目');
    }

    // 只有准备中的项目才能删除
    if (project.status !== ProjectStatus.PREPARING) {
      throw new BadRequestException('只有准备中的项目才能删除');
    }

    await this.projectRepository.remove(project);
  }

  async generateQrCode(id: string, user: User): Promise<Project> {
    const project = await this.findOne(id);

    // 检查权限
    if (
      user.role !== UserRole.SUPER_ADMIN &&
      user.role !== UserRole.ADMIN &&
      project.organizerId !== user.id
    ) {
      throw new ForbiddenException('无权限生成二维码');
    }

    // 生成二维码逻辑（这里简化处理）
    const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(`project:${id}`)}`;
    
    project.qrCode = qrCodeUrl;
    return await this.projectRepository.save(project);
  }

  private validateStatusTransition(currentStatus: ProjectStatus, newStatus: ProjectStatus): void {
    const validTransitions = {
      [ProjectStatus.PREPARING]: [ProjectStatus.COLLECTING],
      [ProjectStatus.COLLECTING]: [ProjectStatus.REVIEWING],
      [ProjectStatus.REVIEWING]: [ProjectStatus.JUDGING],
      [ProjectStatus.JUDGING]: [ProjectStatus.DISPLAYING],
      [ProjectStatus.DISPLAYING]: [ProjectStatus.FINISHED],
      [ProjectStatus.FINISHED]: [],
    };

    if (!validTransitions[currentStatus]?.includes(newStatus)) {
      throw new BadRequestException(`无法从 ${currentStatus} 状态转换到 ${newStatus} 状态`);
    }
  }
}
