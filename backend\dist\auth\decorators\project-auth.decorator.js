"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IsProjectManager = exports.IsProjectOwner = exports.HasProjectPermission = exports.CurrentProjectPermissions = exports.CurrentProjectRole = exports.ProjectId = exports.ProjectMember = exports.ProjectJudge = exports.ProjectManager = exports.ProjectOwner = exports.ProjectRoleAuth = exports.ProjectPermission = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const auth_guard_1 = require("../guards/auth.guard");
const project_permission_guard_1 = require("../guards/project-permission.guard");
const user_roles_entity_1 = require("../../users/entities/user-roles.entity");
const ProjectPermission = (permission) => {
    return (0, common_1.applyDecorators)((0, common_1.SetMetadata)('project_permission', permission), (0, common_1.UseGuards)(auth_guard_1.RequiredAuthGuard, project_permission_guard_1.ProjectPermissionGuard), (0, swagger_1.ApiBearerAuth)(), (0, swagger_1.ApiUnauthorizedResponse)({ description: '未授权访问' }), (0, swagger_1.ApiForbiddenResponse)({ description: '项目权限不足' }));
};
exports.ProjectPermission = ProjectPermission;
const ProjectRoleAuth = (roles) => {
    return (0, common_1.applyDecorators)((0, common_1.SetMetadata)('project_roles', roles), (0, common_1.UseGuards)(auth_guard_1.RequiredAuthGuard, project_permission_guard_1.ProjectPermissionGuard), (0, swagger_1.ApiBearerAuth)(), (0, swagger_1.ApiUnauthorizedResponse)({ description: '未授权访问' }), (0, swagger_1.ApiForbiddenResponse)({ description: '项目角色权限不足' }));
};
exports.ProjectRoleAuth = ProjectRoleAuth;
const ProjectOwner = () => {
    return (0, exports.ProjectRoleAuth)([user_roles_entity_1.ProjectRole.PROJECT_OWNER]);
};
exports.ProjectOwner = ProjectOwner;
const ProjectManager = () => {
    return (0, exports.ProjectRoleAuth)([user_roles_entity_1.ProjectRole.PROJECT_OWNER, user_roles_entity_1.ProjectRole.PROJECT_ADMIN]);
};
exports.ProjectManager = ProjectManager;
const ProjectJudge = () => {
    return (0, exports.ProjectRoleAuth)([
        user_roles_entity_1.ProjectRole.PROJECT_JUDGE,
        user_roles_entity_1.ProjectRole.PROJECT_OWNER,
        user_roles_entity_1.ProjectRole.PROJECT_ADMIN
    ]);
};
exports.ProjectJudge = ProjectJudge;
const ProjectMember = () => {
    return (0, common_1.applyDecorators)((0, common_1.SetMetadata)('project_member', true), (0, common_1.UseGuards)(auth_guard_1.RequiredAuthGuard, project_permission_guard_1.ProjectPermissionGuard), (0, swagger_1.ApiBearerAuth)(), (0, swagger_1.ApiUnauthorizedResponse)({ description: '未授权访问' }), (0, swagger_1.ApiForbiddenResponse)({ description: '非项目成员' }));
};
exports.ProjectMember = ProjectMember;
exports.ProjectId = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    return request.params.projectId || request.params.id;
});
exports.CurrentProjectRole = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    return request.projectRoles || [];
});
exports.CurrentProjectPermissions = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    return request.projectPermissions || {};
});
const HasProjectPermission = (permission) => {
    return (0, common_1.createParamDecorator)((data, ctx) => {
        const request = ctx.switchToHttp().getRequest();
        const permissions = request.projectPermissions || {};
        switch (permission) {
            case 'manage':
                return permissions.canManageProject;
            case 'invite':
                return permissions.canInviteUsers;
            case 'artwork_manage':
                return permissions.canManageArtworks;
            case 'score':
                return permissions.canScore;
            case 'view_scores':
                return permissions.canViewScores;
            case 'export':
                return permissions.canExportData;
            case 'moderate':
                return permissions.canModerateContent;
            case 'schedule':
                return permissions.canManageSchedule;
            default:
                return permissions.customPermissions?.includes(permission) || false;
        }
    })();
};
exports.HasProjectPermission = HasProjectPermission;
exports.IsProjectOwner = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const roles = request.projectRoles || [];
    return roles.includes(user_roles_entity_1.ProjectRole.PROJECT_OWNER);
});
exports.IsProjectManager = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const roles = request.projectRoles || [];
    return roles.includes(user_roles_entity_1.ProjectRole.PROJECT_OWNER) ||
        roles.includes(user_roles_entity_1.ProjectRole.PROJECT_ADMIN);
});
//# sourceMappingURL=project-auth.decorator.js.map