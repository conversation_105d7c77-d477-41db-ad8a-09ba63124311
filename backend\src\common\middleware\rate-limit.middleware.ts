import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

/**
 * 请求限流中间件
 * 基于IP和用户ID进行请求频率限制
 */
@Injectable()
export class RateLimitMiddleware implements NestMiddleware {
  private readonly requests = new Map<string, { count: number; resetTime: number }>();
  
  // 不同用户角色的限流配置
  private readonly limits = {
    anonymous: { requests: 100, window: 60 * 1000 }, // 匿名用户：100次/分钟
    user: { requests: 200, window: 60 * 1000 },      // 普通用户：200次/分钟
    judge: { requests: 300, window: 60 * 1000 },     // 评委：300次/分钟
    organizer: { requests: 500, window: 60 * 1000 }, // 办展方：500次/分钟
    admin: { requests: 1000, window: 60 * 1000 },    // 管理员：1000次/分钟
    super_admin: { requests: 2000, window: 60 * 1000 }, // 超级管理员：2000次/分钟
  };

  use(req: Request, res: Response, next: NextFunction) {
    const key = this.getKey(req);
    const limit = this.getLimit(req);
    const now = Date.now();

    // 获取当前请求记录
    let record = this.requests.get(key);

    // 如果没有记录或者时间窗口已过期，创建新记录
    if (!record || now > record.resetTime) {
      record = {
        count: 1,
        resetTime: now + limit.window,
      };
      this.requests.set(key, record);
    } else {
      record.count++;
    }

    // 设置响应头
    res.setHeader('X-RateLimit-Limit', limit.requests);
    res.setHeader('X-RateLimit-Remaining', Math.max(0, limit.requests - record.count));
    res.setHeader('X-RateLimit-Reset', Math.ceil(record.resetTime / 1000));

    // 检查是否超过限制
    if (record.count > limit.requests) {
      res.status(429).json({
        success: false,
        code: 429,
        message: '请求频率过高，请稍后再试',
        error: {
          type: 'RATE_LIMIT_ERROR',
          details: {
            limit: limit.requests,
            window: limit.window / 1000,
            retryAfter: Math.ceil((record.resetTime - now) / 1000),
          },
        },
        timestamp: Date.now(),
        requestId: req.headers['x-request-id'] || 'unknown',
      });
      return;
    }

    next();
  }

  /**
   * 生成限流键
   */
  private getKey(req: Request): string {
    const user = (req as any).user;
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    
    if (user) {
      return `user:${user.id}`;
    }
    
    return `ip:${ip}`;
  }

  /**
   * 获取限流配置
   */
  private getLimit(req: Request): { requests: number; window: number } {
    const user = (req as any).user;
    
    if (!user) {
      return this.limits.anonymous;
    }
    
    return this.limits[user.role] || this.limits.user;
  }

  /**
   * 清理过期记录
   */
  private cleanup() {
    const now = Date.now();
    for (const [key, record] of this.requests.entries()) {
      if (now > record.resetTime) {
        this.requests.delete(key);
      }
    }
  }
}

/**
 * CORS中间件
 * 处理跨域请求
 */
@Injectable()
export class CorsMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const origin = req.headers.origin;
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:8080',
      'https://admin.artscore.com',
      'https://org.artscore.com',
      'https://mp.artscore.com',
    ];

    // 检查来源是否被允许
    if (allowedOrigins.includes(origin)) {
      res.setHeader('Access-Control-Allow-Origin', origin);
    }

    res.setHeader('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'X-Request-ID',
      'X-Client-Version',
      'X-Platform',
    ].join(','));
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    res.setHeader('Access-Control-Max-Age', '86400'); // 24小时

    // 处理预检请求
    if (req.method === 'OPTIONS') {
      res.status(200).end();
      return;
    }

    next();
  }
}

/**
 * 请求ID中间件
 * 为每个请求生成唯一ID
 */
@Injectable()
export class RequestIdMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const requestId = req.headers['x-request-id'] || this.generateId();
    req.headers['x-request-id'] = requestId as string;
    res.setHeader('X-Request-ID', requestId);
    next();
  }

  private generateId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 安全中间件
 * 处理安全相关的请求头和验证
 */
@Injectable()
export class SecurityMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // 检查User-Agent
    const userAgent = req.headers['user-agent'];
    if (!userAgent || userAgent.length < 10) {
      res.status(400).json({
        success: false,
        code: 400,
        message: '无效的请求',
        error: {
          type: 'SECURITY_ERROR',
          details: 'Invalid User-Agent',
        },
        timestamp: Date.now(),
        requestId: req.headers['x-request-id'] || 'unknown',
      });
      return;
    }

    // 检查Content-Length（防止过大的请求体）
    const contentLength = parseInt(req.headers['content-length'] || '0');
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (contentLength > maxSize) {
      res.status(413).json({
        success: false,
        code: 413,
        message: '请求体过大',
        error: {
          type: 'SECURITY_ERROR',
          details: `Content-Length exceeds ${maxSize} bytes`,
        },
        timestamp: Date.now(),
        requestId: req.headers['x-request-id'] || 'unknown',
      });
      return;
    }

    // 检查可疑的请求头
    const suspiciousHeaders = ['x-forwarded-for', 'x-real-ip'];
    for (const header of suspiciousHeaders) {
      const value = req.headers[header];
      if (value && typeof value === 'string' && value.includes('<script>')) {
        res.status(400).json({
          success: false,
          code: 400,
          message: '无效的请求头',
          error: {
            type: 'SECURITY_ERROR',
            details: 'Suspicious header detected',
          },
          timestamp: Date.now(),
          requestId: req.headers['x-request-id'] || 'unknown',
        });
        return;
      }
    }

    next();
  }
}
