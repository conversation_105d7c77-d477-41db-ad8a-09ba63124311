{"version": 3, "file": "project-permission.service.js", "sourceRoot": "", "sources": ["../../../src/auth/services/project-permission.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAsG;AACtG,6CAAmD;AACnD,qCAAqC;AACrC,kEAAwD;AACxD,8EAIgD;AAChD,uEAAkE;AAO3D,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IAGzB;IAEA;IACA;IALV,YAEU,cAAgC,EAEhC,qBAAkD,EAClD,qBAA4C;QAH5C,mBAAc,GAAd,cAAc,CAAkB;QAEhC,0BAAqB,GAArB,qBAAqB,CAA6B;QAClD,0BAAqB,GAArB,qBAAqB,CAAuB;IACnD,CAAC;IAKJ,KAAK,CAAC,mBAAmB,CACvB,SAAiB,EACjB,MAAc,EACd,IAAiB,EACjB,WAAgB,EAChB,SAAiB,EACjB,QAAc;QAGd,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,0BAAiB,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC;QAGD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;QAClF,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,2BAAkB,CAAC,YAAY,CAAC,CAAC;QAC7C,CAAC;QAGD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YAC5D,KAAK,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE;SACnC,CAAC,CAAC;QAEH,IAAI,YAAY,IAAI,YAAY,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;YACvD,MAAM,IAAI,0BAAiB,CAAC,cAAc,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,YAAY,IAAI,YAAY,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACtD,MAAM,IAAI,0BAAiB,CAAC,YAAY,CAAC,CAAC;QAC5C,CAAC;QAGD,IAAI,YAAY,EAAE,CAAC;YACjB,YAAY,CAAC,WAAW,GAAG,WAAW,CAAC;YACvC,YAAY,CAAC,QAAQ,GAAG;gBACtB,GAAG,YAAY,CAAC,QAAQ;gBACxB,GAAG,QAAQ;gBACX,SAAS;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YACF,YAAY,CAAC,MAAM,GAAG,SAAS,CAAC;YAChC,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC7D,CAAC;QAGD,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;YACpD,MAAM;YACN,SAAS;YACT,IAAI;YACJ,WAAW;YACX,QAAQ,EAAE;gBACR,SAAS;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,GAAG,QAAQ;aACZ;YACD,MAAM,EAAE,SAAS;SAClB,CAAC,CAAC;QAEH,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC5D,CAAC;IAKD,KAAK,CAAC,uBAAuB,CAAC,MAAc,EAAE,SAAiB,EAAE,IAAkB;QACjF,MAAM,cAAc,GAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;QACrE,IAAI,IAAI,EAAE,CAAC;YACT,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YAC1D,KAAK,EAAE,cAAc;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,0BAAiB,CAAC,WAAW,CAAC,CAAC;QAC3C,CAAC;QAGD,IAAI,UAAU,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;YAC9D,UAAU,CAAC,MAAM,GAAG,SAAS,CAAC;YAC9B,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClD,MAAM,IAAI,2BAAkB,CAAC,OAAO,CAAC,CAAC;QACxC,CAAC;QAED,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC;QAC/B,UAAU,CAAC,QAAQ,GAAG;YACpB,GAAG,UAAU,CAAC,QAAQ;YACtB,UAAU,EAAE,IAAI,IAAI,EAAE;SACvB,CAAC;QAEF,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACpD,CAAC;IAKD,KAAK,CAAC,wBAAwB,CAAC,MAAc,EAAE,SAAiB,EAAE,IAAkB;QAClF,MAAM,cAAc,GAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;QACrE,IAAI,IAAI,EAAE,CAAC;YACT,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YAC1D,KAAK,EAAE,cAAc;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,0BAAiB,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC;QAED,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC;QAC/B,UAAU,CAAC,QAAQ,GAAG;YACpB,GAAG,UAAU,CAAC,QAAQ;YACtB,UAAU,EAAE,IAAI,IAAI,EAAE;SACvB,CAAC;QAEF,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACpD,CAAC;IAKD,KAAK,CAAC,4BAA4B,CAChC,MAAc,EACd,SAAiB,EACjB,WAAgB,EAChB,SAAiB;QAGjB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;QAClF,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,2BAAkB,CAAC,SAAS,CAAC,CAAC;QAC1C,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YAC3D,KAAK,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE;SACjD,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,0BAAiB,CAAC,SAAS,CAAC,CAAC;QACzC,CAAC;QAED,WAAW,CAAC,WAAW,GAAG,WAAW,CAAC;QACtC,WAAW,CAAC,QAAQ,GAAG;YACrB,GAAG,WAAW,CAAC,QAAQ;YACvB,aAAa,EAAE,SAAS;YACxB,aAAa,EAAE,IAAI,IAAI,EAAE;SAC1B,CAAC;QAEF,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACrD,CAAC;IAKD,KAAK,CAAC,mBAAmB,CACvB,MAAc,EACd,SAAiB,EACjB,SAAiB,EACjB,MAAe;QAGf,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;QAClF,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,2BAAkB,CAAC,SAAS,CAAC,CAAC;QAC1C,CAAC;QAGD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YACzD,KAAK,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,+BAAW,CAAC,aAAa,EAAE,MAAM,EAAE,UAAU,EAAE;SAClF,CAAC,CAAC;QAEH,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,IAAI,2BAAkB,CAAC,WAAW,CAAC,CAAC;QAC5C,CAAC;QAED,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CACrC,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,EACzC;YACE,MAAM,EAAE,SAAS;YACjB,QAAQ,EAAE,GAAG,EAAE,CAAC,sCAAsC,SAAS,sBAAsB,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,mBAAmB,MAAM,IAAI,EAAE,IAAI;SACjJ,CACF,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,mBAAmB,CAAC,MAAc,EAAE,SAAiB;QACzD,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;YAC3C,KAAK,EAAE;gBACL,MAAM;gBACN,SAAS;gBACT,MAAM,EAAE,UAAU;aACnB;YACD,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;SAC5B,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,iBAAiB,CACrB,SAAiB,EACjB,OAKC;QAED,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,UAAU,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;QAE1E,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB;aAC5C,kBAAkB,CAAC,IAAI,CAAC;aACxB,iBAAiB,CAAC,SAAS,EAAE,MAAM,CAAC;aACpC,KAAK,CAAC,2BAA2B,EAAE,EAAE,SAAS,EAAE,CAAC;aACjD,QAAQ,CAAC,qBAAqB,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QAE/C,IAAI,IAAI,EAAE,CAAC;YACT,YAAY,CAAC,QAAQ,CAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;QACrD,CAAC;QAED,YAAY;aACT,OAAO,CAAC,cAAc,EAAE,KAAK,CAAC;aAC9B,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;aACxB,IAAI,CAAC,KAAK,CAAC,CAAC;QAEf,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM,YAAY,CAAC,eAAe,EAAE,CAAC;QAE3D,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IACtC,CAAC;IAKD,KAAK,CAAC,oBAAoB,CACxB,MAAc,EACd,SAAiB,EACjB,UAAkB;QAGlB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YAC7C,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;YACrB,SAAS,EAAE,CAAC,aAAa,CAAC;SAC3B,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,KAAK,CAAC;QACf,CAAC;QAGD,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QACvF,IAAI,SAAS,CAAC,QAAQ,CAAC,8BAAU,CAAC,WAAW,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,8BAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YACvF,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAEvE,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,KAAK,CAAC;QACf,CAAC;QAGD,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;YACvC,IAAI,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,UAAU,CAAC,EAAE,CAAC;gBAClD,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAKO,eAAe,CAAC,WAA4B,EAAE,UAAkB;QACtE,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,IAAI,EAAE,CAAC;QAGlD,IAAI,WAAW,CAAC,IAAI,KAAK,+BAAW,CAAC,aAAa,EAAE,CAAC;YACnD,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,QAAQ,UAAU,EAAE,CAAC;YACnB,KAAK,QAAQ;gBACX,OAAO,WAAW,CAAC,gBAAgB,IAAI,WAAW,CAAC,IAAI,KAAK,+BAAW,CAAC,aAAa,CAAC;YACxF,KAAK,QAAQ;gBACX,OAAO,WAAW,CAAC,cAAc,CAAC;YACpC,KAAK,gBAAgB;gBACnB,OAAO,WAAW,CAAC,iBAAiB,CAAC;YACvC,KAAK,OAAO;gBACV,OAAO,WAAW,CAAC,QAAQ,CAAC;YAC9B,KAAK,aAAa;gBAChB,OAAO,WAAW,CAAC,aAAa,CAAC;YACnC,KAAK,QAAQ;gBACX,OAAO,WAAW,CAAC,aAAa,CAAC;YACnC,KAAK,UAAU;gBACb,OAAO,WAAW,CAAC,kBAAkB,CAAC;YACxC,KAAK,UAAU;gBACb,OAAO,WAAW,CAAC,iBAAiB,CAAC;YACvC;gBACE,OAAO,WAAW,CAAC,iBAAiB,EAAE,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC;QACxE,CAAC;IACH,CAAC;IAKD,gBAAgB,CAAC,YAA+B;QAC9C,MAAM,iBAAiB,GAAG;YACxB,gBAAgB,EAAE,KAAK;YACvB,cAAc,EAAE,KAAK;YACrB,iBAAiB,EAAE,KAAK;YACxB,QAAQ,EAAE,KAAK;YACf,aAAa,EAAE,KAAK;YACpB,aAAa,EAAE,KAAK;YACpB,kBAAkB,EAAE,KAAK;YACzB,iBAAiB,EAAE,KAAK;YACxB,iBAAiB,EAAE,EAAE;SACtB,CAAC;QAEF,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;YAChC,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC;YAG3C,iBAAiB,CAAC,gBAAgB,GAAG,iBAAiB,CAAC,gBAAgB,IAAI,WAAW,CAAC,gBAAgB,CAAC;YACxG,iBAAiB,CAAC,cAAc,GAAG,iBAAiB,CAAC,cAAc,IAAI,WAAW,CAAC,cAAc,CAAC;YAClG,iBAAiB,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,iBAAiB,IAAI,WAAW,CAAC,iBAAiB,CAAC;YAC3G,iBAAiB,CAAC,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,IAAI,WAAW,CAAC,QAAQ,CAAC;YAChF,iBAAiB,CAAC,aAAa,GAAG,iBAAiB,CAAC,aAAa,IAAI,WAAW,CAAC,aAAa,CAAC;YAC/F,iBAAiB,CAAC,aAAa,GAAG,iBAAiB,CAAC,aAAa,IAAI,WAAW,CAAC,aAAa,CAAC;YAC/F,iBAAiB,CAAC,kBAAkB,GAAG,iBAAiB,CAAC,kBAAkB,IAAI,WAAW,CAAC,kBAAkB,CAAC;YAC9G,iBAAiB,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,iBAAiB,IAAI,WAAW,CAAC,iBAAiB,CAAC;YAG3G,IAAI,WAAW,CAAC,iBAAiB,EAAE,CAAC;gBAClC,iBAAiB,CAAC,iBAAiB,GAAG;oBACpC,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,iBAAiB,CAAC,iBAAiB,EAAE,GAAG,WAAW,CAAC,iBAAiB,CAAC,CAAC;iBACvF,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAKD,KAAK,CAAC,gBAAgB,CACpB,SAAiB,EACjB,WAKE,EACF,SAAiB;QAEjB,MAAM,OAAO,GAAsB,EAAE,CAAC;QAEtC,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACrC,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW;oBACxC,MAAM,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAE1E,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAChD,SAAS,EACT,UAAU,CAAC,MAAM,EACjB,UAAU,CAAC,IAAI,EACf,WAAW,EACX,SAAS,EACT;oBACE,OAAO,EAAE,UAAU,CAAC,OAAO;oBAC3B,eAAe,EAAE,IAAI;iBACtB,CACF,CAAC;gBAEF,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,UAAU,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAC9E,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,KAAK,CAAC,yBAAyB,CAAC,SAAiB;QAC/C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,qBAAqB;aAC/C,kBAAkB,CAAC,IAAI,CAAC;aACxB,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC;aACzB,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;aAC9B,KAAK,CAAC,2BAA2B,EAAE,EAAE,SAAS,EAAE,CAAC;aACjD,QAAQ,CAAC,qBAAqB,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;aACvD,OAAO,CAAC,SAAS,CAAC;aAClB,UAAU,EAAE,CAAC;QAEhB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB;aACjD,kBAAkB,CAAC,IAAI,CAAC;aACxB,MAAM,CAAC,WAAW,EAAE,QAAQ,CAAC;aAC7B,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;aAC9B,KAAK,CAAC,2BAA2B,EAAE,EAAE,SAAS,EAAE,CAAC;aACjD,OAAO,CAAC,WAAW,CAAC;aACpB,UAAU,EAAE,CAAC;QAEhB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC;YAC1D,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE;SACzC,CAAC,CAAC;QAEH,OAAO;YACL,gBAAgB,EAAE,SAAS;YAC3B,kBAAkB,EAAE,WAAW;YAC/B,YAAY;YACZ,SAAS;SACV,CAAC;IACJ,CAAC;CACF,CAAA;AApbY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,kBAAI,CAAC,CAAA;IAEtB,WAAA,IAAA,0BAAgB,EAAC,mCAAe,CAAC,CAAA;yDADV,oBAAU,oBAAV,oBAAU,oDAEH,oBAAU,oBAAV,oBAAU,gCACV,+CAAqB;GAN3C,wBAAwB,CAobpC"}