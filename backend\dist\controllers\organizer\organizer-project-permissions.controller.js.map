{"version": 3, "file": "organizer-project-permissions.controller.js", "sourceRoot": "", "sources": ["../../../src/controllers/organizer/organizer-project-permissions.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAUwB;AACxB,6CAAqE;AACrE,uEAAmE;AACnE,yEAAqE;AACrE,yFAOsD;AACtD,yEAA2E;AAC3E,kEAAwD;AACxD,+FAA0F;AAC1F,yFAAoF;AACpF,6EAM6C;AAStC,IAAM,qCAAqC,GAA3C,MAAM,qCAAsC,SAAQ,gCAAc;IAEpD;IACA;IAFnB,YACmB,wBAAkD,EAClD,qBAA4C;QAE7D,KAAK,EAAE,CAAC;QAHS,6BAAwB,GAAxB,wBAAwB,CAA0B;QAClD,0BAAqB,GAArB,qBAAqB,CAAuB;IAG/D,CAAC;IAWK,AAAN,KAAK,CAAC,UAAU,CACD,SAAiB,EACtB,SAAiC,EAC/B,SAAiB,EAChB,GAAG;QAEd,IAAI,CAAC;YAEH,MAAM,WAAW,GAAG,SAAS,CAAC,WAAW;gBACvC,MAAM,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAEzE,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,mBAAmB,CACxE,SAAS,EACT,SAAS,CAAC,MAAM,EAChB,SAAS,CAAC,IAAI,EACd,WAAW,EACX,SAAS,EACT;gBACE,OAAO,EAAE,SAAS,CAAC,OAAO;gBAC1B,eAAe,EAAE,SAAS,CAAC,eAAe;gBAC1C,gBAAgB,EAAE,SAAS,CAAC,gBAAgB;aAC7C,CACF,CAAC;YAEF,OAAO,IAAI,CAAC,OAAO,CACjB;gBACE,UAAU;gBACV,SAAS;gBACT,WAAW,EAAE,SAAS,CAAC,IAAI;gBAC3B,WAAW;aACZ,EACD,UAAU,EACV,GAAG,CACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YACrC,CAAC;YACD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACvE,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YAC3C,CAAC;YACD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBACxC,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;YAC3C,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IASK,AAAN,KAAK,CAAC,gBAAgB,CACP,SAAiB,EACtB,QAA6B,EAC3B,SAAiB,EAChB,GAAG;QAEd,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,CAClE,SAAS,EACT,QAAQ,CAAC,WAAW,EACpB,SAAS,CACV,CAAC;YAEF,OAAO,IAAI,CAAC,OAAO,CACjB;gBACE,UAAU,EAAE,OAAO,CAAC,MAAM;gBAC1B,KAAK,EAAE,QAAQ,CAAC,WAAW,CAAC,MAAM;gBAClC,OAAO;gBACP,SAAS;aACV,EACD,QAAQ,EACR,GAAG,CACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IASK,AAAN,KAAK,CAAC,iBAAiB,CACR,SAAiB,EACrB,KAA6B,EACT,WAAgB,EAClC,GAAG;QAEd,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAGvF,MAAM,cAAc,GAAG,WAAW,CAAC,aAAa,IAAI,WAAW,CAAC,gBAAgB,CAAC;YAEjF,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBACjD,EAAE,EAAE,MAAM,CAAC,EAAE;gBACb,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,IAAI,EAAE;oBACJ,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;oBAClB,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ;oBAC9B,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ;oBAC9B,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ;oBAC9B,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM;iBAC3B;gBAED,WAAW,EAAE,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;gBAC5D,QAAQ,EAAE;oBACR,SAAS,EAAE,MAAM,CAAC,QAAQ,EAAE,SAAS;oBACrC,UAAU,EAAE,MAAM,CAAC,QAAQ,EAAE,UAAU;oBACvC,eAAe,EAAE,MAAM,CAAC,QAAQ,EAAE,eAAe;iBAClD;gBACD,SAAS,EAAE,MAAM,CAAC,SAAS;aAC5B,CAAC,CAAC,CAAC;YAEJ,OAAO,IAAI,CAAC,SAAS,CACnB,eAAe,EACf,MAAM,CAAC,KAAK,EACZ,MAAM,CAAC,IAAI,EACX,MAAM,CAAC,KAAK,EACZ,YAAY,EACZ,GAAG,CACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAUK,AAAN,KAAK,CAAC,uBAAuB,CACd,SAAiB,EACb,YAAoB,EAC7B,SAAsC,EACpC,SAAiB,EAChB,GAAG;QAEd,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,wBAAwB,CAAC,4BAA4B,CAC9D,YAAY,EACZ,SAAS,EACT,SAAS,CAAC,WAAW,EACrB,SAAS,CACV,CAAC;YAEF,OAAO,IAAI,CAAC,OAAO,CACjB;gBACE,SAAS;gBACT,YAAY;gBACZ,cAAc,EAAE,SAAS,CAAC,WAAW;gBACrC,SAAS;aACV,EACD,UAAU,EACV,GAAG,CACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACpC,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;YACvC,CAAC;YACD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACrC,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;YACxC,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAUK,AAAN,KAAK,CAAC,mBAAmB,CACV,SAAiB,EACb,YAAoB,EAC7B,SAAiC,EAC/B,SAAiB,EAChB,GAAG;QAEd,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,wBAAwB,CAAC,mBAAmB,CACrD,YAAY,EACZ,SAAS,EACT,SAAS,EACT,SAAS,CAAC,MAAM,CACjB,CAAC;YAEF,OAAO,IAAI,CAAC,OAAO,CACjB;gBACE,SAAS;gBACT,aAAa,EAAE,YAAY;gBAC3B,SAAS;gBACT,MAAM,EAAE,SAAS,CAAC,MAAM;aACzB,EACD,UAAU,EACV,GAAG,CACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBACxC,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;YAC1C,CAAC;YACD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACrC,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;YACxC,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IASK,AAAN,KAAK,CAAC,gBAAgB,CACP,SAAiB,EACb,MAAc,EACpB,GAAG;QAEd,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,mBAAmB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAEzF,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;YACvC,CAAC;YAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAEhF,OAAO,IAAI,CAAC,OAAO,CACjB;gBACE,MAAM;gBACN,SAAS;gBACT,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACxB,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,SAAS,EAAE,IAAI,CAAC,SAAS;iBAC1B,CAAC,CAAC;gBACH,iBAAiB;gBACjB,UAAU,EAAE,KAAK,CAAC,MAAM;aACzB,EACD,YAAY,EACZ,GAAG,CACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IASK,AAAN,KAAK,CAAC,yBAAyB,CAChB,SAAiB,EACnB,GAAG;QAEd,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC;YAEvF,OAAO,IAAI,CAAC,OAAO,CACjB,KAAK,EACL,YAAY,EACZ,GAAG,CACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IASK,AAAN,KAAK,CAAC,sBAAsB,CAAgB,IAAY,EAAa,GAAG;QACtE,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,sBAAsB,CAAC,IAAW,CAAC,CAAC;YAEvF,OAAO,IAAI,CAAC,OAAO,CACjB,SAAS,EACT,UAAU,EACV,GAAG,CACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IASK,AAAN,KAAK,CAAC,gBAAgB,CACP,SAAiB,EACf,IAAU,EACH,KAAe,EACR,WAAgB,EACzB,SAAkB,EAC3B,GAAG;QAEd,IAAI,CAAC;YACH,OAAO,IAAI,CAAC,OAAO,CACjB;gBACE,SAAS;gBACT,IAAI,EAAE;oBACJ,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,WAAW,EAAE,IAAI,CAAC,KAAK;iBACxB;gBACD,YAAY,EAAE,KAAK;gBACnB,WAAW;gBACX,SAAS;gBACT,YAAY,EAAE;oBACZ,cAAc,EAAE,WAAW,CAAC,cAAc;oBAC1C,gBAAgB,EAAE,WAAW,CAAC,gBAAgB;oBAC9C,qBAAqB,EAAE,WAAW,CAAC,aAAa,IAAI,WAAW,CAAC,gBAAgB;oBAChF,aAAa,EAAE,WAAW,CAAC,aAAa;iBACzC;aACF,EACD,cAAc,EACd,GAAG,CACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AApXY,sFAAqC;AAiB1C;IANL,IAAA,0CAAiB,EAAC,QAAQ,CAAC;IAC3B,IAAA,aAAI,EAAC,QAAQ,CAAC;IACd,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACrC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC;IACnD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC;IAClD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC;IAElD,WAAA,IAAA,kCAAS,GAAE,CAAA;IACX,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,uBAAM,GAAE,CAAA;IACR,WAAA,IAAA,gBAAO,GAAE,CAAA;;6CAFS,4CAAsB;;uEA4C1C;AASK;IAJL,IAAA,0CAAiB,EAAC,QAAQ,CAAC;IAC3B,IAAA,aAAI,EAAC,cAAc,CAAC;IACpB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;IACvC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC;IAEjD,WAAA,IAAA,kCAAS,GAAE,CAAA;IACX,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,uBAAM,GAAE,CAAA;IACR,WAAA,IAAA,gBAAO,GAAE,CAAA;;6CAFQ,yCAAmB;;6EAwBtC;AASK;IAJL,IAAA,uCAAc,GAAE;IAChB,IAAA,YAAG,EAAC,SAAS,CAAC;IACd,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACrC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAE/C,WAAA,IAAA,kCAAS,GAAE,CAAA;IACX,WAAA,IAAA,cAAK,GAAE,CAAA;IACP,WAAA,IAAA,kDAAyB,GAAE,CAAA;IAC3B,WAAA,IAAA,gBAAO,GAAE,CAAA;;6CAFM,4CAAsB;;8EA2CvC;AAUK;IALL,IAAA,uCAAc,GAAE;IAChB,IAAA,cAAK,EAAC,6BAA6B,CAAC;IACpC,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IACnC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC;IACnD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC;IAElD,WAAA,IAAA,kCAAS,GAAE,CAAA;IACX,WAAA,IAAA,cAAK,EAAC,QAAQ,CAAC,CAAA;IACf,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,uBAAM,GAAE,CAAA;IACR,WAAA,IAAA,gBAAO,GAAE,CAAA;;qDAFS,iDAA2B;;oFA+B/C;AAUK;IALL,IAAA,uCAAc,GAAE;IAChB,IAAA,eAAM,EAAC,iBAAiB,CAAC;IACzB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IACnC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC;IACnD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;IAEpD,WAAA,IAAA,kCAAS,GAAE,CAAA;IACX,WAAA,IAAA,cAAK,EAAC,QAAQ,CAAC,CAAA;IACf,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,uBAAM,GAAE,CAAA;IACR,WAAA,IAAA,gBAAO,GAAE,CAAA;;qDAFS,4CAAsB;;gFA+B1C;AASK;IAJL,IAAA,uCAAc,GAAE;IAChB,IAAA,YAAG,EAAC,iBAAiB,CAAC;IACtB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACrC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAE/C,WAAA,IAAA,kCAAS,GAAE,CAAA;IACX,WAAA,IAAA,cAAK,EAAC,QAAQ,CAAC,CAAA;IACf,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;6EAgCX;AASK;IAJL,IAAA,uCAAc,GAAE;IAChB,IAAA,YAAG,EAAC,YAAY,CAAC;IACjB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACrC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAE/C,WAAA,IAAA,kCAAS,GAAE,CAAA;IACX,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;sFAaX;AASK;IAJL,IAAA,uCAAc,GAAE;IAChB,IAAA,YAAG,EAAC,WAAW,CAAC;IAChB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IACtC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACpB,WAAA,IAAA,cAAK,EAAC,MAAM,CAAC,CAAA;IAAgB,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;mFAYnE;AASK;IAJL,IAAA,uCAAc,GAAE;IAChB,IAAA,YAAG,EAAC,gBAAgB,CAAC;IACrB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;IACvC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAE/C,WAAA,IAAA,kCAAS,GAAE,CAAA;IACX,WAAA,IAAA,4BAAW,GAAE,CAAA;IACb,WAAA,IAAA,2CAAkB,GAAE,CAAA;IACpB,WAAA,IAAA,kDAAyB,GAAE,CAAA;IAC3B,WAAA,IAAA,yCAAgB,GAAE,CAAA;IAClB,WAAA,IAAA,gBAAO,GAAE,CAAA;;6CAJW,kBAAI;;6EA+B1B;gDAnXU,qCAAqC;IAHjD,IAAA,iBAAO,EAAC,YAAY,CAAC;IACrB,IAAA,mBAAU,EAAC,2CAA2C,CAAC;IACvD,IAAA,8BAAa,GAAE;qCAG+B,qDAAwB;QAC3B,+CAAqB;GAHpD,qCAAqC,CAoXjD"}