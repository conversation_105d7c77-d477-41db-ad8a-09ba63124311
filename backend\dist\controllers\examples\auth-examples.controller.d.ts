import { BaseController } from '../../common/base/base.controller';
import { User, UserRole as UserRoleEnum } from '../../users/entities/user.entity';
export declare class AuthExamplesController extends BaseController {
    publicEndpoint(req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        message: string;
        timestamp: string;
        hasUser: boolean;
    }>>;
    protectedEndpoint(user: User, userId: string, userRole: string, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        message: string;
        user: {
            id: string;
            username: string;
            role: UserRoleEnum;
        };
        userId: string;
        userRole: string;
    }>>;
    optionalEndpoint(user: User | null, isAuth: boolean, safeUser: any, clientIP: string, userAgent: string, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        message: string;
        user: {
            id: string;
            username: string;
            nickname: string;
            role: UserRoleEnum;
        };
        safeUser: any;
        isAuthenticated: boolean;
        personalizedContent: string[];
        clientInfo: {
            ip: string;
            userAgent: string;
        };
    }> | import("../../common/base/base.controller").ApiResponse<{
        message: string;
        isAuthenticated: boolean;
        publicContent: string[];
        loginTip: string;
        clientInfo: {
            ip: string;
            userAgent: string;
        };
    }>>;
    adminOnlyEndpoint(user: User, isAdmin: boolean, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        message: string;
        adminUser: {
            id: string;
            username: string;
            role: UserRoleEnum;
        };
        isAdmin: boolean;
        adminFeatures: string[];
    }>>;
    superAdminOnlyEndpoint(user: User, isSuperAdmin: boolean, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        message: string;
        superAdminUser: {
            id: string;
            username: string;
            role: UserRoleEnum;
        };
        isSuperAdmin: boolean;
        superAdminFeatures: string[];
    }>>;
    organizerOnlyEndpoint(user: User, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        message: string;
        organizerUser: {
            id: string;
            username: string;
            role: UserRoleEnum;
        };
        organizerFeatures: string[];
    }>>;
    judgeOnlyEndpoint(user: User, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        message: string;
        judgeUser: {
            id: string;
            username: string;
            role: UserRoleEnum;
        };
        judgeFeatures: string[];
    }>>;
    customRolesEndpoint(user: User, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        message: string;
        user: {
            id: string;
            username: string;
            role: UserRoleEnum;
        };
        allowedRoles: string[];
    }>>;
    createSomething(data: any, user: User, userId: string, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        message: string;
        createdBy: {
            id: string;
            username: string;
        };
        userId: string;
        data: any;
        createdAt: string;
    }>>;
    errorExample(user: User, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<{
        message: string;
        user: {
            id: string;
            role: UserRoleEnum.SUPER_ADMIN | UserRoleEnum.ADMIN | UserRoleEnum.ORGANIZER | UserRoleEnum.ARTIST | UserRoleEnum.JUDGE;
        };
    }>>;
}
