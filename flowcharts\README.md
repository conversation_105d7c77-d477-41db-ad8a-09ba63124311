# 书画作品评选打分管理系统流程图说明

## 文件说明

本文件夹包含了书画作品评选打分管理系统的各类流程图：

1. `system_diagrams.puml` - PlantUML格式的流程图源文件
2. `system_flowcharts.md` - Markdown格式的流程图文件（位于上级目录）

## 如何查看这些流程图

### 方式一：使用在线PlantUML编辑器
1. 访问 http://www.plantuml.com/plantuml/uml/
2. 将 `system_diagrams.puml` 中的内容复制到编辑器中
3. 实时查看生成的图表

### 方式二：使用VSCode查看
1. 安装 "PlantUML" 插件
2. 安装 "Markdown Preview Enhanced" 插件
3. 打开 .puml 或 .md 文件即可预览

### 方式三：本地生成图片
1. 安装 Java 运行环境
2. 下载 PlantUML.jar
3. 运行命令：`java -jar plantuml.jar system_diagrams.puml`

## 流程图内容说明

### 1. 系统功能模块图
- 展示了系统的主要功能模块
- 包括项目管理、作品管理、评分系统等模块
- 清晰展示了模块间的关系

### 2. 用户角色权限图
- 展示了系统中的各类用户角色
- 明确了各角色的权限范围
- 展示了角色间的层级关系

### 3. 数据流转流程图
- 展示了系统中数据的流转过程
- 清晰显示了各角色间的交互
- 展示了完整的业务流程

### 4. 状态转换图
- 展示了项目各阶段的状态变化
- 包含了每个状态的详细子流程
- 清晰展示了状态转换的条件

## 更新维护

如需更新流程图：
1. 修改 `system_diagrams.puml` 文件
2. 使用PlantUML重新生成图片
3. 更新相关文档说明

## 注意事项

1. 修改流程图时请保持命名规范
2. 确保图表风格统一
3. 及时更新相关文档
4. 保持版本一致性 