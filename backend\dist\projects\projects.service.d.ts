import { Repository } from 'typeorm';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { Project, ProjectStatus } from './entities/project.entity';
import { User } from '../users/entities/user.entity';
export declare class ProjectsService {
    private projectRepository;
    constructor(projectRepository: Repository<Project>);
    create(createProjectDto: CreateProjectDto, organizerId: string): Promise<Project>;
    findAll(options: {
        page: number;
        limit: number;
        status?: ProjectStatus;
        organizerId?: string;
        isPublic?: boolean;
    }): Promise<{
        data: any;
        total: any;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    findByOrganizer(organizerId: string, page?: number, limit?: number): Promise<{
        data: any;
        total: any;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    findOne(id: string): Promise<Project>;
    update(id: string, updateProjectDto: UpdateProjectDto, user: User): Promise<Project>;
    updateStatus(id: string, status: ProjectStatus, user: User): Promise<Project>;
    remove(id: string, user: User): Promise<void>;
    generateQrCode(id: string, user: User): Promise<Project>;
    private validateStatusTransition;
}
