import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';

/**
 * 全局角色枚举
 */
export enum GlobalRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  ORGANIZER = 'organizer',
  ARTIST = 'artist',
  JUDGE = 'judge',
  USER = 'user'
}

/**
 * 项目内角色枚举
 */
export enum ProjectRole {
  PROJECT_OWNER = 'project_owner',         // 项目所有者
  PROJECT_ADMIN = 'project_admin',         // 项目管理员
  PROJECT_JUDGE = 'project_judge',         // 项目评委
  PROJECT_ARTIST = 'project_artist',       // 项目参与者
  PROJECT_VIEWER = 'project_viewer',       // 项目观察者
  PROJECT_MODERATOR = 'project_moderator', // 项目协调员
  PROJECT_ASSISTANT = 'project_assistant'  // 项目助理
}

/**
 * 用户全局角色表
 * 用户可以拥有多个全局角色
 */
@Entity('user_global_roles')
@Index(['userId', 'role'], { unique: true })
export class UserGlobalRole {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => User, user => user.globalRoles, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ type: 'enum', enum: GlobalRole })
  role: GlobalRole;

  @Column({ type: 'json', nullable: true })
  metadata: {
    grantedBy?: string;      // 授权人
    grantedAt?: Date;        // 授权时间
    expiresAt?: Date;        // 过期时间
    reason?: string;         // 授权原因
    certifications?: string[]; // 相关认证
  };

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

/**
 * 用户项目角色表
 * 用户在特定项目中的角色和权限
 */
@Entity('user_project_roles')
@Index(['userId', 'projectId', 'role'], { unique: true })
export class UserProjectRole {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ name: 'project_id' })
  projectId: string;

  @Column({ type: 'enum', enum: ProjectRole })
  role: ProjectRole;

  @Column({ type: 'json', nullable: true })
  permissions: {
    canManageProject?: boolean;      // 管理项目
    canInviteUsers?: boolean;        // 邀请用户
    canManageArtworks?: boolean;     // 管理作品
    canScore?: boolean;              // 评分权限
    canViewScores?: boolean;         // 查看评分
    canExportData?: boolean;         // 导出数据
    canModerateContent?: boolean;    // 内容审核
    canManageSchedule?: boolean;     // 管理时间安排
    customPermissions?: string[];    // 自定义权限
  };

  @Column({ type: 'json', nullable: true })
  metadata: {
    invitedBy?: string;      // 邀请人
    invitedAt?: Date;        // 邀请时间
    acceptedAt?: Date;       // 接受时间
    role_description?: string; // 角色描述
    responsibilities?: string[]; // 职责列表
  };

  @Column({ type: 'enum', enum: ['pending', 'accepted', 'declined', 'removed'], default: 'pending' })
  status: 'pending' | 'accepted' | 'declined' | 'removed';

  @Column({ type: 'timestamp', nullable: true })
  expiresAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

/**
 * 权限模板
 * 预定义的角色权限配置
 */
@Entity('permission_templates')
export class PermissionTemplate {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ type: 'enum', enum: ProjectRole })
  role: ProjectRole;

  @Column({ type: 'json' })
  permissions: {
    canManageProject?: boolean;
    canInviteUsers?: boolean;
    canManageArtworks?: boolean;
    canScore?: boolean;
    canViewScores?: boolean;
    canExportData?: boolean;
    canModerateContent?: boolean;
    canManageSchedule?: boolean;
    customPermissions?: string[];
  };

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'boolean', default: true })
  isDefault: boolean;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

/**
 * 角色申请记录
 */
@Entity('role_applications')
export class RoleApplication {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ type: 'enum', enum: GlobalRole })
  targetRole: GlobalRole;

  @Column({ type: 'json' })
  applicationData: {
    organizationName?: string;
    organizationType?: string;
    contactPerson?: string;
    contactPhone?: string;
    qualifications?: string[];
    experience?: string;
    reason?: string;
    attachments?: string[];
  };

  @Column({ type: 'enum', enum: ['pending', 'reviewing', 'approved', 'rejected'], default: 'pending' })
  status: 'pending' | 'reviewing' | 'approved' | 'rejected';

  @Column({ type: 'text', nullable: true })
  reviewComment: string;

  @Column({ name: 'reviewer_id', nullable: true })
  reviewerId: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'reviewer_id' })
  reviewer: User;

  @CreateDateColumn()
  appliedAt: Date;

  @UpdateDateColumn()
  reviewedAt: Date;
}
