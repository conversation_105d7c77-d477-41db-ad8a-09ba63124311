import { User } from './user.entity';
export declare enum GlobalRole {
    SUPER_ADMIN = "super_admin",
    ADMIN = "admin",
    ORGANIZER = "organizer",
    ARTIST = "artist",
    JUDGE = "judge",
    USER = "user"
}
export declare enum ProjectRole {
    PROJECT_OWNER = "project_owner",
    PROJECT_ADMIN = "project_admin",
    PROJECT_JUDGE = "project_judge",
    PROJECT_ARTIST = "project_artist",
    PROJECT_VIEWER = "project_viewer",
    PROJECT_MODERATOR = "project_moderator",
    PROJECT_ASSISTANT = "project_assistant"
}
export declare class UserGlobalRole {
    id: string;
    userId: string;
    user: User;
    role: GlobalRole;
    metadata: {
        grantedBy?: string;
        grantedAt?: Date;
        expiresAt?: Date;
        reason?: string;
        certifications?: string[];
    };
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export declare class UserProjectRole {
    id: string;
    userId: string;
    user: User;
    projectId: string;
    role: ProjectRole;
    permissions: {
        canManageProject?: boolean;
        canInviteUsers?: boolean;
        canManageArtworks?: boolean;
        canScore?: boolean;
        canViewScores?: boolean;
        canExportData?: boolean;
        canModerateContent?: boolean;
        canManageSchedule?: boolean;
        customPermissions?: string[];
    };
    metadata: {
        invitedBy?: string;
        invitedAt?: Date;
        acceptedAt?: Date;
        role_description?: string;
        responsibilities?: string[];
    };
    status: 'pending' | 'accepted' | 'declined' | 'removed';
    expiresAt: Date;
    createdAt: Date;
    updatedAt: Date;
}
export declare class PermissionTemplate {
    id: string;
    name: string;
    role: ProjectRole;
    permissions: {
        canManageProject?: boolean;
        canInviteUsers?: boolean;
        canManageArtworks?: boolean;
        canScore?: boolean;
        canViewScores?: boolean;
        canExportData?: boolean;
        canModerateContent?: boolean;
        canManageSchedule?: boolean;
        customPermissions?: string[];
    };
    description: string;
    isDefault: boolean;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export declare class RoleApplication {
    id: string;
    userId: string;
    user: User;
    targetRole: GlobalRole;
    applicationData: {
        organizationName?: string;
        organizationType?: string;
        contactPerson?: string;
        contactPhone?: string;
        qualifications?: string[];
        experience?: string;
        reason?: string;
        attachments?: string[];
    };
    status: 'pending' | 'reviewing' | 'approved' | 'rejected';
    reviewComment: string;
    reviewerId: string;
    reviewer: User;
    appliedAt: Date;
    reviewedAt: Date;
}
