import { ProjectRole } from '../../users/entities/user-roles.entity';
export declare const ProjectPermission: (permission: string) => <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare const ProjectRoleAuth: (roles: ProjectRole[]) => <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare const ProjectOwner: () => <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare const ProjectManager: () => <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare const ProjectJudge: () => <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare const ProjectMember: () => <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare const ProjectId: (...dataOrPipes: unknown[]) => ParameterDecorator;
export declare const CurrentProjectRole: (...dataOrPipes: unknown[]) => ParameterDecorator;
export declare const CurrentProjectPermissions: (...dataOrPipes: unknown[]) => ParameterDecorator;
export declare const HasProjectPermission: (permission: string) => ParameterDecorator;
export declare const IsProjectOwner: (...dataOrPipes: unknown[]) => ParameterDecorator;
export declare const IsProjectManager: (...dataOrPipes: unknown[]) => ParameterDecorator;
