"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const organizer_application_entity_1 = require("./entities/organizer-application.entity");
const user_entity_1 = require("../users/entities/user.entity");
const users_service_1 = require("../users/users.service");
let ApplicationsService = class ApplicationsService {
    applicationRepository;
    usersService;
    constructor(applicationRepository, usersService) {
        this.applicationRepository = applicationRepository;
        this.usersService = usersService;
    }
    async create(createApplicationDto, userId) {
        const existingApplication = await this.applicationRepository.findOne({
            where: {
                userId,
                status: organizer_application_entity_1.ApplicationStatus.PENDING,
            },
        });
        if (existingApplication) {
            throw new common_1.ConflictException('您已有待处理的申请，请等待审核结果');
        }
        const application = this.applicationRepository.create({
            ...createApplicationDto,
            userId,
        });
        return await this.applicationRepository.save(application);
    }
    async findAll(options) {
        const { page, limit, status, organizationType } = options;
        const queryBuilder = this.applicationRepository
            .createQueryBuilder('application')
            .leftJoinAndSelect('application.user', 'user')
            .leftJoinAndSelect('application.reviewer', 'reviewer');
        if (status) {
            queryBuilder.andWhere('application.status = :status', { status });
        }
        if (organizationType) {
            queryBuilder.andWhere('application.organizationType = :organizationType', {
                organizationType,
            });
        }
        queryBuilder
            .orderBy('application.appliedAt', 'DESC')
            .skip((page - 1) * limit)
            .take(limit);
        const [applications, total] = await queryBuilder.getManyAndCount();
        return {
            data: applications,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
        };
    }
    async findByUser(userId) {
        return await this.applicationRepository.find({
            where: { userId },
            relations: ['reviewer'],
            order: { appliedAt: 'DESC' },
        });
    }
    async findOne(id, user) {
        const application = await this.applicationRepository.findOne({
            where: { id },
            relations: ['user', 'reviewer'],
        });
        if (!application) {
            throw new common_1.NotFoundException('申请不存在');
        }
        if (user.role !== user_entity_1.UserRole.SUPER_ADMIN &&
            user.role !== user_entity_1.UserRole.ADMIN &&
            application.userId !== user.id) {
            throw new common_1.ForbiddenException('无权限查看此申请');
        }
        return application;
    }
    async update(id, updateApplicationDto, user) {
        const application = await this.findOne(id, user);
        if (application.userId !== user.id) {
            throw new common_1.ForbiddenException('只能修改自己的申请');
        }
        if (application.status !== organizer_application_entity_1.ApplicationStatus.PENDING) {
            throw new common_1.ForbiddenException('只能修改待处理的申请');
        }
        Object.assign(application, updateApplicationDto);
        return await this.applicationRepository.save(application);
    }
    async review(id, reviewApplicationDto, reviewerId) {
        const application = await this.applicationRepository.findOne({
            where: { id },
            relations: ['user'],
        });
        if (!application) {
            throw new common_1.NotFoundException('申请不存在');
        }
        if (application.status !== organizer_application_entity_1.ApplicationStatus.PENDING) {
            throw new common_1.ForbiddenException('只能审核待处理的申请');
        }
        application.status = reviewApplicationDto.status;
        application.reviewComment = reviewApplicationDto.reviewComment;
        application.reviewerId = reviewerId;
        application.reviewedAt = new Date();
        const updatedApplication = await this.applicationRepository.save(application);
        if (reviewApplicationDto.status === organizer_application_entity_1.ApplicationStatus.APPROVED) {
            await this.usersService.updateRole(application.userId, user_entity_1.UserRole.ORGANIZER);
        }
        return updatedApplication;
    }
    async remove(id, user) {
        const application = await this.findOne(id, user);
        if (application.userId !== user.id) {
            throw new common_1.ForbiddenException('只能删除自己的申请');
        }
        if (application.status !== organizer_application_entity_1.ApplicationStatus.PENDING) {
            throw new common_1.ForbiddenException('只能删除待处理的申请');
        }
        await this.applicationRepository.remove(application);
    }
    async getApplicationStats() {
        const stats = await this.applicationRepository
            .createQueryBuilder('application')
            .select('application.status', 'status')
            .addSelect('COUNT(*)', 'count')
            .groupBy('application.status')
            .getRawMany();
        const total = await this.applicationRepository.count();
        const recentApplications = await this.applicationRepository.find({
            relations: ['user'],
            order: { appliedAt: 'DESC' },
            take: 10,
        });
        return {
            total,
            byStatus: stats.reduce((acc, item) => {
                acc[item.status] = parseInt(item.count);
                return acc;
            }, {}),
            recent: recentApplications,
        };
    }
};
exports.ApplicationsService = ApplicationsService;
exports.ApplicationsService = ApplicationsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(organizer_application_entity_1.OrganizerApplication)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, users_service_1.UsersService])
], ApplicationsService);
//# sourceMappingURL=applications.service.js.map