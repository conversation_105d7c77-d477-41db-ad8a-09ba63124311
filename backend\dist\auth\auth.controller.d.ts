import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { WechatLoginDto } from './dto/wechat-login.dto';
import { RegisterDto } from './dto/register.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    login(loginDto: LoginDto): Promise<{
        accessToken: any;
        refreshToken: any;
        user: {
            id: string;
            username: string;
            nickname: string;
            role: import("../users/entities/user.entity").UserRole;
            avatarUrl: string;
        };
    }>;
    register(registerDto: RegisterDto): Promise<{
        accessToken: any;
        refreshToken: any;
        user: {
            id: string;
            username: string;
            nickname: string;
            role: import("../users/entities/user.entity").UserRole;
            avatarUrl: string;
        };
    }>;
    wechatLogin(wechatLoginDto: WechatLoginDto): Promise<{
        accessToken: any;
        refreshToken: any;
        user: {
            id: string;
            username: string;
            nickname: string;
            role: import("../users/entities/user.entity").UserRole;
            avatarUrl: string;
        };
    }>;
    getProfile(req: any): Promise<{
        id: string;
        openid: string;
        unionid: string;
        username: string;
        email: string;
        nickname: string;
        avatarUrl: string;
        phone: string;
        realName: string;
        role: import("../users/entities/user.entity").UserRole;
        loginType: import("../users/entities/user.entity").LoginType;
        bio: string;
        profileData: {
            address?: string;
            birthDate?: string;
            gender?: "male" | "female" | "other";
            profession?: string;
            education?: string;
            website?: string;
            socialMedia?: {
                wechat?: string;
                weibo?: string;
                qq?: string;
            };
        };
        specialties: string[];
        certificationLevel: number;
        organizationId: string;
        status: import("../users/entities/user.entity").UserStatus;
        lastLoginAt: Date;
        loginCount: number;
        settings: {
            language?: string;
            timezone?: string;
            emailNotifications?: boolean;
            smsNotifications?: boolean;
            theme?: "light" | "dark";
        };
        createdAt: Date;
        updatedAt: Date;
        globalRoles: import("../users/entities/user-roles.entity").UserGlobalRole[];
        projectRoles: import("../users/entities/user-roles.entity").UserProjectRole[];
    }>;
    refreshToken(req: any): Promise<{
        accessToken: any;
        refreshToken: any;
        user: {
            id: string;
            username: string;
            nickname: string;
            role: import("../users/entities/user.entity").UserRole;
            avatarUrl: string;
        };
    }>;
    logout(req: any): Promise<{
        message: string;
    }>;
}
