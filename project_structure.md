# 书画作品评选系统项目结构

## 目录结构
```
calligraphy-contest/
├── backend/                 # 后端API服务
│   ├── src/
│   │   ├── controllers/     # 控制器
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # 路由
│   │   ├── middleware/     # 中间件
│   │   └── utils/          # 工具函数
│   ├── config/             # 配置文件
│   └── package.json
├── frontend/
│   ├── uni-app/            # uni-app多端应用
│   │   ├── pages/          # 页面
│   │   ├── components/     # 组件
│   │   ├── static/         # 静态资源
│   │   ├── store/          # 状态管理
│   │   ├── utils/          # 工具函数
│   │   ├── api/            # API接口
│   │   ├── manifest.json   # 应用配置
│   │   ├── pages.json      # 页面配置
│   │   └── uni.scss        # 全局样式
│   └── admin-web/          # PC管理端
│       ├── src/
│       │   ├── views/      # 页面
│       │   ├── components/ # 组件
│       │   └── api/        # API接口
│       └── package.json
├── database/               # 数据库脚本
│   ├── migrations/         # 数据迁移
│   └── seeds/             # 初始数据
└── docs/                  # 文档
    ├── api/               # API文档
    └── design/            # 设计文档
```

## 技术栈选择

### 后端
- **框架**: Node.js + Express
- **数据库**: MySQL + Sequelize ORM
- **缓存**: Redis
- **文件上传**: Multer + 云存储
- **认证**: JWT + 微信登录

### 前端
- **多端应用**: uni-app (支持微信小程序、H5、App)
- **PC端**: Vue 3 + Element Plus
- **状态管理**: Vuex/Pinia
- **HTTP客户端**: uni.request / Axios
- **UI框架**: uni-ui / uView

### 开发工具
- **版本控制**: Git
- **API测试**: Postman
- **数据库管理**: MySQL Workbench
- **代码编辑器**: VS Code