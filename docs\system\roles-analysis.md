# 书画作品评选系统角色分析与规划

## 🎯 系统角色概览

基于书画作品评选的业务流程，系统需要支持以下6种核心角色：

```
系统角色层次结构：
├── super_admin (超级管理员)
├── admin (系统管理员)
├── organizer (办展方)
├── artist (美工组/作者)
├── judge (评委)
└── user (普通用户/观众)
```

## 📋 详细角色分析

### 1. **Super Admin (超级管理员)**
**职责**：系统最高权限管理者，负责整个平台的运营和维护

**核心功能**：
- ✅ **用户管理**：创建、编辑、删除所有用户
- ✅ **角色管理**：分配和修改用户角色
- ✅ **系统配置**：修改系统参数、功能开关
- ✅ **数据管理**：数据备份、恢复、清理
- ✅ **安全管理**：查看系统日志、安全审计
- ✅ **平台监控**：系统性能、错误监控
- ✅ **全局统计**：平台整体数据分析

**权限范围**：
- 所有模块的完全访问权限
- 危险操作权限（删除、重置等）
- 系统级配置权限

**使用场景**：
- 平台初始化和配置
- 处理重大系统问题
- 用户申诉和纠纷处理
- 系统升级和维护

---

### 2. **Admin (系统管理员)**
**职责**：日常运营管理，协助超级管理员处理平台事务

**核心功能**：
- ✅ **用户审核**：审批办展方申请
- ✅ **内容监管**：审核项目、作品内容
- ✅ **数据统计**：生成运营报表
- ✅ **客服支持**：处理用户问题和投诉
- ✅ **活动管理**：监督项目进展
- ✅ **质量控制**：确保平台内容质量

**权限范围**：
- 用户管理（除超级管理员外）
- 项目和作品的查看、审核权限
- 申请审批权限
- 数据导出权限

**使用场景**：
- 日常用户管理
- 办展方资质审核
- 项目质量监督
- 用户问题处理

---

### 3. **Organizer (办展方)**
**职责**：组织和管理书画作品评选活动

**核心功能**：
- ✅ **项目管理**：创建、配置、管理评选项目
- ✅ **作品管理**：收集、整理、审核参赛作品
- ✅ **评委邀请**：邀请和管理评委
- ✅ **流程控制**：控制项目各阶段流转
- ✅ **结果发布**：公布评选结果
- ✅ **数据导出**：导出项目相关数据

**权限范围**：
- 自己创建的项目完全管理权限
- 项目内作品的管理权限
- 评分结果的查看权限
- 项目数据的导出权限

**使用场景**：
- 创建书法/绘画比赛
- 管理参赛作品
- 监控评选进度
- 发布比赛结果

**子角色考虑**：
- **主办方**：大型机构，可创建多个项目
- **协办方**：协助主办方管理项目
- **承办方**：具体执行项目运营

---

### 4. **Artist (美工组/作者)**
**职责**：作品创作者和内容贡献者

**核心功能**：
- ✅ **作品上传**：提交参赛作品
- ✅ **信息维护**：管理个人作品信息
- ✅ **进度跟踪**：查看作品审核状态
- ✅ **结果查看**：查看评选结果
- ✅ **证书下载**：下载获奖证书

**权限范围**：
- 自己作品的管理权限
- 参与项目的查看权限
- 个人数据的查看权限

**使用场景**：
- 参加书画比赛
- 上传作品参评
- 查看评选结果
- 管理个人作品集

**细分类型**：
- **书法家**：专业书法创作者
- **画家**：专业绘画创作者
- **业余爱好者**：非专业创作者
- **学生**：在校学习者

---

### 5. **Judge (评委)**
**职责**：专业评审，对作品进行评分和点评

**核心功能**：
- ✅ **作品评分**：对作品进行客观和主观评分
- ✅ **评语录入**：提供专业点评意见
- ✅ **语音评语**：录制语音点评
- ✅ **图片标注**：在作品上标注要点
- ✅ **评分历史**：查看自己的评分记录
- ✅ **进度管理**：跟踪评分进度

**权限范围**：
- 被邀请项目的作品查看权限
- 作品评分和评语权限
- 自己评分记录的查看权限

**使用场景**：
- 接受评委邀请
- 在线评审作品
- 提供专业意见
- 参与评选讨论

**专业分类**：
- **书法评委**：书法专业评审
- **绘画评委**：绘画专业评审
- **综合评委**：多领域专家
- **特邀评委**：知名专家学者

---

### 6. **User (普通用户/观众)**
**职责**：平台访客，浏览和关注评选活动

**核心功能**：
- ✅ **内容浏览**：查看公开的项目和作品
- ✅ **结果查看**：查看评选结果
- ✅ **互动参与**：点赞、收藏、分享
- ✅ **申请升级**：申请成为其他角色
- ✅ **个人中心**：管理个人信息

**权限范围**：
- 公开内容的查看权限
- 个人信息的管理权限
- 基础互动功能权限

**使用场景**：
- 浏览书画作品
- 关注比赛动态
- 学习优秀作品
- 申请参与评选

---

## 🔄 角色流转关系

### **角色升级路径**
```
user → artist (申请参赛)
user → judge (专业认证)
user → organizer (机构申请)
artist → judge (专业提升)
admin → super_admin (权限提升)
```

### **角色权限继承**
```
super_admin > admin > organizer
super_admin > admin > judge
super_admin > admin > artist > user
```

## 📊 角色权限矩阵

| 功能模块 | Super Admin | Admin | Organizer | Artist | Judge | User |
|----------|-------------|-------|-----------|--------|-------|------|
| 用户管理 | ✅ 全部 | ✅ 部分 | ❌ | ❌ | ❌ | ❌ |
| 项目管理 | ✅ 全部 | ✅ 查看 | ✅ 自己的 | ❌ | ❌ | ❌ |
| 作品管理 | ✅ 全部 | ✅ 审核 | ✅ 项目内 | ✅ 自己的 | ❌ | ❌ |
| 评分管理 | ✅ 全部 | ✅ 查看 | ✅ 查看 | ❌ | ✅ 评分 | ❌ |
| 申请审批 | ✅ 全部 | ✅ 审批 | ❌ | ❌ | ❌ | ❌ |
| 系统配置 | ✅ 全部 | ❌ | ❌ | ❌ | ❌ | ❌ |
| 数据统计 | ✅ 全部 | ✅ 运营 | ✅ 项目 | ✅ 个人 | ✅ 个人 | ❌ |
| 内容浏览 | ✅ 全部 | ✅ 全部 | ✅ 相关 | ✅ 相关 | ✅ 相关 | ✅ 公开 |

## 🎨 角色使用场景分析

### **典型业务流程**

#### 1. **项目创建流程**
```
Organizer 创建项目 → Admin 审核 → 项目发布 → Artist 提交作品
```

#### 2. **作品评选流程**
```
Artist 上传作品 → Organizer 审核 → Judge 评分 → 结果发布 → User 查看
```

#### 3. **用户申请流程**
```
User 提交申请 → Admin 审核 → 角色升级 → 获得新权限
```

### **关键决策点**

#### 1. **是否需要更细粒度的角色？**
**建议**：当前6个角色已经覆盖主要场景，可以通过权限配置实现细分

#### 2. **是否需要临时角色？**
**建议**：可以考虑添加：
- `guest_judge` - 临时评委（单次项目）
- `project_admin` - 项目管理员（协助办展方）

#### 3. **是否需要组织架构？**
**建议**：可以考虑添加：
- 机构管理（一个机构多个办展方）
- 评委组管理（评委团队协作）

## 🚀 实施建议

### **Phase 1: 核心角色实现**
- ✅ 实现6个基础角色
- ✅ 完善权限控制系统
- ✅ 实现角色申请流程

### **Phase 2: 功能完善**
- 🔄 添加角色升级机制
- 🔄 实现细粒度权限控制
- 🔄 添加角色管理界面

### **Phase 3: 高级特性**
- 📋 组织架构管理
- 📋 临时角色支持
- 📋 权限委托机制

### **技术实现要点**

#### 1. **数据库设计**
```sql
-- 用户表
users (id, username, email, role, status, ...)

-- 角色权限表
role_permissions (role, resource, action, allowed)

-- 用户申请表
user_applications (user_id, target_role, status, ...)
```

#### 2. **权限检查**
```typescript
// 基于角色的权限检查
@RoleAuth([UserRole.ORGANIZER, UserRole.ADMIN])

// 基于资源的权限检查
@ResourceAuth('project', 'manage')
```

#### 3. **角色升级**
```typescript
// 申请角色升级
await this.userService.applyForRole(userId, targetRole, applicationData);

// 审批角色升级
await this.adminService.approveRoleApplication(applicationId, approved);
```

这个角色体系设计既满足了书画评选的专业需求，又保持了系统的灵活性和可扩展性。
