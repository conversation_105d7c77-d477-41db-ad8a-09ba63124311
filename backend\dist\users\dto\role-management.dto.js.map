{"version": 3, "file": "role-management.dto.js", "sourceRoot": "", "sources": ["../../../src/users/dto/role-management.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAqG;AACrG,6CAAmE;AACnE,qEAAwE;AAKxE,MAAa,kBAAkB;IAG7B,UAAU,CAAa;IAIvB,eAAe,CASb;CACH;AAjBD,gDAiBC;AAdC;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,8BAAU,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC;IACvD,IAAA,wBAAM,EAAC,8BAAU,CAAC;;sDACI;AAIvB;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC;IACvC,IAAA,0BAAQ,GAAE;;2DAUT;AAMJ,MAAa,wBAAwB;IAGnC,QAAQ,CAAU;IAKlB,aAAa,CAAU;CACxB;AATD,4DASC;AANC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACpC,IAAA,2BAAS,GAAE;;0DACM;AAKlB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAC5C,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;+DACY;AAMzB,MAAa,mBAAmB;IAG9B,MAAM,CAAS;IAIf,IAAI,CAAa;IAKjB,QAAQ,CAIN;CACH;AAjBD,kDAiBC;AAdC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACpC,IAAA,wBAAM,GAAE;;mDACM;AAIf;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,8BAAU,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;IACpD,IAAA,wBAAM,EAAC,8BAAU,CAAC;;iDACF;AAKjB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC;IAC7C,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;qDAKT;AAMJ,MAAa,mBAAmB;IAG9B,WAAW,CAAwB;CACpC;AAJD,kDAIC;AADC;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,CAAC,mBAAmB,CAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC;IACnE,IAAA,yBAAO,GAAE;;wDACyB;AAMrC,MAAa,sBAAsB;IAGjC,MAAM,CAAS;IAIf,IAAI,CAAc;IAKlB,WAAW,CAUT;IAKF,OAAO,CAAU;IAKjB,eAAe,CAAU;IAKzB,gBAAgB,CAAY;CAC7B;AAtCD,wDAsCC;AAnCC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACpC,IAAA,wBAAM,GAAE;;sDACM;AAIf;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,+BAAW,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACvD,IAAA,wBAAM,EAAC,+BAAW,CAAC;;oDACF;AAKlB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC;IAC/C,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2DAWT;AAKF;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAC5C,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;uDACM;AAKjB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAC5C,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;+DACc;AAKzB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAC5C,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;;gEACkB;AAM9B,MAAa,mBAAmB;IAG9B,WAAW,CAA2B;CACvC;AAJD,kDAIC;AADC;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,CAAC,sBAAsB,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACpE,IAAA,yBAAO,GAAE;;wDAC4B;AAMxC,MAAa,2BAA2B;IAGtC,WAAW,CAUT;CACH;AAdD,kEAcC;AAXC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACpC,IAAA,0BAAQ,GAAE;;gEAWT;AAMJ,MAAa,sBAAsB;IAIjC,MAAM,CAAU;CACjB;AALD,wDAKC;AADC;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAC5C,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sDACK;AAMlB,MAAa,2BAA2B;IAGtC,IAAI,CAAS;IAIb,IAAI,CAAc;IAIlB,WAAW,CAUT;IAKF,WAAW,CAAU;IAKrB,SAAS,CAAW;CACrB;AAhCD,kEAgCC;AA7BC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACpC,IAAA,0BAAQ,GAAE;;yDACE;AAIb;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,+BAAW,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACvD,IAAA,wBAAM,EAAC,+BAAW,CAAC;;yDACF;AAIlB;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACpC,IAAA,0BAAQ,GAAE;;gEAWT;AAKF;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAC5C,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;gEACU;AAKrB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC;IAC/C,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;8DACQ;AAMtB,MAAa,wBAAwB;IAInC,MAAM,CAAU;IAKhB,UAAU,CAAc;IAKxB,MAAM,CAAU;IAIhB,IAAI,CAAU;IAId,KAAK,CAAU;CAChB;AAvBD,4DAuBC;AAnBC;IAHC,IAAA,6BAAmB,EAAC,EAAE,IAAI,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACpG,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,CAAC,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;;wDACzC;AAKhB;IAHC,IAAA,6BAAmB,EAAC,EAAE,IAAI,EAAE,8BAAU,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAC9D,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,8BAAU,CAAC;;4DACK;AAKxB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAC5C,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,GAAE;;wDACO;AAIhB;IAFC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACtD,IAAA,4BAAU,GAAE;;sDACC;AAId;IAFC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;IACzD,IAAA,4BAAU,GAAE;;uDACE;AAMjB,MAAa,sBAAsB;IAIjC,IAAI,CAAe;IAKnB,MAAM,CAAU;IAIhB,IAAI,CAAU;IAId,KAAK,CAAU;CAChB;AAlBD,wDAkBC;AAdC;IAHC,IAAA,6BAAmB,EAAC,EAAE,IAAI,EAAE,+BAAW,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAC/D,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,+BAAW,CAAC;;oDACD;AAKnB;IAHC,IAAA,6BAAmB,EAAC,EAAE,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;IAChG,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;;sDACvC;AAIhB;IAFC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACtD,IAAA,4BAAU,GAAE;;oDACC;AAId;IAFC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;IACzD,IAAA,4BAAU,GAAE;;qDACE"}