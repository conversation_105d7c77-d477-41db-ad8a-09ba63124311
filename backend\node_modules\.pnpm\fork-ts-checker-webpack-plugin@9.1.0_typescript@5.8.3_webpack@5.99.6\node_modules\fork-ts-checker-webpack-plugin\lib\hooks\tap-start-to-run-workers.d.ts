import type * as webpack from 'webpack';
import type { ForkTsCheckerWebpackPluginConfig } from '../plugin-config';
import type { ForkTsCheckerWebpackPluginState } from '../plugin-state';
import type { RpcWorker } from '../rpc';
import type { GetDependenciesWorker } from '../typescript/worker/get-dependencies-worker';
import type { GetIssuesWorker } from '../typescript/worker/get-issues-worker';
declare function tapStartToRunWorkers(compiler: webpack.Compiler, getIssuesWorker: RpcWorker<GetIssuesWorker>, getDependenciesWorker: RpcWorker<GetDependenciesWorker>, config: ForkTsCheckerWebpackPluginConfig, state: ForkTsCheckerWebpackPluginState): void;
export { tapStartToRunWorkers };
