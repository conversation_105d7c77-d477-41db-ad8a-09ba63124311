import { Request } from 'express';
export interface ApiResponse<T = any> {
    success: boolean;
    code: number;
    message: string;
    data?: T;
    timestamp: number;
    requestId: string;
}
export interface PaginatedResponse<T> {
    success: boolean;
    code: number;
    message: string;
    data: {
        items: T[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    };
    timestamp: number;
    requestId: string;
}
export interface ApiErrorResponse {
    success: false;
    code: number;
    message: string;
    error: {
        type: string;
        details?: any;
        field?: string;
    };
    timestamp: number;
    requestId: string;
}
export interface PaginationQuery {
    page?: number;
    limit?: number;
    sort?: string;
    order?: 'ASC' | 'DESC';
}
export interface BaseQuery extends PaginationQuery {
    search?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
    fields?: string;
}
export declare enum BusinessCode {
    SUCCESS = 200,
    CREATED = 201,
    BAD_REQUEST = 400,
    UNAUTHORIZED = 401,
    FORBIDDEN = 403,
    NOT_FOUND = 404,
    CONFLICT = 409,
    VALIDATION_ERROR = 422,
    USER_NOT_FOUND = 1001,
    USER_DISABLED = 1002,
    PROJECT_NOT_FOUND = 2001,
    PROJECT_STATUS_ERROR = 2002,
    ARTWORK_NOT_FOUND = 3001,
    SCORE_DUPLICATE = 4001,
    SCORE_TIME_EXPIRED = 4002,
    APPLICATION_NOT_FOUND = 5001,
    INTERNAL_ERROR = 500,
    DATABASE_ERROR = 501,
    EXTERNAL_SERVICE_ERROR = 502
}
export declare enum ErrorType {
    VALIDATION_ERROR = "VALIDATION_ERROR",
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR",
    AUTHORIZATION_ERROR = "AUTHORIZATION_ERROR",
    BUSINESS_ERROR = "BUSINESS_ERROR",
    SYSTEM_ERROR = "SYSTEM_ERROR",
    EXTERNAL_ERROR = "EXTERNAL_ERROR"
}
export declare abstract class BaseController {
    protected getRequestId(req?: Request): string;
    protected success<T>(data: T, message?: string, code?: number, req?: Request): ApiResponse<T>;
    protected created<T>(data: T, message?: string, req?: Request): ApiResponse<T>;
    protected paginated<T>(items: T[], total: number, page: number, limit: number, message?: string, req?: Request): PaginatedResponse<T>;
    protected error(message: string, code?: number, errorType?: ErrorType, details?: any, field?: string, req?: Request): ApiErrorResponse;
    protected validationError(details: any, message?: string, req?: Request): ApiErrorResponse;
    protected notFound(message?: string, req?: Request): ApiErrorResponse;
    protected forbidden(message?: string, req?: Request): ApiErrorResponse;
    protected conflict(message?: string, req?: Request): ApiErrorResponse;
    protected parsePagination(query: any): {
        page: number;
        limit: number;
    };
    protected parseSort(query: any): {
        sort?: string;
        order: 'ASC' | 'DESC';
    };
    protected parseDateRange(query: any): {
        startDate?: Date;
        endDate?: Date;
    };
    protected parseFields(query: any): string[] | undefined;
}
