"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseController = exports.ErrorType = exports.BusinessCode = void 0;
const common_1 = require("@nestjs/common");
const uuid_1 = require("uuid");
var BusinessCode;
(function (BusinessCode) {
    BusinessCode[BusinessCode["SUCCESS"] = 200] = "SUCCESS";
    BusinessCode[BusinessCode["CREATED"] = 201] = "CREATED";
    BusinessCode[BusinessCode["BAD_REQUEST"] = 400] = "BAD_REQUEST";
    BusinessCode[BusinessCode["UNAUTHORIZED"] = 401] = "UNAUTHORIZED";
    BusinessCode[BusinessCode["FORBIDDEN"] = 403] = "FORBIDDEN";
    BusinessCode[BusinessCode["NOT_FOUND"] = 404] = "NOT_FOUND";
    BusinessCode[BusinessCode["CONFLICT"] = 409] = "CONFLICT";
    BusinessCode[BusinessCode["VALIDATION_ERROR"] = 422] = "VALIDATION_ERROR";
    BusinessCode[BusinessCode["USER_NOT_FOUND"] = 1001] = "USER_NOT_FOUND";
    BusinessCode[BusinessCode["USER_DISABLED"] = 1002] = "USER_DISABLED";
    BusinessCode[BusinessCode["PROJECT_NOT_FOUND"] = 2001] = "PROJECT_NOT_FOUND";
    BusinessCode[BusinessCode["PROJECT_STATUS_ERROR"] = 2002] = "PROJECT_STATUS_ERROR";
    BusinessCode[BusinessCode["ARTWORK_NOT_FOUND"] = 3001] = "ARTWORK_NOT_FOUND";
    BusinessCode[BusinessCode["SCORE_DUPLICATE"] = 4001] = "SCORE_DUPLICATE";
    BusinessCode[BusinessCode["SCORE_TIME_EXPIRED"] = 4002] = "SCORE_TIME_EXPIRED";
    BusinessCode[BusinessCode["APPLICATION_NOT_FOUND"] = 5001] = "APPLICATION_NOT_FOUND";
    BusinessCode[BusinessCode["INTERNAL_ERROR"] = 500] = "INTERNAL_ERROR";
    BusinessCode[BusinessCode["DATABASE_ERROR"] = 501] = "DATABASE_ERROR";
    BusinessCode[BusinessCode["EXTERNAL_SERVICE_ERROR"] = 502] = "EXTERNAL_SERVICE_ERROR";
})(BusinessCode || (exports.BusinessCode = BusinessCode = {}));
var ErrorType;
(function (ErrorType) {
    ErrorType["VALIDATION_ERROR"] = "VALIDATION_ERROR";
    ErrorType["AUTHENTICATION_ERROR"] = "AUTHENTICATION_ERROR";
    ErrorType["AUTHORIZATION_ERROR"] = "AUTHORIZATION_ERROR";
    ErrorType["BUSINESS_ERROR"] = "BUSINESS_ERROR";
    ErrorType["SYSTEM_ERROR"] = "SYSTEM_ERROR";
    ErrorType["EXTERNAL_ERROR"] = "EXTERNAL_ERROR";
})(ErrorType || (exports.ErrorType = ErrorType = {}));
let BaseController = class BaseController {
    getRequestId(req) {
        return req?.headers['x-request-id'] || (0, uuid_1.v4)();
    }
    success(data, message = '操作成功', code = BusinessCode.SUCCESS, req) {
        return {
            success: true,
            code,
            message,
            data,
            timestamp: Date.now(),
            requestId: this.getRequestId(req)
        };
    }
    created(data, message = '创建成功', req) {
        return this.success(data, message, BusinessCode.CREATED, req);
    }
    paginated(items, total, page, limit, message = '获取成功', req) {
        const totalPages = Math.ceil(total / limit);
        return {
            success: true,
            code: BusinessCode.SUCCESS,
            message,
            data: {
                items,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages,
                    hasNext: page < totalPages,
                    hasPrev: page > 1
                }
            },
            timestamp: Date.now(),
            requestId: this.getRequestId(req)
        };
    }
    error(message, code = BusinessCode.BAD_REQUEST, errorType = ErrorType.BUSINESS_ERROR, details, field, req) {
        return {
            success: false,
            code,
            message,
            error: {
                type: errorType,
                details,
                field
            },
            timestamp: Date.now(),
            requestId: this.getRequestId(req)
        };
    }
    validationError(details, message = '参数验证失败', req) {
        return this.error(message, BusinessCode.VALIDATION_ERROR, ErrorType.VALIDATION_ERROR, details, undefined, req);
    }
    notFound(message = '资源不存在', req) {
        return this.error(message, BusinessCode.NOT_FOUND, ErrorType.BUSINESS_ERROR, undefined, undefined, req);
    }
    forbidden(message = '权限不足', req) {
        return this.error(message, BusinessCode.FORBIDDEN, ErrorType.AUTHORIZATION_ERROR, undefined, undefined, req);
    }
    conflict(message = '资源冲突', req) {
        return this.error(message, BusinessCode.CONFLICT, ErrorType.BUSINESS_ERROR, undefined, undefined, req);
    }
    parsePagination(query) {
        const page = Math.max(1, parseInt(query.page) || 1);
        const limit = Math.min(100, Math.max(1, parseInt(query.limit) || 10));
        return { page, limit };
    }
    parseSort(query) {
        const sort = query.sort;
        const order = query.order?.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
        return { sort, order };
    }
    parseDateRange(query) {
        const startDate = query.startDate ? new Date(query.startDate) : undefined;
        const endDate = query.endDate ? new Date(query.endDate) : undefined;
        return { startDate, endDate };
    }
    parseFields(query) {
        return query.fields ? query.fields.split(',').map((f) => f.trim()) : undefined;
    }
};
exports.BaseController = BaseController;
exports.BaseController = BaseController = __decorate([
    (0, common_1.Injectable)()
], BaseController);
//# sourceMappingURL=base.controller.js.map