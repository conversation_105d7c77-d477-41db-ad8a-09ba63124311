"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectPermissionService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("../../users/entities/user.entity");
const user_roles_entity_1 = require("../../users/entities/user-roles.entity");
const role_management_service_1 = require("./role-management.service");
let ProjectPermissionService = class ProjectPermissionService {
    userRepository;
    projectRoleRepository;
    roleManagementService;
    constructor(userRepository, projectRoleRepository, roleManagementService) {
        this.userRepository = userRepository;
        this.projectRoleRepository = projectRoleRepository;
        this.roleManagementService = roleManagementService;
    }
    async inviteUserToProject(projectId, userId, role, permissions, invitedBy, metadata) {
        const user = await this.userRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        const canInvite = await this.hasProjectPermission(invitedBy, projectId, 'invite');
        if (!canInvite) {
            throw new common_1.ForbiddenException('您没有邀请用户的权限');
        }
        const existingRole = await this.projectRoleRepository.findOne({
            where: { userId, projectId, role }
        });
        if (existingRole && existingRole.status === 'accepted') {
            throw new common_1.ConflictException('用户已在项目中拥有此角色');
        }
        if (existingRole && existingRole.status === 'pending') {
            throw new common_1.ConflictException('用户已有待处理的邀请');
        }
        if (existingRole) {
            existingRole.permissions = permissions;
            existingRole.metadata = {
                ...existingRole.metadata,
                ...metadata,
                invitedBy,
                invitedAt: new Date()
            };
            existingRole.status = 'pending';
            return await this.projectRoleRepository.save(existingRole);
        }
        const projectRole = this.projectRoleRepository.create({
            userId,
            projectId,
            role,
            permissions,
            metadata: {
                invitedBy,
                invitedAt: new Date(),
                ...metadata
            },
            status: 'pending'
        });
        return await this.projectRoleRepository.save(projectRole);
    }
    async acceptProjectInvitation(userId, projectId, role) {
        const whereCondition = { userId, projectId, status: 'pending' };
        if (role) {
            whereCondition.role = role;
        }
        const invitation = await this.projectRoleRepository.findOne({
            where: whereCondition
        });
        if (!invitation) {
            throw new common_1.NotFoundException('邀请不存在或已过期');
        }
        if (invitation.expiresAt && new Date() > invitation.expiresAt) {
            invitation.status = 'removed';
            await this.projectRoleRepository.save(invitation);
            throw new common_1.ForbiddenException('邀请已过期');
        }
        invitation.status = 'accepted';
        invitation.metadata = {
            ...invitation.metadata,
            acceptedAt: new Date()
        };
        await this.projectRoleRepository.save(invitation);
    }
    async declineProjectInvitation(userId, projectId, role) {
        const whereCondition = { userId, projectId, status: 'pending' };
        if (role) {
            whereCondition.role = role;
        }
        const invitation = await this.projectRoleRepository.findOne({
            where: whereCondition
        });
        if (!invitation) {
            throw new common_1.NotFoundException('邀请不存在');
        }
        invitation.status = 'declined';
        invitation.metadata = {
            ...invitation.metadata,
            declinedAt: new Date()
        };
        await this.projectRoleRepository.save(invitation);
    }
    async updateProjectRolePermissions(userId, projectId, permissions, updatedBy) {
        const canManage = await this.hasProjectPermission(updatedBy, projectId, 'manage');
        if (!canManage) {
            throw new common_1.ForbiddenException('您没有管理权限');
        }
        const projectRole = await this.projectRoleRepository.findOne({
            where: { userId, projectId, status: 'accepted' }
        });
        if (!projectRole) {
            throw new common_1.NotFoundException('用户不在项目中');
        }
        projectRole.permissions = permissions;
        projectRole.metadata = {
            ...projectRole.metadata,
            lastUpdatedBy: updatedBy,
            lastUpdatedAt: new Date()
        };
        await this.projectRoleRepository.save(projectRole);
    }
    async removeProjectMember(userId, projectId, removedBy, reason) {
        const canManage = await this.hasProjectPermission(removedBy, projectId, 'manage');
        if (!canManage) {
            throw new common_1.ForbiddenException('您没有管理权限');
        }
        const ownerRole = await this.projectRoleRepository.findOne({
            where: { userId, projectId, role: user_roles_entity_1.ProjectRole.PROJECT_OWNER, status: 'accepted' }
        });
        if (ownerRole) {
            throw new common_1.ForbiddenException('不能移除项目所有者');
        }
        await this.projectRoleRepository.update({ userId, projectId, status: 'accepted' }, {
            status: 'removed',
            metadata: () => `JSON_SET(metadata, '$.removedBy', '${removedBy}', '$.removedAt', '${new Date().toISOString()}', '$.reason', '${reason || ''}')`
        });
    }
    async getUserProjectRoles(userId, projectId) {
        return await this.projectRoleRepository.find({
            where: {
                userId,
                projectId,
                status: 'accepted'
            },
            order: { createdAt: 'ASC' }
        });
    }
    async getProjectMembers(projectId, filters) {
        const { role, status = 'accepted', page = 1, limit = 50 } = filters || {};
        const queryBuilder = this.projectRoleRepository
            .createQueryBuilder('pr')
            .leftJoinAndSelect('pr.user', 'user')
            .where('pr.projectId = :projectId', { projectId })
            .andWhere('pr.status = :status', { status });
        if (role) {
            queryBuilder.andWhere('pr.role = :role', { role });
        }
        queryBuilder
            .orderBy('pr.createdAt', 'ASC')
            .skip((page - 1) * limit)
            .take(limit);
        const [data, total] = await queryBuilder.getManyAndCount();
        return { data, total, page, limit };
    }
    async hasProjectPermission(userId, projectId, permission) {
        const user = await this.userRepository.findOne({
            where: { id: userId },
            relations: ['globalRoles']
        });
        if (!user) {
            return false;
        }
        const userRoles = user.globalRoles?.filter(gr => gr.isActive).map(gr => gr.role) || [];
        if (userRoles.includes(user_roles_entity_1.GlobalRole.SUPER_ADMIN) || userRoles.includes(user_roles_entity_1.GlobalRole.ADMIN)) {
            return true;
        }
        const projectRoles = await this.getUserProjectRoles(userId, projectId);
        if (projectRoles.length === 0) {
            return false;
        }
        for (const projectRole of projectRoles) {
            if (this.checkPermission(projectRole, permission)) {
                return true;
            }
        }
        return false;
    }
    checkPermission(projectRole, permission) {
        const permissions = projectRole.permissions || {};
        if (projectRole.role === user_roles_entity_1.ProjectRole.PROJECT_OWNER) {
            return true;
        }
        switch (permission) {
            case 'manage':
                return permissions.canManageProject || projectRole.role === user_roles_entity_1.ProjectRole.PROJECT_ADMIN;
            case 'invite':
                return permissions.canInviteUsers;
            case 'artwork_manage':
                return permissions.canManageArtworks;
            case 'score':
                return permissions.canScore;
            case 'view_scores':
                return permissions.canViewScores;
            case 'export':
                return permissions.canExportData;
            case 'moderate':
                return permissions.canModerateContent;
            case 'schedule':
                return permissions.canManageSchedule;
            default:
                return permissions.customPermissions?.includes(permission) || false;
        }
    }
    mergePermissions(projectRoles) {
        const mergedPermissions = {
            canManageProject: false,
            canInviteUsers: false,
            canManageArtworks: false,
            canScore: false,
            canViewScores: false,
            canExportData: false,
            canModerateContent: false,
            canManageSchedule: false,
            customPermissions: []
        };
        for (const role of projectRoles) {
            const permissions = role.permissions || {};
            mergedPermissions.canManageProject = mergedPermissions.canManageProject || permissions.canManageProject;
            mergedPermissions.canInviteUsers = mergedPermissions.canInviteUsers || permissions.canInviteUsers;
            mergedPermissions.canManageArtworks = mergedPermissions.canManageArtworks || permissions.canManageArtworks;
            mergedPermissions.canScore = mergedPermissions.canScore || permissions.canScore;
            mergedPermissions.canViewScores = mergedPermissions.canViewScores || permissions.canViewScores;
            mergedPermissions.canExportData = mergedPermissions.canExportData || permissions.canExportData;
            mergedPermissions.canModerateContent = mergedPermissions.canModerateContent || permissions.canModerateContent;
            mergedPermissions.canManageSchedule = mergedPermissions.canManageSchedule || permissions.canManageSchedule;
            if (permissions.customPermissions) {
                mergedPermissions.customPermissions = [
                    ...new Set([...mergedPermissions.customPermissions, ...permissions.customPermissions])
                ];
            }
        }
        return mergedPermissions;
    }
    async batchInviteUsers(projectId, invitations, invitedBy) {
        const results = [];
        for (const invitation of invitations) {
            try {
                const permissions = invitation.permissions ||
                    await this.roleManagementService.getDefaultPermissions(invitation.role);
                const projectRole = await this.inviteUserToProject(projectId, invitation.userId, invitation.role, permissions, invitedBy, {
                    message: invitation.message,
                    batchInvitation: true
                });
                results.push(projectRole);
            }
            catch (error) {
                console.error(`Failed to invite user ${invitation.userId}:`, error.message);
            }
        }
        return results;
    }
    async getProjectPermissionStats(projectId) {
        const roleStats = await this.projectRoleRepository
            .createQueryBuilder('pr')
            .select('pr.role', 'role')
            .addSelect('COUNT(*)', 'count')
            .where('pr.projectId = :projectId', { projectId })
            .andWhere('pr.status = :status', { status: 'accepted' })
            .groupBy('pr.role')
            .getRawMany();
        const statusStats = await this.projectRoleRepository
            .createQueryBuilder('pr')
            .select('pr.status', 'status')
            .addSelect('COUNT(*)', 'count')
            .where('pr.projectId = :projectId', { projectId })
            .groupBy('pr.status')
            .getRawMany();
        const totalMembers = await this.projectRoleRepository.count({
            where: { projectId, status: 'accepted' }
        });
        return {
            roleDistribution: roleStats,
            statusDistribution: statusStats,
            totalMembers,
            projectId
        };
    }
};
exports.ProjectPermissionService = ProjectPermissionService;
exports.ProjectPermissionService = ProjectPermissionService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(user_roles_entity_1.UserProjectRole)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, role_management_service_1.RoleManagementService])
], ProjectPermissionService);
//# sourceMappingURL=project-permission.service.js.map