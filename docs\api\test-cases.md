# API 接口测试用例

## 测试环境配置

### 环境变量
```
BASE_URL=http://localhost:3000/api/v1
ADMIN_USERNAME=<EMAIL>
ADMIN_PASSWORD=admin123
ORGANIZER_USERNAME=<EMAIL>
ORGANIZER_PASSWORD=organizer123
```

### 测试数据准备
```sql
-- 创建测试用户
INSERT INTO users (id, username, email, password, role, status) VALUES
('admin-uuid', '<EMAIL>', '<EMAIL>', '$2a$10$...', 'super_admin', 1),
('organizer-uuid', '<EMAIL>', '<EMAIL>', '$2a$10$...', 'organizer', 1),
('judge-uuid', '<EMAIL>', '<EMAIL>', '$2a$10$...', 'judge', 1);

-- 创建测试项目
INSERT INTO projects (id, name, organizer_id, status) VALUES
('project-uuid', '测试项目', 'organizer-uuid', 'judging');
```

## 认证接口测试

### 1. 用户登录测试
```http
POST /auth/login
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "admin123"
}
```

**期望结果**:
- 状态码: 200
- 响应包含 accessToken 和 refreshToken
- 用户信息正确

**测试用例**:
- ✅ 正确的用户名和密码
- ❌ 错误的用户名
- ❌ 错误的密码
- ❌ 空用户名或密码
- ❌ 被禁用的用户

### 2. 微信登录测试
```http
POST /auth/wechat-login
Content-Type: application/json

{
  "code": "mock_wx_code",
  "userInfo": {
    "nickname": "测试用户",
    "avatarUrl": "https://example.com/avatar.jpg"
  }
}
```

**测试用例**:
- ✅ 新用户首次登录
- ✅ 已存在用户登录
- ❌ 无效的微信 code

## 项目管理接口测试

### 1. 创建项目测试
```http
POST /projects
Authorization: Bearer {organizer_token}
Content-Type: application/json

{
  "name": "2024年测试书法大赛",
  "description": "这是一个测试项目",
  "collectStartTime": "2024-01-01T00:00:00Z",
  "collectEndTime": "2024-01-31T23:59:59Z",
  "judgeStartTime": "2024-02-01T00:00:00Z",
  "judgeEndTime": "2024-02-15T23:59:59Z",
  "isPublic": true
}
```

**测试用例**:
- ✅ 办展方创建项目
- ❌ 普通用户创建项目（权限不足）
- ❌ 缺少必填字段
- ❌ 时间设置不合理

### 2. 项目状态流转测试
```http
PATCH /projects/{projectId}/status
Authorization: Bearer {organizer_token}
Content-Type: application/json

{
  "status": "collecting"
}
```

**测试用例**:
- ✅ preparing → collecting
- ✅ collecting → reviewing
- ✅ reviewing → judging
- ✅ judging → displaying
- ✅ displaying → finished
- ❌ 跳跃式状态变更
- ❌ 逆向状态变更

## 作品管理接口测试

### 1. 上传作品测试
```http
POST /artworks
Authorization: Bearer {organizer_token}
Content-Type: multipart/form-data

projectId: project-uuid
title: 测试作品
authorName: 测试作者
image: [binary file data]
```

**测试用例**:
- ✅ 正常上传作品
- ❌ 缺少图片文件
- ❌ 图片格式不支持
- ❌ 图片大小超限
- ❌ 缺少必填字段

### 2. Excel 导入测试
```http
POST /artworks/import-excel
Authorization: Bearer {organizer_token}
Content-Type: multipart/form-data

projectId: project-uuid
excel: [Excel file data]
```

**测试用例**:
- ✅ 正确格式的 Excel 文件
- ❌ 错误格式的文件
- ❌ 缺少必填列
- ❌ 数据格式错误

## 评分系统接口测试

### 1. 创建评分测试
```http
POST /scores
Authorization: Bearer {judge_token}
Content-Type: application/json

{
  "projectId": "project-uuid",
  "artworkId": "artwork-uuid",
  "objectivePass": true,
  "subjectiveScores": {
    "临帖传承": 25,
    "结体结构": 28,
    "艺术创意": 35
  },
  "totalScore": 88
}
```

**测试用例**:
- ✅ 评委正常评分
- ❌ 重复评分
- ❌ 非评委用户评分
- ❌ 评分时间已过
- ❌ 评分数据不完整

### 2. 评语功能测试
```http
POST /scores/{scoreId}/comments
Authorization: Bearer {judge_token}
Content-Type: application/json

{
  "content": "整体表现优秀，细节处理到位",
  "annotations": [
    {
      "x": 100,
      "y": 200,
      "width": 50,
      "height": 30,
      "note": "此处笔法精妙"
    }
  ],
  "isPublic": true
}
```

**测试用例**:
- ✅ 添加文字评语
- ✅ 添加图片标注
- ✅ 上传语音评语
- ❌ 非评分创建者添加评语

## 申请管理接口测试

### 1. 提交申请测试
```http
POST /applications
Authorization: Bearer {user_token}
Content-Type: application/json

{
  "organizationName": "测试书法协会",
  "organizationType": "association",
  "contactPerson": "张主任",
  "contactPhone": "13888888888",
  "contactEmail": "<EMAIL>",
  "introduction": "这是一个测试申请"
}
```

**测试用例**:
- ✅ 首次提交申请
- ❌ 重复提交申请
- ❌ 缺少必填字段
- ❌ 手机号格式错误

### 2. 申请审批测试
```http
PATCH /applications/{applicationId}/review
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "status": "approved",
  "reviewComment": "申请材料齐全，同意通过"
}
```

**测试用例**:
- ✅ 管理员通过申请
- ✅ 管理员拒绝申请
- ❌ 普通用户审批申请
- ❌ 审批已处理的申请

## 文件上传接口测试

### 1. 图片上传测试
```http
POST /files/upload/image
Authorization: Bearer {token}
Content-Type: multipart/form-data

file: [image file]
folder: artworks
```

**测试用例**:
- ✅ JPG 格式图片
- ✅ PNG 格式图片
- ❌ 不支持的格式
- ❌ 文件大小超限
- ❌ 空文件

### 2. 缩略图生成测试
```http
POST /files/thumbnail
Authorization: Bearer {token}
Content-Type: application/json

{
  "imageUrl": "/uploads/artworks/test.jpg",
  "width": 300,
  "height": 300
}
```

## 权限控制测试

### 1. 角色权限测试
| 接口 | super_admin | admin | organizer | judge | user |
|------|-------------|-------|-----------|-------|------|
| GET /users | ✅ | ✅ | ❌ | ❌ | ❌ |
| POST /projects | ✅ | ✅ | ✅ | ❌ | ❌ |
| POST /scores | ✅ | ✅ | ❌ | ✅ | ❌ |
| PATCH /applications/*/review | ✅ | ✅ | ❌ | ❌ | ❌ |

### 2. 资源权限测试
- ✅ 项目创建者可以管理自己的项目
- ❌ 项目创建者不能管理他人的项目
- ✅ 评委可以查看自己的评分
- ❌ 评委不能查看他人的评分

## 性能测试

### 1. 并发测试
- 100 个并发用户同时登录
- 50 个并发用户同时上传作品
- 20 个评委同时进行评分

### 2. 压力测试
- 单接口 QPS 测试
- 系统整体负载测试
- 数据库连接池测试

## 安全测试

### 1. 认证安全
- Token 过期处理
- 无效 Token 处理
- Token 伪造检测

### 2. 输入验证
- SQL 注入测试
- XSS 攻击测试
- 文件上传安全测试

### 3. 权限绕过
- 越权访问测试
- 权限提升测试
- 敏感信息泄露测试

## 自动化测试脚本

### Jest 测试示例
```javascript
describe('Auth API', () => {
  test('should login successfully with valid credentials', async () => {
    const response = await request(app)
      .post('/api/v1/auth/login')
      .send({
        username: '<EMAIL>',
        password: 'admin123'
      });
    
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.data.accessToken).toBeDefined();
  });
  
  test('should fail with invalid credentials', async () => {
    const response = await request(app)
      .post('/api/v1/auth/login')
      .send({
        username: '<EMAIL>',
        password: 'wrongpassword'
      });
    
    expect(response.status).toBe(401);
    expect(response.body.success).toBe(false);
  });
});
```

## 测试报告模板

### 测试执行结果
- 总测试用例数: 150
- 通过用例数: 145
- 失败用例数: 5
- 测试覆盖率: 96.7%

### 发现的问题
1. 文件上传大小限制提示不够明确
2. 评分时间校验逻辑有漏洞
3. 权限错误信息需要优化

### 建议改进
1. 增加更详细的错误提示
2. 完善边界条件处理
3. 优化接口响应时间
