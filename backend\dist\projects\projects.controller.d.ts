import { ProjectsService } from './projects.service';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { ProjectStatus } from './entities/project.entity';
export declare class ProjectsController {
    private readonly projectsService;
    constructor(projectsService: ProjectsService);
    create(createProjectDto: CreateProjectDto, req: any): Promise<import("./entities/project.entity").Project>;
    findAll(page?: number, limit?: number, status?: ProjectStatus, organizerId?: string, isPublic?: boolean): Promise<{
        data: any;
        total: any;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    findMyProjects(req: any, page?: number): Promise<{
        data: any;
        total: any;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    findOne(id: string): Promise<import("./entities/project.entity").Project>;
    update(id: string, updateProjectDto: UpdateProjectDto, req: any): Promise<import("./entities/project.entity").Project>;
    remove(id: string, req: any): Promise<void>;
    updateStatus(id: string, status: ProjectStatus, req: any): Promise<import("./entities/project.entity").Project>;
    generateQrCode(id: string, req: any): Promise<import("./entities/project.entity").Project>;
}
