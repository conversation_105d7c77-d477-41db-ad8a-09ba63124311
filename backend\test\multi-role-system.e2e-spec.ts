import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { JwtService } from '@nestjs/jwt';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../src/users/entities/user.entity';
import { UserGlobalRole, UserProjectRole, GlobalRole, ProjectRole } from '../src/users/entities/user-roles.entity';

describe('Multi-Role Permission System (e2e)', () => {
  let app: INestApplication;
  let jwtService: JwtService;
  let userRepository: Repository<User>;
  let globalRoleRepository: Repository<UserGlobalRole>;
  let projectRoleRepository: Repository<UserProjectRole>;

  // 测试用户和Token
  let superAdminUser: User;
  let adminUser: User;
  let organizerUser: User;
  let artistUser: User;
  let judgeUser: User;
  let normalUser: User;

  let superAdminToken: string;
  let adminToken: string;
  let organizerToken: string;
  let artistToken: string;
  let judgeToken: string;
  let normalUserToken: string;

  const testProjectId = 'test-project-123';

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    jwtService = moduleFixture.get<JwtService>(JwtService);
    userRepository = moduleFixture.get<Repository<User>>(getRepositoryToken(User));
    globalRoleRepository = moduleFixture.get<Repository<UserGlobalRole>>(getRepositoryToken(UserGlobalRole));
    projectRoleRepository = moduleFixture.get<Repository<UserProjectRole>>(getRepositoryToken(UserProjectRole));

    await app.init();

    // 创建测试用户
    await createTestUsers();
    await createTestTokens();
  });

  afterAll(async () => {
    await cleanupTestData();
    await app.close();
  });

  describe('Global Role Management', () => {
    describe('Role Applications', () => {
      it('should allow user to apply for organizer role', () => {
        return request(app.getHttpServer())
          .post('/api/v1/common/role-applications')
          .set('Authorization', `Bearer ${normalUserToken}`)
          .send({
            targetRole: GlobalRole.ORGANIZER,
            applicationData: {
              organizationName: '测试机构',
              organizationType: '文化机构',
              contactPerson: '张三',
              contactPhone: '13800138000',
              qualifications: ['机构资质证书'],
              experience: '5年组织经验',
              reason: '希望组织书画比赛'
            }
          })
          .expect(201)
          .expect((res) => {
            expect(res.body.success).toBe(true);
            expect(res.body.data.targetRole).toBe(GlobalRole.ORGANIZER);
            expect(res.body.data.status).toBe('pending');
          });
      });

      it('should prevent duplicate role applications', () => {
        return request(app.getHttpServer())
          .post('/api/v1/common/role-applications')
          .set('Authorization', `Bearer ${normalUserToken}`)
          .send({
            targetRole: GlobalRole.ORGANIZER,
            applicationData: {
              organizationName: '测试机构2',
              reason: '重复申请测试'
            }
          })
          .expect(409)
          .expect((res) => {
            expect(res.body.success).toBe(false);
            expect(res.body.message).toContain('待处理的申请');
          });
      });

      it('should allow admin to review role applications', async () => {
        // 先获取申请列表
        const applicationsRes = await request(app.getHttpServer())
          .get('/api/v1/admin/role-management/applications')
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);

        const application = applicationsRes.body.data.data[0];

        // 审批申请
        return request(app.getHttpServer())
          .patch(`/api/v1/admin/role-management/applications/${application.id}/review`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send({
            approved: true,
            reviewComment: '申请材料完整，批准通过'
          })
          .expect(200)
          .expect((res) => {
            expect(res.body.success).toBe(true);
            expect(res.body.data.action).toBe('approved');
          });
      });
    });

    describe('Role Assignment', () => {
      it('should allow admin to directly assign global roles', () => {
        return request(app.getHttpServer())
          .post('/api/v1/admin/role-management/assign-role')
          .set('Authorization', `Bearer ${adminToken}`)
          .send({
            userId: artistUser.id,
            role: GlobalRole.JUDGE,
            metadata: {
              reason: '专业书法家，具备评委资格',
              certifications: ['中国书法家协会会员']
            }
          })
          .expect(201)
          .expect((res) => {
            expect(res.body.success).toBe(true);
            expect(res.body.data.role).toBe(GlobalRole.JUDGE);
          });
      });

      it('should allow batch role assignment', () => {
        return request(app.getHttpServer())
          .post('/api/v1/admin/role-management/batch-assign')
          .set('Authorization', `Bearer ${adminToken}`)
          .send({
            assignments: [
              {
                userId: normalUser.id,
                role: GlobalRole.ARTIST,
                metadata: { reason: '批量分配测试' }
              }
            ]
          })
          .expect(201)
          .expect((res) => {
            expect(res.body.success).toBe(true);
            expect(res.body.data.successful).toBe(1);
          });
      });
    });

    describe('Multi-Role Support', () => {
      it('should allow user to have multiple global roles', async () => {
        // 用户现在应该既是artist又是judge
        const rolesRes = await request(app.getHttpServer())
          .get(`/api/v1/admin/role-management/users/${artistUser.id}/roles`)
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);

        const roles = rolesRes.body.data.roles.map(r => r.role);
        expect(roles).toContain(GlobalRole.ARTIST);
        expect(roles).toContain(GlobalRole.JUDGE);
      });
    });
  });

  describe('Project Permission Management', () => {
    describe('Project Member Invitation', () => {
      it('should allow organizer to invite users to project', () => {
        return request(app.getHttpServer())
          .post(`/api/v1/organizer/projects/${testProjectId}/permissions/invite`)
          .set('Authorization', `Bearer ${organizerToken}`)
          .send({
            userId: artistUser.id,
            role: ProjectRole.PROJECT_JUDGE,
            permissions: {
              canScore: true,
              canViewScores: true,
              canExportData: false
            },
            message: '邀请您担任本次比赛的评委',
            roleDescription: '书法专业评委'
          })
          .expect(201)
          .expect((res) => {
            expect(res.body.success).toBe(true);
            expect(res.body.data.invitedRole).toBe(ProjectRole.PROJECT_JUDGE);
          });
      });

      it('should allow user to accept project invitation', () => {
        return request(app.getHttpServer())
          .post(`/api/v1/common/role-applications/project-invitations/${testProjectId}/accept`)
          .set('Authorization', `Bearer ${artistToken}`)
          .expect(200)
          .expect((res) => {
            expect(res.body.success).toBe(true);
            expect(res.body.data.status).toBe('accepted');
          });
      });

      it('should allow batch user invitation', () => {
        return request(app.getHttpServer())
          .post(`/api/v1/organizer/projects/${testProjectId}/permissions/batch-invite`)
          .set('Authorization', `Bearer ${organizerToken}`)
          .send({
            invitations: [
              {
                userId: judgeUser.id,
                role: ProjectRole.PROJECT_JUDGE,
                message: '批量邀请测试'
              },
              {
                userId: normalUser.id,
                role: ProjectRole.PROJECT_ARTIST,
                message: '邀请参赛'
              }
            ]
          })
          .expect(201)
          .expect((res) => {
            expect(res.body.success).toBe(true);
            expect(res.body.data.total).toBe(2);
          });
      });
    });

    describe('Permission Management', () => {
      it('should allow project manager to update member permissions', () => {
        return request(app.getHttpServer())
          .patch(`/api/v1/organizer/projects/${testProjectId}/permissions/members/${artistUser.id}/permissions`)
          .set('Authorization', `Bearer ${organizerToken}`)
          .send({
            permissions: {
              canScore: true,
              canViewScores: true,
              canExportData: true, // 新增权限
              canModerateContent: false
            }
          })
          .expect(200)
          .expect((res) => {
            expect(res.body.success).toBe(true);
            expect(res.body.data.newPermissions.canExportData).toBe(true);
          });
      });

      it('should get project members list', () => {
        return request(app.getHttpServer())
          .get(`/api/v1/organizer/projects/${testProjectId}/permissions/members`)
          .set('Authorization', `Bearer ${organizerToken}`)
          .expect(200)
          .expect((res) => {
            expect(res.body.success).toBe(true);
            expect(res.body.data.data.length).toBeGreaterThan(0);
          });
      });
    });

    describe('Permission Checking', () => {
      it('should allow project judge to access scoring endpoint', () => {
        return request(app.getHttpServer())
          .get(`/api/v1/examples/multi-role/projects/${testProjectId}/judging`)
          .set('Authorization', `Bearer ${artistToken}`) // artistUser现在是项目评委
          .expect(200)
          .expect((res) => {
            expect(res.body.success).toBe(true);
            expect(res.body.data.permissions.canScore).toBe(true);
          });
      });

      it('should deny access to users without proper permissions', () => {
        return request(app.getHttpServer())
          .get(`/api/v1/examples/multi-role/projects/${testProjectId}/export-data`)
          .set('Authorization', `Bearer ${normalUserToken}`)
          .expect(403);
      });

      it('should allow conditional access based on permissions', () => {
        return request(app.getHttpServer())
          .get(`/api/v1/examples/multi-role/projects/${testProjectId}/conditional-data`)
          .set('Authorization', `Bearer ${artistToken}`)
          .expect(200)
          .expect((res) => {
            expect(res.body.success).toBe(true);
            expect(res.body.data.permissions.canScore).toBe(true);
            expect(res.body.data.judgeData).toBeDefined();
          });
      });
    });
  });

  describe('Statistics and Reporting', () => {
    it('should get role statistics', () => {
      return request(app.getHttpServer())
        .get('/api/v1/admin/role-management/statistics')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.globalRoles).toBeDefined();
          expect(res.body.data.totalUsers).toBeGreaterThan(0);
        });
    });

    it('should get project permission statistics', () => {
      return request(app.getHttpServer())
        .get(`/api/v1/organizer/projects/${testProjectId}/permissions/statistics`)
        .set('Authorization', `Bearer ${organizerToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.totalMembers).toBeGreaterThan(0);
        });
    });
  });

  // 辅助函数
  async function createTestUsers() {
    // 创建测试用户
    superAdminUser = await userRepository.save({
      username: 'superadmin',
      email: '<EMAIL>',
      password: 'password123',
      status: 1
    });

    adminUser = await userRepository.save({
      username: 'admin',
      email: '<EMAIL>',
      password: 'password123',
      status: 1
    });

    organizerUser = await userRepository.save({
      username: 'organizer',
      email: '<EMAIL>',
      password: 'password123',
      status: 1
    });

    artistUser = await userRepository.save({
      username: 'artist',
      email: '<EMAIL>',
      password: 'password123',
      status: 1
    });

    judgeUser = await userRepository.save({
      username: 'judge',
      email: '<EMAIL>',
      password: 'password123',
      status: 1
    });

    normalUser = await userRepository.save({
      username: 'user',
      email: '<EMAIL>',
      password: 'password123',
      status: 1
    });

    // 分配全局角色
    await globalRoleRepository.save([
      { userId: superAdminUser.id, role: GlobalRole.SUPER_ADMIN, isActive: true },
      { userId: adminUser.id, role: GlobalRole.ADMIN, isActive: true },
      { userId: organizerUser.id, role: GlobalRole.ORGANIZER, isActive: true },
      { userId: artistUser.id, role: GlobalRole.ARTIST, isActive: true },
      { userId: judgeUser.id, role: GlobalRole.JUDGE, isActive: true },
      { userId: normalUser.id, role: GlobalRole.USER, isActive: true },
    ]);

    // 创建项目角色（organizerUser是项目所有者）
    await projectRoleRepository.save({
      userId: organizerUser.id,
      projectId: testProjectId,
      role: ProjectRole.PROJECT_OWNER,
      permissions: {
        canManageProject: true,
        canInviteUsers: true,
        canManageArtworks: true,
        canViewScores: true,
        canExportData: true,
        canModerateContent: true,
        canManageSchedule: true
      },
      status: 'accepted'
    });
  }

  async function createTestTokens() {
    superAdminToken = jwtService.sign({ sub: superAdminUser.id, username: superAdminUser.username });
    adminToken = jwtService.sign({ sub: adminUser.id, username: adminUser.username });
    organizerToken = jwtService.sign({ sub: organizerUser.id, username: organizerUser.username });
    artistToken = jwtService.sign({ sub: artistUser.id, username: artistUser.username });
    judgeToken = jwtService.sign({ sub: judgeUser.id, username: judgeUser.username });
    normalUserToken = jwtService.sign({ sub: normalUser.id, username: normalUser.username });
  }

  async function cleanupTestData() {
    // 清理测试数据
    await projectRoleRepository.delete({ projectId: testProjectId });
    await globalRoleRepository.delete({});
    await userRepository.delete({});
  }
});
