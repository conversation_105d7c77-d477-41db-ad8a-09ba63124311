import { Repository } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { UserGlobalRole, UserProjectRole, GlobalRole, ProjectRole } from '../../users/entities/user-roles.entity';
export declare class PermissionService {
    private userRepository;
    private globalRoleRepository;
    private projectRoleRepository;
    constructor(userRepository: Repository<User>, globalRoleRepository: Repository<UserGlobalRole>, projectRoleRepository: Repository<UserProjectRole>);
    hasGlobalPermission(userId: string, resource: string, action: string): Promise<boolean>;
    hasProjectPermission(userId: string, projectId: string, permission: string): Promise<boolean>;
    addGlobalRole(userId: string, role: GlobalRole, grantedBy: string, metadata?: any): Promise<UserGlobalRole>;
    removeGlobalRole(userId: string, role: GlobalRole): Promise<void>;
    inviteUserToProject(projectId: string, userId: string, role: ProjectRole, permissions: any, invitedBy: string, metadata?: any): Promise<UserProjectRole>;
    acceptProjectInvitation(userId: string, projectId: string): Promise<void>;
    updateProjectRolePermissions(userId: string, projectId: string, permissions: any): Promise<void>;
    getUserProjectRoles(userId: string, projectId: string): Promise<UserProjectRole[]>;
    getProjectMembers(projectId: string): Promise<UserProjectRole[]>;
    private checkRolePermissions;
    private checkProjectPermission;
    getDefaultProjectPermissions(role: ProjectRole): any;
}
