{"version": 3, "file": "user-roles.entity.js", "sourceRoot": "", "sources": ["../../../src/users/entities/user-roles.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCASiB;AACjB,+CAAqC;AAKrC,IAAY,UAOX;AAPD,WAAY,UAAU;IACpB,yCAA2B,CAAA;IAC3B,6BAAe,CAAA;IACf,qCAAuB,CAAA;IACvB,+BAAiB,CAAA;IACjB,6BAAe,CAAA;IACf,2BAAa,CAAA;AACf,CAAC,EAPW,UAAU,0BAAV,UAAU,QAOrB;AAKD,IAAY,WAQX;AARD,WAAY,WAAW;IACrB,8CAA+B,CAAA;IAC/B,8CAA+B,CAAA;IAC/B,8CAA+B,CAAA;IAC/B,gDAAiC,CAAA;IACjC,gDAAiC,CAAA;IACjC,sDAAuC,CAAA;IACvC,sDAAuC,CAAA;AACzC,CAAC,EARW,WAAW,2BAAX,WAAW,QAQtB;AAQM,IAAM,cAAc,GAApB,MAAM,cAAc;IAEzB,EAAE,CAAS;IAGX,MAAM,CAAS;IAIf,IAAI,CAAO;IAGX,IAAI,CAAa;IAGjB,QAAQ,CAMN;IAGF,QAAQ,CAAU;IAGlB,SAAS,CAAO;IAGhB,SAAS,CAAO;CACjB,CAAA;AA/BY,wCAAc;AAEzB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;0CACpB;AAGX;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;;8CACb;AAIf;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kBAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;IACxE,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;8BAC1B,kBAAI;4CAAC;AAGX;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;;4CAC1B;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDAOvC;AAGF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;gDACzB;AAGlB;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;iDAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;iDAAC;yBA9BL,cAAc;IAF1B,IAAA,gBAAM,EAAC,mBAAmB,CAAC;IAC3B,IAAA,eAAK,EAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;GAC/B,cAAc,CA+B1B;AAQM,IAAM,eAAe,GAArB,MAAM,eAAe;IAE1B,EAAE,CAAS;IAGX,MAAM,CAAS;IAIf,IAAI,CAAO;IAGX,SAAS,CAAS;IAGlB,IAAI,CAAc;IAGlB,WAAW,CAUT;IAGF,QAAQ,CAMN;IAGF,MAAM,CAAkD;IAGxD,SAAS,CAAO;IAGhB,SAAS,CAAO;IAGhB,SAAS,CAAO;CACjB,CAAA;AAlDY,0CAAe;AAE1B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;2CACpB;AAGX;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;;+CACb;AAIf;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kBAAI,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;IAC9C,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;8BAC1B,kBAAI;6CAAC;AAGX;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;;kDACb;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;;6CAC1B;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;oDAWvC;AAGF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDAOvC;AAGF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;;+CAC3C;AAGxD;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;8BACnC,IAAI;kDAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;kDAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;kDAAC;0BAjDL,eAAe;IAF3B,IAAA,gBAAM,EAAC,oBAAoB,CAAC;IAC5B,IAAA,eAAK,EAAC,CAAC,QAAQ,EAAE,WAAW,EAAE,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;GAC5C,eAAe,CAkD3B;AAOM,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAE7B,EAAE,CAAS;IAGX,IAAI,CAAS;IAGb,IAAI,CAAc;IAGlB,WAAW,CAUT;IAGF,WAAW,CAAS;IAGpB,SAAS,CAAU;IAGnB,QAAQ,CAAU;IAGlB,SAAS,CAAO;IAGhB,SAAS,CAAO;CACjB,CAAA;AArCY,gDAAkB;AAE7B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;8CACpB;AAGX;IADC,IAAA,gBAAM,GAAE;;gDACI;AAGb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;;gDAC1B;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;uDAWvB;AAGF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uDACrB;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;qDACxB;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;oDACzB;AAGlB;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;qDAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;qDAAC;6BApCL,kBAAkB;IAD9B,IAAA,gBAAM,EAAC,sBAAsB,CAAC;GAClB,kBAAkB,CAqC9B;AAMM,IAAM,eAAe,GAArB,MAAM,eAAe;IAE1B,EAAE,CAAS;IAGX,MAAM,CAAS;IAIf,IAAI,CAAO;IAGX,UAAU,CAAa;IAGvB,eAAe,CASb;IAGF,MAAM,CAAoD;IAG1D,aAAa,CAAS;IAGtB,UAAU,CAAS;IAInB,QAAQ,CAAO;IAGf,SAAS,CAAO;IAGhB,UAAU,CAAO;CAClB,CAAA;AA5CY,0CAAe;AAE1B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;2CACpB;AAGX;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;;+CACb;AAIf;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kBAAI,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;IAC9C,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;8BAC1B,kBAAI;6CAAC;AAGX;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;;mDACpB;AAGvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;wDAUvB;AAGF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;;+CAC3C;AAG1D;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDACnB;AAGtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAC7B;AAInB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kBAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACzC,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;8BAC1B,kBAAI;iDAAC;AAGf;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;kDAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;8BACP,IAAI;mDAAC;0BA3CN,eAAe;IAD3B,IAAA,gBAAM,EAAC,mBAAmB,CAAC;GACf,eAAe,CA4C3B"}