"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const project_entity_1 = require("./entities/project.entity");
const user_entity_1 = require("../users/entities/user.entity");
let ProjectsService = class ProjectsService {
    projectRepository;
    constructor(projectRepository) {
        this.projectRepository = projectRepository;
    }
    async create(createProjectDto, organizerId) {
        const project = this.projectRepository.create({
            ...createProjectDto,
            organizerId,
        });
        return await this.projectRepository.save(project);
    }
    async findAll(options) {
        const { page, limit, status, organizerId, isPublic } = options;
        const queryBuilder = this.projectRepository
            .createQueryBuilder('project')
            .leftJoinAndSelect('project.organizer', 'organizer');
        if (status) {
            queryBuilder.andWhere('project.status = :status', { status });
        }
        if (organizerId) {
            queryBuilder.andWhere('project.organizerId = :organizerId', { organizerId });
        }
        if (isPublic !== undefined) {
            queryBuilder.andWhere('project.isPublic = :isPublic', { isPublic: isPublic ? 1 : 0 });
        }
        queryBuilder
            .orderBy('project.createdAt', 'DESC')
            .skip((page - 1) * limit)
            .take(limit);
        const [projects, total] = await queryBuilder.getManyAndCount();
        return {
            data: projects,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
        };
    }
    async findByOrganizer(organizerId, page = 1, limit = 10) {
        return this.findAll({ page, limit, organizerId });
    }
    async findOne(id) {
        const project = await this.projectRepository.findOne({
            where: { id },
            relations: ['organizer'],
        });
        if (!project) {
            throw new common_1.NotFoundException('项目不存在');
        }
        return project;
    }
    async update(id, updateProjectDto, user) {
        const project = await this.findOne(id);
        if (user.role !== user_entity_1.UserRole.SUPER_ADMIN &&
            user.role !== user_entity_1.UserRole.ADMIN &&
            project.organizerId !== user.id) {
            throw new common_1.ForbiddenException('无权限修改此项目');
        }
        Object.assign(project, updateProjectDto);
        return await this.projectRepository.save(project);
    }
    async updateStatus(id, status, user) {
        const project = await this.findOne(id);
        if (user.role !== user_entity_1.UserRole.SUPER_ADMIN &&
            user.role !== user_entity_1.UserRole.ADMIN &&
            project.organizerId !== user.id) {
            throw new common_1.ForbiddenException('无权限修改此项目状态');
        }
        this.validateStatusTransition(project.status, status);
        project.status = status;
        return await this.projectRepository.save(project);
    }
    async remove(id, user) {
        const project = await this.findOne(id);
        if (user.role !== user_entity_1.UserRole.SUPER_ADMIN &&
            user.role !== user_entity_1.UserRole.ADMIN &&
            project.organizerId !== user.id) {
            throw new common_1.ForbiddenException('无权限删除此项目');
        }
        if (project.status !== project_entity_1.ProjectStatus.PREPARING) {
            throw new common_1.BadRequestException('只有准备中的项目才能删除');
        }
        await this.projectRepository.remove(project);
    }
    async generateQrCode(id, user) {
        const project = await this.findOne(id);
        if (user.role !== user_entity_1.UserRole.SUPER_ADMIN &&
            user.role !== user_entity_1.UserRole.ADMIN &&
            project.organizerId !== user.id) {
            throw new common_1.ForbiddenException('无权限生成二维码');
        }
        const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(`project:${id}`)}`;
        project.qrCode = qrCodeUrl;
        return await this.projectRepository.save(project);
    }
    validateStatusTransition(currentStatus, newStatus) {
        const validTransitions = {
            [project_entity_1.ProjectStatus.PREPARING]: [project_entity_1.ProjectStatus.COLLECTING],
            [project_entity_1.ProjectStatus.COLLECTING]: [project_entity_1.ProjectStatus.REVIEWING],
            [project_entity_1.ProjectStatus.REVIEWING]: [project_entity_1.ProjectStatus.JUDGING],
            [project_entity_1.ProjectStatus.JUDGING]: [project_entity_1.ProjectStatus.DISPLAYING],
            [project_entity_1.ProjectStatus.DISPLAYING]: [project_entity_1.ProjectStatus.FINISHED],
            [project_entity_1.ProjectStatus.FINISHED]: [],
        };
        if (!validTransitions[currentStatus]?.includes(newStatus)) {
            throw new common_1.BadRequestException(`无法从 ${currentStatus} 状态转换到 ${newStatus} 状态`);
        }
    }
};
exports.ProjectsService = ProjectsService;
exports.ProjectsService = ProjectsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(project_entity_1.Project)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object])
], ProjectsService);
//# sourceMappingURL=projects.service.js.map