import { BaseController } from '../../common/base/base.controller';
import { UsersService } from '../../users/users.service';
import { CreateUserDto } from '../../users/dto/create-user.dto';
import { UpdateUserDto } from '../../users/dto/update-user.dto';
import { UserRole } from '../../users/entities/user.entity';
export declare class AdminUsersController extends BaseController {
    private readonly usersService;
    constructor(usersService: UsersService);
    create(createUserDto: CreateUserDto, req: any): Promise<import("../../common/base/base.controller").ApiResponse<import("../../users/entities/user.entity").User> | import("../../common/base/base.controller").ApiErrorResponse>;
    findAll(query: any, req: any): Promise<import("../../common/base/base.controller").PaginatedResponse<unknown>>;
    findOne(id: string, req: any): Promise<import("../../common/base/base.controller").ApiResponse<import("../../users/entities/user.entity").User> | import("../../common/base/base.controller").ApiErrorResponse>;
    update(id: string, updateUserDto: UpdateUserDto, req: any): Promise<import("../../common/base/base.controller").ApiResponse<import("../../users/entities/user.entity").User> | import("../../common/base/base.controller").ApiErrorResponse>;
    updateStatus(id: string, status: number, req: any): Promise<import("../../common/base/base.controller").ApiResponse<import("../../users/entities/user.entity").User> | import("../../common/base/base.controller").ApiErrorResponse>;
    updateRole(id: string, role: UserRole, req: any): Promise<import("../../common/base/base.controller").ApiResponse<import("../../users/entities/user.entity").User> | import("../../common/base/base.controller").ApiErrorResponse>;
    remove(id: string, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<null>>;
    batchRemove(ids: string[], req: any): Promise<import("../../common/base/base.controller").ApiResponse<any>>;
    resetPassword(id: string, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<{
        temporaryPassword: any;
    }>>;
    getStats(req: any): Promise<import("../../common/base/base.controller").ApiResponse<any>>;
    exportUsers(query: any, req: any): Promise<import("../../common/base/base.controller").ApiResponse<any>>;
}
