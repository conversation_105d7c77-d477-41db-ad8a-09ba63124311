import {
  Controller,
  Get,
  Post,
  Body,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { BaseController } from '../../common/base/base.controller';
import { AuthService } from '../../auth/auth.service';
import { LoginDto } from '../../auth/dto/login.dto';
import { WechatLoginDto } from '../../auth/dto/wechat-login.dto';
import { RegisterDto } from '../../auth/dto/register.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

/**
 * 公共认证控制器
 * 路由前缀: /api/v1/common/auth
 */
@ApiTags('公共接口-用户认证')
@Controller('common/auth')
export class CommonAuthController extends BaseController {
  constructor(private readonly authService: AuthService) {
    super();
  }

  /**
   * 用户登录
   */
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '用户登录' })
  @ApiResponse({ status: 200, description: '登录成功' })
  @ApiResponse({ status: 401, description: '用户名或密码错误' })
  @ApiResponse({ status: 403, description: '账户已被禁用' })
  async login(@Body() loginDto: LoginDto, @Request() req) {
    try {
      const result = await this.authService.login(loginDto);
      return this.success(result, '登录成功', req);
    } catch (error) {
      if (error.message.includes('用户名或密码错误')) {
        return this.error(
          '用户名或密码错误',
          this.BusinessCode.UNAUTHORIZED,
          'AUTHENTICATION_ERROR',
          undefined,
          undefined,
          req
        );
      }
      if (error.message.includes('已被禁用')) {
        return this.error(
          '账户已被禁用，请联系管理员',
          this.BusinessCode.USER_DISABLED,
          'AUTHENTICATION_ERROR',
          undefined,
          undefined,
          req
        );
      }
      throw error;
    }
  }

  /**
   * 微信小程序登录
   */
  @Post('wechat-login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '微信小程序登录' })
  @ApiResponse({ status: 200, description: '登录成功' })
  @ApiResponse({ status: 400, description: '微信登录失败' })
  async wechatLogin(@Body() wechatLoginDto: WechatLoginDto, @Request() req) {
    try {
      const result = await this.authService.wechatLogin(wechatLoginDto);
      return this.success(result, '微信登录成功', req);
    } catch (error) {
      if (error.message.includes('微信登录失败')) {
        return this.error(
          '微信登录失败，请重试',
          this.BusinessCode.BAD_REQUEST,
          'AUTHENTICATION_ERROR',
          undefined,
          undefined,
          req
        );
      }
      throw error;
    }
  }

  /**
   * 用户注册
   */
  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: '用户注册' })
  @ApiResponse({ status: 201, description: '注册成功' })
  @ApiResponse({ status: 409, description: '用户名或邮箱已存在' })
  @ApiResponse({ status: 422, description: '参数验证失败' })
  async register(@Body() registerDto: RegisterDto, @Request() req) {
    try {
      const result = await this.authService.register(registerDto);
      return this.created(result, '注册成功', req);
    } catch (error) {
      if (error.message.includes('已存在')) {
        return this.conflict(error.message, req);
      }
      if (error.message.includes('验证失败')) {
        return this.validationError(error.details, error.message, req);
      }
      throw error;
    }
  }

  /**
   * 获取当前用户信息
   */
  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取当前用户信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 401, description: 'Token无效或已过期' })
  async getProfile(@Request() req) {
    try {
      const profile = await this.authService.getProfile(req.user.id);
      return this.success(profile, '用户信息获取成功', req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('用户不存在', req);
      }
      throw error;
    }
  }

  /**
   * 刷新Token
   */
  @Post('refresh')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '刷新Token' })
  @ApiResponse({ status: 200, description: 'Token刷新成功' })
  @ApiResponse({ status: 401, description: 'Token无效或已过期' })
  async refreshToken(@Request() req) {
    try {
      const result = await this.authService.refreshToken(req.user);
      return this.success(result, 'Token刷新成功', req);
    } catch (error) {
      throw error;
    }
  }

  /**
   * 退出登录
   */
  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '退出登录' })
  @ApiResponse({ status: 200, description: '退出成功' })
  async logout(@Request() req) {
    try {
      await this.authService.logout(req.user.id);
      return this.success(null, '退出登录成功', req);
    } catch (error) {
      throw error;
    }
  }

  /**
   * 修改密码
   */
  @Post('change-password')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '修改密码' })
  @ApiResponse({ status: 200, description: '密码修改成功' })
  @ApiResponse({ status: 400, description: '原密码错误' })
  async changePassword(
    @Body('oldPassword') oldPassword: string,
    @Body('newPassword') newPassword: string,
    @Request() req
  ) {
    try {
      await this.authService.changePassword(req.user.id, oldPassword, newPassword);
      return this.success(null, '密码修改成功', req);
    } catch (error) {
      if (error.message.includes('原密码错误')) {
        return this.error(
          '原密码错误',
          this.BusinessCode.BAD_REQUEST,
          'BUSINESS_ERROR',
          undefined,
          undefined,
          req
        );
      }
      throw error;
    }
  }

  /**
   * 忘记密码 - 发送重置邮件
   */
  @Post('forgot-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '忘记密码 - 发送重置邮件' })
  @ApiResponse({ status: 200, description: '重置邮件发送成功' })
  @ApiResponse({ status: 404, description: '邮箱不存在' })
  async forgotPassword(@Body('email') email: string, @Request() req) {
    try {
      await this.authService.sendPasswordResetEmail(email);
      return this.success(null, '密码重置邮件已发送，请查收', req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('邮箱不存在', req);
      }
      throw error;
    }
  }

  /**
   * 重置密码
   */
  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '重置密码' })
  @ApiResponse({ status: 200, description: '密码重置成功' })
  @ApiResponse({ status: 400, description: '重置令牌无效或已过期' })
  async resetPassword(
    @Body('token') token: string,
    @Body('newPassword') newPassword: string,
    @Request() req
  ) {
    try {
      await this.authService.resetPassword(token, newPassword);
      return this.success(null, '密码重置成功', req);
    } catch (error) {
      if (error.message.includes('令牌无效') || error.message.includes('已过期')) {
        return this.error(
          '重置令牌无效或已过期',
          this.BusinessCode.BAD_REQUEST,
          'BUSINESS_ERROR',
          undefined,
          undefined,
          req
        );
      }
      throw error;
    }
  }

  /**
   * 验证Token有效性
   */
  @Get('verify-token')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '验证Token有效性' })
  @ApiResponse({ status: 200, description: 'Token有效' })
  @ApiResponse({ status: 401, description: 'Token无效或已过期' })
  async verifyToken(@Request() req) {
    return this.success(
      {
        valid: true,
        user: {
          id: req.user.id,
          username: req.user.username,
          role: req.user.role
        }
      },
      'Token验证成功',
      req
    );
  }

  /**
   * 获取登录历史
   */
  @Get('login-history')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取登录历史' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getLoginHistory(@Request() req) {
    try {
      const history = await this.authService.getLoginHistory(req.user.id);
      return this.success(history, '登录历史获取成功', req);
    } catch (error) {
      throw error;
    }
  }
}
