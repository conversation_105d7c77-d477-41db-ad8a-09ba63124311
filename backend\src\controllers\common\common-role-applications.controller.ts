import {
  Controller,
  Get,
  Post,
  Patch,
  Body,
  Param,
  Query,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { BaseController } from '../../common/base/base.controller';
import { RequiredAuth, OptionalAuth } from '../../auth/decorators/auth.decorator';
import { CurrentUser, UserId } from '../../auth/decorators/user.decorator';
import { User } from '../../users/entities/user.entity';
import { RoleManagementService } from '../../auth/services/role-management.service';
import { ProjectPermissionService } from '../../auth/services/project-permission.service';
import {
  ApplyGlobalRoleDto,
  QueryRoleApplicationsDto,
} from '../../users/dto/role-management.dto';

/**
 * 公共角色申请控制器
 * 处理用户角色申请、项目邀请接受等功能
 */
@ApiTags('公共-角色申请')
@Controller('common/role-applications')
export class CommonRoleApplicationsController extends BaseController {
  constructor(
    private readonly roleManagementService: RoleManagementService,
    private readonly projectPermissionService: ProjectPermissionService,
  ) {
    super();
  }

  /**
   * 申请全局角色
   */
  @RequiredAuth()
  @Post()
  @ApiOperation({ summary: '申请全局角色' })
  @ApiResponse({ status: 201, description: '申请提交成功' })
  @ApiResponse({ status: 409, description: '已拥有此角色或有待处理申请' })
  async applyForGlobalRole(
    @Body() applyDto: ApplyGlobalRoleDto,
    @UserId() userId: string,
    @Request() req
  ) {
    try {
      const application = await this.roleManagementService.applyForGlobalRole(
        userId,
        applyDto.targetRole,
        applyDto.applicationData
      );

      return this.created(
        {
          application,
          targetRole: applyDto.targetRole,
          status: 'pending',
          message: '您的申请已提交，请等待管理员审核',
        },
        '角色申请提交成功',
        req
      );
    } catch (error) {
      if (error.message.includes('已经拥有此角色')) {
        return this.conflict('您已经拥有此角色', req);
      }
      if (error.message.includes('待处理的申请')) {
        return this.conflict('您已有待处理的申请，请等待审核结果', req);
      }
      throw error;
    }
  }

  /**
   * 获取我的角色申请
   */
  @RequiredAuth()
  @Get('my-applications')
  @ApiOperation({ summary: '获取我的角色申请' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getMyApplications(
    @UserId() userId: string,
    @Query() query: { page?: number; limit?: number; status?: string },
    @Request() req
  ) {
    try {
      const result = await this.roleManagementService.getRoleApplications({
        userId,
        ...query,
      });

      return this.paginated(
        result.data,
        result.total,
        result.page,
        result.limit,
        '我的申请列表获取成功',
        req
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * 获取我的全局角色
   */
  @RequiredAuth()
  @Get('my-roles')
  @ApiOperation({ summary: '获取我的全局角色' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getMyGlobalRoles(@UserId() userId: string, @Request() req) {
    try {
      const roles = await this.roleManagementService.getUserGlobalRoles(userId);

      return this.success(
        {
          userId,
          roles: roles.map(role => ({
            id: role.id,
            role: role.role,
            isActive: role.isActive,
            metadata: role.metadata,
            createdAt: role.createdAt,
          })),
          totalRoles: roles.length,
          activeRoles: roles.filter(r => r.isActive).length,
        },
        '我的角色列表获取成功',
        req
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * 接受项目邀请
   */
  @RequiredAuth()
  @Post('project-invitations/:projectId/accept')
  @ApiOperation({ summary: '接受项目邀请' })
  @ApiResponse({ status: 200, description: '邀请接受成功' })
  @ApiResponse({ status: 404, description: '邀请不存在或已过期' })
  async acceptProjectInvitation(
    @Param('projectId') projectId: string,
    @UserId() userId: string,
    @Request() req
  ) {
    try {
      await this.projectPermissionService.acceptProjectInvitation(userId, projectId);

      return this.success(
        {
          projectId,
          userId,
          status: 'accepted',
          message: '您已成功加入项目',
        },
        '项目邀请接受成功',
        req
      );
    } catch (error) {
      if (error.message.includes('不存在') || error.message.includes('已过期')) {
        return this.notFound('邀请不存在或已过期', req);
      }
      throw error;
    }
  }

  /**
   * 拒绝项目邀请
   */
  @RequiredAuth()
  @Post('project-invitations/:projectId/decline')
  @ApiOperation({ summary: '拒绝项目邀请' })
  @ApiResponse({ status: 200, description: '邀请拒绝成功' })
  @ApiResponse({ status: 404, description: '邀请不存在' })
  async declineProjectInvitation(
    @Param('projectId') projectId: string,
    @UserId() userId: string,
    @Request() req
  ) {
    try {
      await this.projectPermissionService.declineProjectInvitation(userId, projectId);

      return this.success(
        {
          projectId,
          userId,
          status: 'declined',
        },
        '项目邀请拒绝成功',
        req
      );
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('邀请不存在', req);
      }
      throw error;
    }
  }

  /**
   * 获取我的项目邀请
   */
  @RequiredAuth()
  @Get('project-invitations')
  @ApiOperation({ summary: '获取我的项目邀请' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getMyProjectInvitations(
    @UserId() userId: string,
    @Query() query: { status?: string; page?: number; limit?: number },
    @Request() req
  ) {
    try {
      // 这里需要实现获取项目邀请的逻辑
      // 暂时返回示例数据
      const mockInvitations = {
        data: [],
        total: 0,
        page: query.page || 1,
        limit: query.limit || 10,
      };

      return this.paginated(
        mockInvitations.data,
        mockInvitations.total,
        mockInvitations.page,
        mockInvitations.limit,
        '我的项目邀请获取成功',
        req
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * 获取我参与的项目
   */
  @RequiredAuth()
  @Get('my-projects')
  @ApiOperation({ summary: '获取我参与的项目' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getMyProjects(
    @UserId() userId: string,
    @Query() query: { role?: string; status?: string; page?: number; limit?: number },
    @Request() req
  ) {
    try {
      // 这里需要实现获取用户参与项目的逻辑
      // 暂时返回示例数据
      const mockProjects = {
        data: [],
        total: 0,
        page: query.page || 1,
        limit: query.limit || 10,
      };

      return this.paginated(
        mockProjects.data,
        mockProjects.total,
        mockProjects.page,
        mockProjects.limit,
        '我的项目列表获取成功',
        req
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * 获取角色申请指南
   */
  @OptionalAuth()
  @Get('guide')
  @ApiOperation({ summary: '获取角色申请指南' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getRoleApplicationGuide(@CurrentUser() user: User | null, @Request() req) {
    try {
      const guide = {
        roles: [
          {
            role: 'organizer',
            name: '办展方',
            description: '可以创建和管理评选项目',
            requirements: [
              '具有合法的机构资质',
              '有组织活动的经验',
              '提供机构证明材料',
            ],
            applicationFields: [
              'organizationName',
              'organizationType',
              'contactPerson',
              'contactPhone',
              'qualifications',
            ],
          },
          {
            role: 'judge',
            name: '评委',
            description: '可以对作品进行专业评审',
            requirements: [
              '具有相关专业背景',
              '有评审经验',
              '提供专业资质证明',
            ],
            applicationFields: [
              'experience',
              'qualifications',
              'reason',
            ],
          },
          {
            role: 'artist',
            name: '美工组/作者',
            description: '可以参与评选活动',
            requirements: [
              '有作品创作能力',
              '同意平台使用条款',
            ],
            applicationFields: [
              'experience',
              'reason',
            ],
          },
        ],
        currentUser: user ? {
          id: user.id,
          username: user.username,
          currentRoles: user.roles,
          canApplyFor: this.getAvailableRoles(user.roles),
        } : null,
        applicationProcess: [
          '填写申请表单',
          '提交相关材料',
          '等待管理员审核',
          '审核通过后获得角色权限',
        ],
      };

      return this.success(
        guide,
        '角色申请指南获取成功',
        req
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * 获取用户可申请的角色
   */
  private getAvailableRoles(currentRoles: string[]): string[] {
    const allRoles = ['organizer', 'judge', 'artist'];
    return allRoles.filter(role => !currentRoles.includes(role));
  }
}
