import { Module } from '@nestjs/common';

// 管理端控制器
import { AdminUsersController } from './admin/admin-users.controller';
import { AdminProjectsController } from './admin/admin-projects.controller';
import { AdminApplicationsController } from './admin/admin-applications.controller';
import { AdminStatsController } from './admin/admin-stats.controller';

// 办展方端控制器
import { OrganizerProjectsController } from './organizer/organizer-projects.controller';
import { OrganizerArtworksController } from './organizer/organizer-artworks.controller';
import { OrganizerScoresController } from './organizer/organizer-scores.controller';

// 小程序端控制器
import { MpProjectsController } from './miniprogram/mp-projects.controller';
import { MpArtworksController } from './miniprogram/mp-artworks.controller';
import { MpScoresController } from './miniprogram/mp-scores.controller';

// 公共控制器
import { CommonAuthController } from './common/common-auth.controller';
import { CommonFilesController } from './common/common-files.controller';

// 示例控制器
import { AuthExamplesController } from './examples/auth-examples.controller';

// 服务模块
import { UsersModule } from '../users/users.module';
import { ProjectsModule } from '../projects/projects.module';
import { ArtworksModule } from '../artworks/artworks.module';
import { ScoresModule } from '../scores/scores.module';
import { ApplicationsModule } from '../applications/applications.module';
import { FilesModule } from '../files/files.module';
import { AuthModule } from '../auth/auth.module';

/**
 * 控制器模块
 * 统一管理所有控制器的注册
 */
@Module({
  imports: [
    AuthModule,
    UsersModule,
    ProjectsModule,
    ArtworksModule,
    ScoresModule,
    ApplicationsModule,
    FilesModule,
  ],
  controllers: [
    // 管理端控制器
    AdminUsersController,
    AdminProjectsController,
    AdminApplicationsController,
    AdminStatsController,

    // 办展方端控制器
    OrganizerProjectsController,
    OrganizerArtworksController,
    OrganizerScoresController,

    // 小程序端控制器
    MpProjectsController,
    MpArtworksController,
    MpScoresController,

    // 公共控制器
    CommonAuthController,
    CommonFilesController,

    // 示例控制器（开发环境）
    AuthExamplesController,
  ],
})
export class ControllersModule {}
