import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { User as UserEntity } from '../../users/entities/user.entity';

/**
 * 当前用户装饰器
 * 从请求中提取当前登录用户信息
 * 
 * @example
 * ```typescript
 * @Get('profile')
 * @RequiredAuth()
 * async getProfile(@CurrentUser() user: User) {
 *   return user;
 * }
 * 
 * // 获取用户ID
 * @Get('my-data')
 * @RequiredAuth()
 * async getMyData(@CurrentUser('id') userId: string) {
 *   return this.service.getUserData(userId);
 * }
 * 
 * // 可选认证场景
 * @Get('articles')
 * @OptionalAuth()
 * async getArticles(@CurrentUser() user?: User) {
 *   if (user) {
 *     return this.getPersonalizedArticles(user.id);
 *   }
 *   return this.getPublicArticles();
 * }
 * ```
 */
export const CurrentUser = createParamDecorator(
  (data: keyof UserEntity | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      return null;
    }

    return data ? user[data] : user;
  },
);

/**
 * 用户ID装饰器
 * 快速获取当前用户ID
 * 
 * @example
 * ```typescript
 * @Post('articles')
 * @RequiredAuth()
 * async createArticle(
 *   @UserId() userId: string,
 *   @Body() createArticleDto: CreateArticleDto
 * ) {
 *   return this.articlesService.create(userId, createArticleDto);
 * }
 * ```
 */
export const UserId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string | null => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    return user ? user.id : null;
  },
);

/**
 * 用户角色装饰器
 * 快速获取当前用户角色
 * 
 * @example
 * ```typescript
 * @Get('dashboard')
 * @RequiredAuth()
 * async getDashboard(@UserRole() role: UserRole) {
 *   switch (role) {
 *     case UserRole.ADMIN:
 *       return this.getAdminDashboard();
 *     case UserRole.ORGANIZER:
 *       return this.getOrganizerDashboard();
 *     default:
 *       return this.getUserDashboard();
 *   }
 * }
 * ```
 */
export const UserRole = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string | null => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    return user ? user.role : null;
  },
);

/**
 * 用户权限检查装饰器
 * 检查当前用户是否具有特定权限
 * 
 * @example
 * ```typescript
 * @Get('sensitive-data')
 * @RequiredAuth()
 * async getSensitiveData(@IsAdmin() isAdmin: boolean) {
 *   if (!isAdmin) {
 *     throw new ForbiddenException('权限不足');
 *   }
 *   return this.getSensitiveData();
 * }
 * ```
 */
export const IsAdmin = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): boolean => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    return user && (user.role === 'admin' || user.role === 'super_admin');
  },
);

/**
 * 检查是否为超级管理员
 */
export const IsSuperAdmin = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): boolean => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    return user && user.role === 'super_admin';
  },
);

/**
 * 检查是否为办展方
 */
export const IsOrganizer = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): boolean => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    return user && user.role === 'organizer';
  },
);

/**
 * 检查是否为评委
 */
export const IsJudge = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): boolean => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    return user && user.role === 'judge';
  },
);

/**
 * 检查用户是否已认证
 */
export const IsAuthenticated = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): boolean => {
    const request = ctx.switchToHttp().getRequest();
    return !!request.user;
  },
);

/**
 * 获取用户的完整信息（包含敏感字段的过滤版本）
 */
export const SafeUser = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    
    if (!user) {
      return null;
    }

    // 返回安全的用户信息，排除敏感字段
    const { password, refreshToken, ...safeUser } = user;
    return safeUser;
  },
);

/**
 * 获取请求的IP地址
 */
export const ClientIP = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest();
    return request.ip || 
           request.connection.remoteAddress || 
           request.socket.remoteAddress ||
           (request.connection.socket ? request.connection.socket.remoteAddress : null) ||
           'unknown';
  },
);

/**
 * 获取用户代理信息
 */
export const UserAgent = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest();
    return request.headers['user-agent'] || 'unknown';
  },
);

/**
 * 获取请求ID
 */
export const RequestId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest();
    return request.headers['x-request-id'] || 'unknown';
  },
);
