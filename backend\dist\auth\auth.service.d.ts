import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { LoginDto } from './dto/login.dto';
import { WechatLoginDto } from './dto/wechat-login.dto';
import { RegisterDto } from './dto/register.dto';
import { User, LoginType } from '../users/entities/user.entity';
export declare class AuthService {
    private usersService;
    private jwtService;
    constructor(usersService: UsersService, jwtService: JwtService);
    login(loginDto: LoginDto): Promise<{
        accessToken: any;
        refreshToken: any;
        user: {
            id: string;
            username: string;
            nickname: string;
            role: import("../users/entities/user.entity").UserRole;
            avatarUrl: string;
        };
    }>;
    register(registerDto: RegisterDto): Promise<{
        accessToken: any;
        refreshToken: any;
        user: {
            id: string;
            username: string;
            nickname: string;
            role: import("../users/entities/user.entity").UserRole;
            avatarUrl: string;
        };
    }>;
    wechatLogin(wechatLoginDto: WechatLoginDto): Promise<{
        accessToken: any;
        refreshToken: any;
        user: {
            id: string;
            username: string;
            nickname: string;
            role: import("../users/entities/user.entity").UserRole;
            avatarUrl: string;
        };
    }>;
    getProfile(userId: string): Promise<{
        id: string;
        openid: string;
        unionid: string;
        username: string;
        email: string;
        nickname: string;
        avatarUrl: string;
        phone: string;
        realName: string;
        role: import("../users/entities/user.entity").UserRole;
        loginType: LoginType;
        status: number;
        createdAt: Date;
        updatedAt: Date;
    }>;
    refreshToken(user: User): Promise<{
        accessToken: any;
        refreshToken: any;
        user: {
            id: string;
            username: string;
            nickname: string;
            role: import("../users/entities/user.entity").UserRole;
            avatarUrl: string;
        };
    }>;
    logout(userId: string): Promise<{
        message: string;
    }>;
    private generateTokens;
    validateUser(userId: string): Promise<User>;
}
