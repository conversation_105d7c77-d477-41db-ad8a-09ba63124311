import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Request,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { BaseController } from '../../common/base/base.controller';

// 导入多角色认证装饰器
import { RequiredAuth } from '../../auth/decorators/auth.decorator';
import {
  ProjectPermission,
  ProjectRoleAuth,
  ProjectOwner,
  ProjectManager,
  ProjectJudge,
  ProjectMember,
  ProjectId,
  CurrentProjectRole,
  CurrentProjectPermissions,
  HasProjectPermission,
  IsProjectOwner,
  IsProjectManager,
} from '../../auth/decorators/project-auth.decorator';

// 导入用户装饰器
import {
  CurrentUser,
  UserId,
} from '../../auth/decorators/user.decorator';

import { User } from '../../users/entities/user.entity';
import { ProjectRole, GlobalRole } from '../../users/entities/user-roles.entity';
import { PermissionService } from '../../auth/services/permission.service';

/**
 * 多角色权限系统示例控制器
 * 演示用户多角色和项目级权限的使用
 */
@ApiTags('多角色权限示例')
@Controller('examples/multi-role')
export class MultiRoleExamplesController extends BaseController {
  constructor(private readonly permissionService: PermissionService) {
    super();
  }

  /**
   * 示例1：查看用户的所有角色
   */
  @RequiredAuth()
  @Get('my-roles')
  @ApiOperation({ summary: '查看我的所有角色' })
  async getMyRoles(@CurrentUser() user: User, @Request() req) {
    return this.success(
      {
        userId: user.id,
        username: user.username,
        globalRoles: user.roles, // 所有全局角色
        primaryRole: user.primaryRole, // 主要角色（兼容性）
        isAdmin: user.isAdmin,
        specialties: user.specialties,
        certificationLevel: user.certificationLevel,
      },
      '用户角色信息获取成功',
      req
    );
  }

  /**
   * 示例2：项目所有者专用功能
   */
  @ProjectOwner()
  @Get('projects/:projectId/owner-dashboard')
  @ApiOperation({ summary: '项目所有者仪表板' })
  async getOwnerDashboard(
    @ProjectId() projectId: string,
    @CurrentUser() user: User,
    @CurrentProjectRole() projectRoles: ProjectRole[],
    @Request() req
  ) {
    return this.success(
      {
        message: '项目所有者仪表板',
        projectId,
        owner: {
          id: user.id,
          username: user.username,
        },
        projectRoles,
        ownerFeatures: [
          '项目设置管理',
          '成员权限管理',
          '项目删除',
          '数据导出',
        ],
      },
      '所有者仪表板获取成功',
      req
    );
  }

  /**
   * 示例3：项目管理者功能（所有者+管理员）
   */
  @ProjectManager()
  @Get('projects/:projectId/management')
  @ApiOperation({ summary: '项目管理功能' })
  async getProjectManagement(
    @ProjectId() projectId: string,
    @CurrentUser() user: User,
    @IsProjectOwner() isOwner: boolean,
    @IsProjectManager() isManager: boolean,
    @Request() req
  ) {
    return this.success(
      {
        message: '项目管理功能',
        projectId,
        manager: {
          id: user.id,
          username: user.username,
        },
        permissions: {
          isOwner,
          isManager,
        },
        managementFeatures: [
          '邀请成员',
          '管理作品',
          '查看评分',
          '内容审核',
        ],
      },
      '项目管理信息获取成功',
      req
    );
  }

  /**
   * 示例4：项目评委功能
   */
  @ProjectJudge()
  @Get('projects/:projectId/judging')
  @ApiOperation({ summary: '评委评审功能' })
  async getJudgingPanel(
    @ProjectId() projectId: string,
    @CurrentUser() user: User,
    @HasProjectPermission('score') canScore: boolean,
    @HasProjectPermission('view_scores') canViewScores: boolean,
    @Request() req
  ) {
    return this.success(
      {
        message: '评委评审面板',
        projectId,
        judge: {
          id: user.id,
          username: user.username,
          specialties: user.specialties,
        },
        permissions: {
          canScore,
          canViewScores,
        },
        judgingFeatures: [
          '作品评分',
          '评语录入',
          '评分历史',
          '评审进度',
        ],
      },
      '评审面板获取成功',
      req
    );
  }

  /**
   * 示例5：项目成员通用功能
   */
  @ProjectMember()
  @Get('projects/:projectId/member-info')
  @ApiOperation({ summary: '项目成员信息' })
  async getMemberInfo(
    @ProjectId() projectId: string,
    @CurrentUser() user: User,
    @CurrentProjectRole() projectRoles: ProjectRole[],
    @CurrentProjectPermissions() permissions: any,
    @Request() req
  ) {
    return this.success(
      {
        message: '项目成员信息',
        projectId,
        member: {
          id: user.id,
          username: user.username,
        },
        projectRoles,
        permissions,
        memberFeatures: [
          '查看项目信息',
          '参与讨论',
          '接收通知',
        ],
      },
      '成员信息获取成功',
      req
    );
  }

  /**
   * 示例6：特定权限检查
   */
  @ProjectPermission('export')
  @Get('projects/:projectId/export-data')
  @ApiOperation({ summary: '导出项目数据' })
  async exportProjectData(
    @ProjectId() projectId: string,
    @CurrentUser() user: User,
    @Request() req
  ) {
    return this.success(
      {
        message: '项目数据导出',
        projectId,
        exporter: {
          id: user.id,
          username: user.username,
        },
        exportData: {
          projects: '项目基本信息',
          artworks: '作品列表',
          scores: '评分数据',
          members: '成员信息',
        },
      },
      '数据导出成功',
      req
    );
  }

  /**
   * 示例7：邀请用户加入项目
   */
  @ProjectPermission('invite')
  @Post('projects/:projectId/invite')
  @ApiOperation({ summary: '邀请用户加入项目' })
  async inviteUser(
    @ProjectId() projectId: string,
    @Body() inviteData: {
      userId: string;
      role: ProjectRole;
      permissions?: any;
      message?: string;
    },
    @CurrentUser() user: User,
    @Request() req
  ) {
    try {
      // 获取默认权限
      const defaultPermissions = this.permissionService.getDefaultProjectPermissions(inviteData.role);
      const permissions = { ...defaultPermissions, ...inviteData.permissions };

      // 邀请用户
      const invitation = await this.permissionService.inviteUserToProject(
        projectId,
        inviteData.userId,
        inviteData.role,
        permissions,
        user.id,
        {
          role_description: `邀请担任${inviteData.role}`,
          message: inviteData.message,
        }
      );

      return this.created(
        {
          invitation,
          inviter: {
            id: user.id,
            username: user.username,
          },
          invitedRole: inviteData.role,
          permissions,
        },
        '用户邀请发送成功',
        req
      );
    } catch (error) {
      if (error.message.includes('已存在')) {
        return this.conflict('用户已在项目中', req);
      }
      throw error;
    }
  }

  /**
   * 示例8：接受项目邀请
   */
  @RequiredAuth()
  @Post('projects/:projectId/accept-invitation')
  @ApiOperation({ summary: '接受项目邀请' })
  async acceptInvitation(
    @ProjectId() projectId: string,
    @UserId() userId: string,
    @Request() req
  ) {
    try {
      await this.permissionService.acceptProjectInvitation(userId, projectId);

      return this.success(
        {
          projectId,
          userId,
          status: 'accepted',
        },
        '项目邀请接受成功',
        req
      );
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('邀请不存在或已过期', req);
      }
      throw error;
    }
  }

  /**
   * 示例9：更新项目成员权限
   */
  @ProjectManager()
  @Patch('projects/:projectId/members/:userId/permissions')
  @ApiOperation({ summary: '更新成员权限' })
  async updateMemberPermissions(
    @ProjectId() projectId: string,
    @Param('userId') targetUserId: string,
    @Body() newPermissions: any,
    @CurrentUser() user: User,
    @Request() req
  ) {
    try {
      await this.permissionService.updateProjectRolePermissions(
        targetUserId,
        projectId,
        newPermissions
      );

      return this.success(
        {
          projectId,
          targetUserId,
          newPermissions,
          updatedBy: {
            id: user.id,
            username: user.username,
          },
        },
        '成员权限更新成功',
        req
      );
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('用户不在项目中', req);
      }
      throw error;
    }
  }

  /**
   * 示例10：获取项目所有成员
   */
  @ProjectMember()
  @Get('projects/:projectId/members')
  @ApiOperation({ summary: '获取项目成员列表' })
  async getProjectMembers(
    @ProjectId() projectId: string,
    @HasProjectPermission('view_scores') canViewScores: boolean,
    @Request() req
  ) {
    try {
      const members = await this.permissionService.getProjectMembers(projectId);

      // 根据权限过滤敏感信息
      const filteredMembers = members.map(member => ({
        id: member.id,
        userId: member.userId,
        role: member.role,
        status: member.status,
        user: {
          id: member.user.id,
          username: member.user.username,
          nickname: member.user.nickname,
          avatar: member.user.avatarUrl,
        },
        // 只有有权限的用户才能看到详细权限
        permissions: canViewScores ? member.permissions : undefined,
        metadata: {
          invitedAt: member.metadata?.invitedAt,
          acceptedAt: member.metadata?.acceptedAt,
        },
      }));

      return this.success(
        {
          projectId,
          members: filteredMembers,
          totalMembers: members.length,
          canViewDetails: canViewScores,
        },
        '项目成员列表获取成功',
        req
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * 示例11：条件权限逻辑
   */
  @ProjectMember()
  @Get('projects/:projectId/conditional-data')
  @ApiOperation({ summary: '根据权限返回不同数据' })
  async getConditionalData(
    @ProjectId() projectId: string,
    @CurrentUser() user: User,
    @IsProjectOwner() isOwner: boolean,
    @IsProjectManager() isManager: boolean,
    @HasProjectPermission('score') canScore: boolean,
    @HasProjectPermission('view_scores') canViewScores: boolean,
    @HasProjectPermission('export') canExport: boolean,
    @Request() req
  ) {
    const responseData: any = {
      projectId,
      user: {
        id: user.id,
        username: user.username,
      },
      permissions: {
        isOwner,
        isManager,
        canScore,
        canViewScores,
        canExport,
      },
    };

    // 根据不同权限返回不同数据
    if (isOwner) {
      responseData.ownerData = {
        projectSettings: '项目配置信息',
        financialData: '财务数据',
        memberManagement: '成员管理',
      };
    }

    if (isManager) {
      responseData.managerData = {
        memberList: '成员列表',
        activityLog: '活动日志',
        contentModeration: '内容审核',
      };
    }

    if (canScore) {
      responseData.judgeData = {
        artworksList: '待评审作品',
        scoringHistory: '评分历史',
        judgingGuidelines: '评审指南',
      };
    }

    if (canViewScores) {
      responseData.scoresData = {
        allScores: '所有评分',
        statistics: '统计数据',
        rankings: '排名信息',
      };
    }

    if (canExport) {
      responseData.exportOptions = {
        formats: ['Excel', 'PDF', 'CSV'],
        dataTypes: ['作品', '评分', '成员'],
      };
    }

    // 基础数据（所有成员都能看到）
    responseData.basicData = {
      projectInfo: '项目基本信息',
      publicArtworks: '公开作品',
      announcements: '公告信息',
    };

    return this.success(responseData, '条件数据获取成功', req);
  }
}
