import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { UsersService } from '../../users/users.service';

/**
 * 认证守卫基类
 * 提供通用的Token验证逻辑
 */
@Injectable()
export abstract class BaseAuthGuard implements CanActivate {
  constructor(
    protected jwtService: JwtService,
    protected usersService: UsersService,
    protected reflector: Reflector,
  ) {}

  /**
   * 从请求头中提取Token
   */
  protected extractTokenFromHeader(request: Request): string | undefined {
    const authorization = request.headers.authorization;
    if (!authorization) {
      return undefined;
    }

    const [type, token] = authorization.split(' ');
    return type === 'Bearer' ? token : undefined;
  }

  /**
   * 验证Token并获取用户信息
   */
  protected async validateToken(token: string): Promise<any> {
    try {
      // 验证Token
      const payload = this.jwtService.verify(token);
      
      // 获取用户信息
      const user = await this.usersService.findOne(payload.sub);
      
      if (!user) {
        throw new UnauthorizedException('用户不存在');
      }

      if (user.status !== 1) {
        throw new UnauthorizedException('账户已被禁用');
      }

      return user;
    } catch (error) {
      if (error.name === 'JsonWebTokenError') {
        throw new UnauthorizedException('Token格式错误');
      }
      if (error.name === 'TokenExpiredError') {
        throw new UnauthorizedException('Token已过期');
      }
      throw error;
    }
  }

  abstract canActivate(context: ExecutionContext): boolean | Promise<boolean>;
}

/**
 * 1. 不需要认证守卫
 * 用于完全公开的接口，不进行任何认证检查
 */
@Injectable()
export class NoAuthGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    // 直接放行，不做任何检查
    return true;
  }
}

/**
 * 2. 必须认证守卫
 * 用于需要登录才能访问的接口
 */
@Injectable()
export class RequiredAuthGuard extends BaseAuthGuard {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException('请先登录');
    }

    try {
      const user = await this.validateToken(token);
      // 将用户信息附加到请求对象上
      (request as any).user = user;
      return true;
    } catch (error) {
      throw error;
    }
  }
}

/**
 * 3. 可选认证守卫
 * 用于可以登录也可以不登录访问的接口
 * 如果有Token且有效，则将用户信息附加到请求对象上
 * 如果没有Token或Token无效，则继续执行但不附加用户信息
 */
@Injectable()
export class OptionalAuthGuard extends BaseAuthGuard {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const token = this.extractTokenFromHeader(request);

    // 如果没有Token，直接放行
    if (!token) {
      (request as any).user = null;
      return true;
    }

    try {
      // 尝试验证Token
      const user = await this.validateToken(token);
      // 将用户信息附加到请求对象上
      (request as any).user = user;
      return true;
    } catch (error) {
      // Token无效时，不抛出异常，而是将user设为null继续执行
      (request as any).user = null;
      return true;
    }
  }
}

/**
 * JWT认证守卫（兼容原有代码）
 * 等同于RequiredAuthGuard
 */
@Injectable()
export class JwtAuthGuard extends RequiredAuthGuard {
  constructor(
    jwtService: JwtService,
    usersService: UsersService,
    reflector: Reflector,
  ) {
    super(jwtService, usersService, reflector);
  }
}

/**
 * 认证守卫工厂
 * 根据装饰器元数据自动选择合适的守卫
 */
@Injectable()
export class SmartAuthGuard extends BaseAuthGuard {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    // 检查是否标记为不需要认证
    const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    // 检查是否标记为可选认证
    const isOptional = this.reflector.getAllAndOverride<boolean>('isOptionalAuth', [
      context.getHandler(),
      context.getClass(),
    ]);

    const request = context.switchToHttp().getRequest<Request>();
    const token = this.extractTokenFromHeader(request);

    if (isOptional) {
      // 可选认证逻辑
      if (!token) {
        (request as any).user = null;
        return true;
      }

      try {
        const user = await this.validateToken(token);
        (request as any).user = user;
        return true;
      } catch (error) {
        (request as any).user = null;
        return true;
      }
    }

    // 默认为必须认证
    if (!token) {
      throw new UnauthorizedException('请先登录');
    }

    try {
      const user = await this.validateToken(token);
      (request as any).user = user;
      return true;
    } catch (error) {
      throw error;
    }
  }
}
