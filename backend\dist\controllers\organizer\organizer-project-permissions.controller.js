"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizerProjectPermissionsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const base_controller_1 = require("../../common/base/base.controller");
const auth_decorator_1 = require("../../auth/decorators/auth.decorator");
const project_auth_decorator_1 = require("../../auth/decorators/project-auth.decorator");
const user_decorator_1 = require("../../auth/decorators/user.decorator");
const user_entity_1 = require("../../users/entities/user.entity");
const project_permission_service_1 = require("../../auth/services/project-permission.service");
const role_management_service_1 = require("../../auth/services/role-management.service");
const role_management_dto_1 = require("../../users/dto/role-management.dto");
let OrganizerProjectPermissionsController = class OrganizerProjectPermissionsController extends base_controller_1.BaseController {
    projectPermissionService;
    roleManagementService;
    constructor(projectPermissionService, roleManagementService) {
        super();
        this.projectPermissionService = projectPermissionService;
        this.roleManagementService = roleManagementService;
    }
    async inviteUser(projectId, inviteDto, invitedBy, req) {
        try {
            const permissions = inviteDto.permissions ||
                await this.roleManagementService.getDefaultPermissions(inviteDto.role);
            const invitation = await this.projectPermissionService.inviteUserToProject(projectId, inviteDto.userId, inviteDto.role, permissions, invitedBy, {
                message: inviteDto.message,
                roleDescription: inviteDto.roleDescription,
                responsibilities: inviteDto.responsibilities,
            });
            return this.created({
                invitation,
                projectId,
                invitedRole: inviteDto.role,
                permissions,
            }, '用户邀请发送成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('用户不存在', req);
            }
            if (error.message.includes('已在项目中') || error.message.includes('已有待处理')) {
                return this.conflict(error.message, req);
            }
            if (error.message.includes('没有邀请用户的权限')) {
                return this.forbidden('您没有邀请用户的权限', req);
            }
            throw error;
        }
    }
    async batchInviteUsers(projectId, batchDto, invitedBy, req) {
        try {
            const results = await this.projectPermissionService.batchInviteUsers(projectId, batchDto.invitations, invitedBy);
            return this.created({
                successful: results.length,
                total: batchDto.invitations.length,
                results,
                projectId,
            }, '批量邀请完成', req);
        }
        catch (error) {
            throw error;
        }
    }
    async getProjectMembers(projectId, query, permissions, req) {
        try {
            const result = await this.projectPermissionService.getProjectMembers(projectId, query);
            const canViewDetails = permissions.canViewScores || permissions.canManageProject;
            const filteredMembers = result.data.map(member => ({
                id: member.id,
                userId: member.userId,
                role: member.role,
                status: member.status,
                user: {
                    id: member.user.id,
                    username: member.user.username,
                    nickname: member.user.nickname,
                    realName: member.user.realName,
                    avatar: member.user.avatar,
                },
                permissions: canViewDetails ? member.permissions : undefined,
                metadata: {
                    invitedAt: member.metadata?.invitedAt,
                    acceptedAt: member.metadata?.acceptedAt,
                    roleDescription: member.metadata?.roleDescription,
                },
                createdAt: member.createdAt,
            }));
            return this.paginated(filteredMembers, result.total, result.page, result.limit, '项目成员列表获取成功', req);
        }
        catch (error) {
            throw error;
        }
    }
    async updateMemberPermissions(projectId, targetUserId, updateDto, updatedBy, req) {
        try {
            await this.projectPermissionService.updateProjectRolePermissions(targetUserId, projectId, updateDto.permissions, updatedBy);
            return this.success({
                projectId,
                targetUserId,
                newPermissions: updateDto.permissions,
                updatedBy,
            }, '成员权限更新成功', req);
        }
        catch (error) {
            if (error.message.includes('不在项目中')) {
                return this.notFound('用户不在项目中', req);
            }
            if (error.message.includes('没有管理权限')) {
                return this.forbidden('您没有管理权限', req);
            }
            throw error;
        }
    }
    async removeProjectMember(projectId, targetUserId, removeDto, removedBy, req) {
        try {
            await this.projectPermissionService.removeProjectMember(targetUserId, projectId, removedBy, removeDto.reason);
            return this.success({
                projectId,
                removedUserId: targetUserId,
                removedBy,
                reason: removeDto.reason,
            }, '项目成员移除成功', req);
        }
        catch (error) {
            if (error.message.includes('不能移除项目所有者')) {
                return this.forbidden('不能移除项目所有者', req);
            }
            if (error.message.includes('没有管理权限')) {
                return this.forbidden('您没有管理权限', req);
            }
            throw error;
        }
    }
    async getMemberDetails(projectId, userId, req) {
        try {
            const roles = await this.projectPermissionService.getUserProjectRoles(userId, projectId);
            if (roles.length === 0) {
                return this.notFound('用户不在项目中', req);
            }
            const mergedPermissions = this.projectPermissionService.mergePermissions(roles);
            return this.success({
                userId,
                projectId,
                roles: roles.map(role => ({
                    id: role.id,
                    role: role.role,
                    permissions: role.permissions,
                    status: role.status,
                    metadata: role.metadata,
                    createdAt: role.createdAt,
                })),
                mergedPermissions,
                totalRoles: roles.length,
            }, '成员详细信息获取成功', req);
        }
        catch (error) {
            throw error;
        }
    }
    async getProjectPermissionStats(projectId, req) {
        try {
            const stats = await this.projectPermissionService.getProjectPermissionStats(projectId);
            return this.success(stats, '项目权限统计获取成功', req);
        }
        catch (error) {
            throw error;
        }
    }
    async getPermissionTemplates(role, req) {
        try {
            const templates = await this.roleManagementService.getPermissionTemplates(role);
            return this.success(templates, '权限模板获取成功', req);
        }
        catch (error) {
            throw error;
        }
    }
    async getMyPermissions(projectId, user, roles, permissions, isManager, req) {
        try {
            return this.success({
                projectId,
                user: {
                    id: user.id,
                    username: user.username,
                    globalRoles: user.roles,
                },
                projectRoles: roles,
                permissions,
                isManager,
                capabilities: {
                    canInviteUsers: permissions.canInviteUsers,
                    canManageMembers: permissions.canManageProject,
                    canViewAllPermissions: permissions.canViewScores || permissions.canManageProject,
                    canExportData: permissions.canExportData,
                },
            }, '我的项目权限信息获取成功', req);
        }
        catch (error) {
            throw error;
        }
    }
};
exports.OrganizerProjectPermissionsController = OrganizerProjectPermissionsController;
__decorate([
    (0, project_auth_decorator_1.ProjectPermission)('invite'),
    (0, common_1.Post)('invite'),
    (0, swagger_1.ApiOperation)({ summary: '邀请用户加入项目' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '邀请发送成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '用户已在项目中' }),
    __param(0, (0, project_auth_decorator_1.ProjectId)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, user_decorator_1.UserId)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, role_management_dto_1.InviteUserToProjectDto, String, Object]),
    __metadata("design:returntype", Promise)
], OrganizerProjectPermissionsController.prototype, "inviteUser", null);
__decorate([
    (0, project_auth_decorator_1.ProjectPermission)('invite'),
    (0, common_1.Post)('batch-invite'),
    (0, swagger_1.ApiOperation)({ summary: '批量邀请用户加入项目' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '批量邀请完成' }),
    __param(0, (0, project_auth_decorator_1.ProjectId)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, user_decorator_1.UserId)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, role_management_dto_1.BatchInviteUsersDto, String, Object]),
    __metadata("design:returntype", Promise)
], OrganizerProjectPermissionsController.prototype, "batchInviteUsers", null);
__decorate([
    (0, project_auth_decorator_1.ProjectManager)(),
    (0, common_1.Get)('members'),
    (0, swagger_1.ApiOperation)({ summary: '获取项目成员列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, project_auth_decorator_1.ProjectId)()),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, project_auth_decorator_1.CurrentProjectPermissions)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, role_management_dto_1.QueryProjectMembersDto, Object, Object]),
    __metadata("design:returntype", Promise)
], OrganizerProjectPermissionsController.prototype, "getProjectMembers", null);
__decorate([
    (0, project_auth_decorator_1.ProjectManager)(),
    (0, common_1.Patch)('members/:userId/permissions'),
    (0, swagger_1.ApiOperation)({ summary: '更新成员权限' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '权限更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不在项目中' }),
    __param(0, (0, project_auth_decorator_1.ProjectId)()),
    __param(1, (0, common_1.Param)('userId')),
    __param(2, (0, common_1.Body)()),
    __param(3, (0, user_decorator_1.UserId)()),
    __param(4, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, role_management_dto_1.UpdateProjectPermissionsDto, String, Object]),
    __metadata("design:returntype", Promise)
], OrganizerProjectPermissionsController.prototype, "updateMemberPermissions", null);
__decorate([
    (0, project_auth_decorator_1.ProjectManager)(),
    (0, common_1.Delete)('members/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '移除项目成员' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成员移除成功' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: '不能移除项目所有者' }),
    __param(0, (0, project_auth_decorator_1.ProjectId)()),
    __param(1, (0, common_1.Param)('userId')),
    __param(2, (0, common_1.Body)()),
    __param(3, (0, user_decorator_1.UserId)()),
    __param(4, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, role_management_dto_1.RemoveProjectMemberDto, String, Object]),
    __metadata("design:returntype", Promise)
], OrganizerProjectPermissionsController.prototype, "removeProjectMember", null);
__decorate([
    (0, project_auth_decorator_1.ProjectManager)(),
    (0, common_1.Get)('members/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '获取成员详细信息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, project_auth_decorator_1.ProjectId)()),
    __param(1, (0, common_1.Param)('userId')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], OrganizerProjectPermissionsController.prototype, "getMemberDetails", null);
__decorate([
    (0, project_auth_decorator_1.ProjectManager)(),
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({ summary: '获取项目权限统计' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, project_auth_decorator_1.ProjectId)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], OrganizerProjectPermissionsController.prototype, "getProjectPermissionStats", null);
__decorate([
    (0, project_auth_decorator_1.ProjectManager)(),
    (0, common_1.Get)('templates'),
    (0, swagger_1.ApiOperation)({ summary: '获取可用的权限模板' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)('role')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], OrganizerProjectPermissionsController.prototype, "getPermissionTemplates", null);
__decorate([
    (0, project_auth_decorator_1.ProjectManager)(),
    (0, common_1.Get)('my-permissions'),
    (0, swagger_1.ApiOperation)({ summary: '获取我的项目权限信息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, project_auth_decorator_1.ProjectId)()),
    __param(1, (0, user_decorator_1.CurrentUser)()),
    __param(2, (0, project_auth_decorator_1.CurrentProjectRole)()),
    __param(3, (0, project_auth_decorator_1.CurrentProjectPermissions)()),
    __param(4, (0, project_auth_decorator_1.IsProjectManager)()),
    __param(5, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, user_entity_1.User, Array, Object, Boolean, Object]),
    __metadata("design:returntype", Promise)
], OrganizerProjectPermissionsController.prototype, "getMyPermissions", null);
exports.OrganizerProjectPermissionsController = OrganizerProjectPermissionsController = __decorate([
    (0, swagger_1.ApiTags)('办展方-项目权限管理'),
    (0, common_1.Controller)('organizer/projects/:projectId/permissions'),
    (0, auth_decorator_1.OrganizerAuth)(),
    __metadata("design:paramtypes", [project_permission_service_1.ProjectPermissionService,
        role_management_service_1.RoleManagementService])
], OrganizerProjectPermissionsController);
//# sourceMappingURL=organizer-project-permissions.controller.js.map