-- 多角色权限系统数据库迁移脚本
-- 创建用户角色和项目权限相关表

-- 1. 更新用户表，添加新字段
ALTER TABLE users 
ADD COLUMN real_name VARCHAR(100),
ADD COLUMN phone VARCHAR(20),
ADD COLUMN bio TEXT,
ADD COLUMN profile_data JSON,
ADD COLUMN specialties JSON,
ADD COLUMN certification_level INT DEFAULT 0,
ADD COLUMN organization_id VARCHAR(36),
ADD COLUMN last_login_at TIMESTAMP NULL,
ADD COLUMN login_count INT DEFAULT 0,
ADD COLUMN settings JSON;

-- 更新用户状态字段
ALTER TABLE users 
MODIFY COLUMN status ENUM('0', '1', '2', '3') DEFAULT '1' COMMENT '0:禁用, 1:正常, 2:待审核, 3:已拒绝';

-- 2. 创建用户全局角色表
CREATE TABLE user_global_roles (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    role ENUM('super_admin', 'admin', 'organizer', 'artist', 'judge', 'user') NOT NULL,
    metadata JSON COMMENT '角色元数据：授权人、授权时间、过期时间等',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_role (user_id, role),
    INDEX idx_user_id (user_id),
    INDEX idx_role (role),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户全局角色表';

-- 3. 创建用户项目角色表
CREATE TABLE user_project_roles (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    project_id VARCHAR(36) NOT NULL,
    role ENUM('project_owner', 'project_admin', 'project_judge', 'project_artist', 'project_viewer', 'project_moderator', 'project_assistant') NOT NULL,
    permissions JSON COMMENT '项目内权限配置',
    metadata JSON COMMENT '角色元数据：邀请人、邀请时间、职责描述等',
    status ENUM('pending', 'accepted', 'declined', 'removed') DEFAULT 'pending',
    expires_at TIMESTAMP NULL COMMENT '权限过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_project_role (user_id, project_id, role),
    INDEX idx_user_id (user_id),
    INDEX idx_project_id (project_id),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户项目角色表';

-- 4. 创建权限模板表
CREATE TABLE permission_templates (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '模板名称',
    role ENUM('project_owner', 'project_admin', 'project_judge', 'project_artist', 'project_viewer', 'project_moderator', 'project_assistant') NOT NULL,
    permissions JSON NOT NULL COMMENT '权限配置',
    description TEXT COMMENT '模板描述',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否为默认模板',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_role (role),
    INDEX idx_default (is_default),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限模板表';

-- 5. 创建角色申请表
CREATE TABLE role_applications (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    target_role ENUM('super_admin', 'admin', 'organizer', 'artist', 'judge', 'user') NOT NULL,
    application_data JSON NOT NULL COMMENT '申请材料和信息',
    status ENUM('pending', 'reviewing', 'approved', 'rejected') DEFAULT 'pending',
    review_comment TEXT COMMENT '审核意见',
    reviewer_id VARCHAR(36) NULL COMMENT '审核人ID',
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewer_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_target_role (target_role),
    INDEX idx_status (status),
    INDEX idx_reviewer_id (reviewer_id),
    INDEX idx_applied_at (applied_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色申请表';

-- 6. 创建项目邀请记录表
CREATE TABLE project_invitations (
    id VARCHAR(36) PRIMARY KEY,
    project_id VARCHAR(36) NOT NULL,
    inviter_id VARCHAR(36) NOT NULL COMMENT '邀请人ID',
    invitee_id VARCHAR(36) NOT NULL COMMENT '被邀请人ID',
    role ENUM('project_owner', 'project_admin', 'project_judge', 'project_artist', 'project_viewer', 'project_moderator', 'project_assistant') NOT NULL,
    permissions JSON COMMENT '分配的权限',
    message TEXT COMMENT '邀请消息',
    status ENUM('pending', 'accepted', 'declined', 'expired') DEFAULT 'pending',
    expires_at TIMESTAMP NOT NULL COMMENT '邀请过期时间',
    responded_at TIMESTAMP NULL COMMENT '响应时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (inviter_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (invitee_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_project_id (project_id),
    INDEX idx_inviter_id (inviter_id),
    INDEX idx_invitee_id (invitee_id),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目邀请记录表';

-- 7. 插入默认权限模板
INSERT INTO permission_templates (id, name, role, permissions, description, is_default) VALUES
(UUID(), '项目所有者默认权限', 'project_owner', JSON_OBJECT(
    'canManageProject', true,
    'canInviteUsers', true,
    'canManageArtworks', true,
    'canScore', false,
    'canViewScores', true,
    'canExportData', true,
    'canModerateContent', true,
    'canManageSchedule', true
), '项目所有者拥有项目内所有管理权限', true),

(UUID(), '项目管理员默认权限', 'project_admin', JSON_OBJECT(
    'canManageProject', true,
    'canInviteUsers', true,
    'canManageArtworks', true,
    'canScore', false,
    'canViewScores', true,
    'canExportData', true,
    'canModerateContent', true,
    'canManageSchedule', true
), '项目管理员拥有大部分管理权限', true),

(UUID(), '项目评委默认权限', 'project_judge', JSON_OBJECT(
    'canManageProject', false,
    'canInviteUsers', false,
    'canManageArtworks', false,
    'canScore', true,
    'canViewScores', true,
    'canExportData', false,
    'canModerateContent', false,
    'canManageSchedule', false
), '项目评委主要负责评分和查看评分', true),

(UUID(), '项目参与者默认权限', 'project_artist', JSON_OBJECT(
    'canManageProject', false,
    'canInviteUsers', false,
    'canManageArtworks', false,
    'canScore', false,
    'canViewScores', false,
    'canExportData', false,
    'canModerateContent', false,
    'canManageSchedule', false
), '项目参与者只能提交作品', true),

(UUID(), '项目观察者默认权限', 'project_viewer', JSON_OBJECT(
    'canManageProject', false,
    'canInviteUsers', false,
    'canManageArtworks', false,
    'canScore', false,
    'canViewScores', false,
    'canExportData', false,
    'canModerateContent', false,
    'canManageSchedule', false
), '项目观察者只能查看基本信息', true),

(UUID(), '项目协调员默认权限', 'project_moderator', JSON_OBJECT(
    'canManageProject', false,
    'canInviteUsers', false,
    'canManageArtworks', true,
    'canScore', false,
    'canViewScores', true,
    'canExportData', false,
    'canModerateContent', true,
    'canManageSchedule', false
), '项目协调员负责内容管理和审核', true);

-- 8. 为现有用户创建默认全局角色
INSERT INTO user_global_roles (id, user_id, role, metadata, is_active)
SELECT 
    UUID() as id,
    id as user_id,
    role as role,
    JSON_OBJECT(
        'grantedBy', 'system',
        'grantedAt', NOW(),
        'reason', '系统迁移时自动创建'
    ) as metadata,
    true as is_active
FROM users 
WHERE role IS NOT NULL;

-- 9. 创建索引优化查询性能
CREATE INDEX idx_users_organization ON users(organization_id);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_last_login ON users(last_login_at);

-- 10. 创建视图简化查询
CREATE VIEW user_roles_view AS
SELECT 
    u.id as user_id,
    u.username,
    u.email,
    u.real_name,
    u.status as user_status,
    GROUP_CONCAT(ugr.role) as global_roles,
    COUNT(DISTINCT upr.project_id) as project_count
FROM users u
LEFT JOIN user_global_roles ugr ON u.id = ugr.user_id AND ugr.is_active = true
LEFT JOIN user_project_roles upr ON u.id = upr.user_id AND upr.status = 'accepted'
GROUP BY u.id, u.username, u.email, u.real_name, u.status;
