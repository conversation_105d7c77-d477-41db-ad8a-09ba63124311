import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum OrganizationType {
  GOVERNMENT = 'government',
  ASSOCIATION = 'association',
  COMPANY = 'company',
  SCHOOL = 'school',
  INDIVIDUAL = 'individual',
}

export enum ApplicationStatus {
  PENDING = 'pending',
  REVIEWING = 'reviewing',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

@Entity('organizer_applications')
export class OrganizerApplication {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({ name: 'organization_name', length: 200 })
  organizationName: string;

  @Column({
    name: 'organization_type',
    type: 'enum',
    enum: OrganizationType,
  })
  organizationType: OrganizationType;

  @Column({ name: 'legal_person', length: 50, nullable: true })
  legalPerson: string;

  @Column({ name: 'contact_person', length: 50 })
  contactPerson: string;

  @Column({ name: 'contact_phone', length: 20 })
  contactPhone: string;

  @Column({ name: 'contact_email', length: 100, nullable: true })
  contactEmail: string;

  @Column({ type: 'text', nullable: true })
  address: string;

  @Column({ name: 'business_license', nullable: true })
  businessLicense: string;

  @Column({ name: 'qualification_docs', type: 'json', nullable: true })
  qualificationDocs: string[];

  @Column({ type: 'text', nullable: true })
  introduction: string;

  @Column({ type: 'text', nullable: true })
  experience: string;

  @Column({ name: 'application_reason', type: 'text', nullable: true })
  applicationReason: string;

  @Column({
    type: 'enum',
    enum: ApplicationStatus,
    default: ApplicationStatus.PENDING,
  })
  status: ApplicationStatus;

  @Column({ name: 'reviewer_id', nullable: true })
  reviewerId: string;

  @Column({ name: 'review_comment', type: 'text', nullable: true })
  reviewComment: string;

  @Column({ name: 'applied_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  appliedAt: Date;

  @Column({ name: 'reviewed_at', type: 'timestamp', nullable: true })
  reviewedAt: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'reviewer_id' })
  reviewer: User;
}
