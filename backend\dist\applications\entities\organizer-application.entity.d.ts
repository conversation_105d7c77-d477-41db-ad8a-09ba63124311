import { User } from '../../users/entities/user.entity';
export declare enum OrganizationType {
    GOVERNMENT = "government",
    ASSOCIATION = "association",
    COMPANY = "company",
    SCHOOL = "school",
    INDIVIDUAL = "individual"
}
export declare enum ApplicationStatus {
    PENDING = "pending",
    REVIEWING = "reviewing",
    APPROVED = "approved",
    REJECTED = "rejected"
}
export declare class OrganizerApplication {
    id: string;
    userId: string;
    organizationName: string;
    organizationType: OrganizationType;
    legalPerson: string;
    contactPerson: string;
    contactPhone: string;
    contactEmail: string;
    address: string;
    businessLicense: string;
    qualificationDocs: string[];
    introduction: string;
    experience: string;
    applicationReason: string;
    status: ApplicationStatus;
    reviewerId: string;
    reviewComment: string;
    appliedAt: Date;
    reviewedAt: Date;
    createdAt: Date;
    updatedAt: Date;
    user: User;
    reviewer: User;
}
