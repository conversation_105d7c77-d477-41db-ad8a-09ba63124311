# 书画作品评选打分管理系统流程图

## 一、系统功能模块图

```mermaid
graph TD
    A[书画作品评选打分管理系统] --> B[项目管理]
    A --> C[作品管理]
    A --> D[评分系统]
    A --> E[统计分析]
    A --> F[创新功能]

    %% 项目管理模块
    B --> B1[项目创建]
    B --> B2[评选规则]
    B --> B3[评委管理]
    B --> B4[状态流转]
    
    B4 --> B41[筹备中]
    B41 --> B42[正在筹稿]
    B42 --> B43[作品统计]
    B43 --> B44[正在评选]
    B44 --> B45[评选查看]
    B45 --> B46[电子展厅]
    B46 --> B47[已结束]

    %% 作品管理模块
    C --> C1[作品上传]
    C --> C2[作品展示]
    
    C1 --> C11[Excel导入]
    C1 --> C12[批量上传]
    C1 --> C13[信息管理]
    
    C2 --> C21[多视图模式]
    C2 --> C22[详情展示]

    %% 评分系统模块
    D --> D1[评分功能]
    D --> D2[评语系统]
    
    D1 --> D11[单项打分]
    D1 --> D12[综合评分]
    D1 --> D13[一键不合格]
    
    D2 --> D21[文本评语]
    D2 --> D22[语音评语]
    D2 --> D23[评语模板]

    %% 统计分析模块
    E --> E1[成绩统计]
    E --> E2[数据分析]
    
    E1 --> E11[作品维度]
    E1 --> E12[评委维度]
    
    E2 --> E21[可视化图表]
    E2 --> E22[报表导出]

    %% 创新功能模块
    F --> F1[AI辅助]
    F --> F2[社交互动]
    F --> F3[直播功能]
    F --> F4[增强展示]
    
    F1 --> F11[智能分析]
    F1 --> F12[评语生成]
    
    F2 --> F21[点赞收藏]
    F2 --> F22[评论互动]
    
    F3 --> F31[评选直播]
    F3 --> F32[专家点评]
    
    F4 --> F41[VR展厅]
    F4 --> F42[3D展示]
```

## 二、用户角色权限图

```mermaid
graph TD
    A[系统角色] --> B[超级管理员]
    A --> C[项目发起人]
    A --> D[美工组成员]
    A --> E[评委组成员]
    A --> F[普通用户]

    %% 超级管理员权限
    B --> B1[系统配置管理]
    B --> B2[用户权限管理]
    B --> B3[数据备份恢复]
    B --> B4[系统监控]

    %% 项目发起人权限
    C --> C1[项目创建管理]
    C --> C2[评委邀请管理]
    C --> C3[作品审核管理]
    C --> C4[结果发布管理]

    %% 美工组权限
    D --> D1[作品上传管理]
    D --> D2[作品信息编辑]
    D --> D3[展示效果调整]
    D --> D4[作品分类整理]

    %% 评委组权限
    E --> E1[作品评分]
    E --> E2[评语录入]
    E --> E3[评分修改]
    E --> E4[进度查看]

    %% 普通用户权限
    F --> F1[项目浏览]
    F --> F2[作品查看]
    F --> F3[评论互动]
    F --> F4[分享传播]
```

## 三、数据流转流程图

```mermaid
sequenceDiagram
    participant A as 项目发起人
    participant B as 美工组
    participant C as 评委组
    participant D as 系统
    participant E as 普通用户

    A->>D: 创建项目
    A->>D: 设置评选规则
    A->>B: 邀请美工组
    A->>C: 邀请评委组
    
    B->>D: 上传作品信息
    B->>D: 上传作品图片
    D->>D: 自动匹配信息
    
    D->>C: 通知开始评分
    C->>D: 进行评分
    C->>D: 提交评语
    
    D->>D: 统计分析
    D->>A: 生成统计报告
    
    A->>D: 确认发布结果
    D->>E: 展示评选结果
    
    E->>D: 查看/互动
    D->>A: 反馈数据分析
```

## 四、关键流程说明

### 4.1 项目创建流程
1. 项目发起人登录系统
2. 填写项目基本信息
3. 设置评选规则和标准
4. 邀请美工组和评委组
5. 生成项目二维码和海报
6. 发布项目到首页

### 4.2 作品上传流程
1. 美工组登录系统
2. 下载Excel模板
3. 填写作品信息
4. 批量上传作品图片
5. 系统自动匹配信息
6. 预览和确认作品信息

### 4.3 评分流程
1. 评委登录系统
2. 查看待评分作品
3. 进行客观标准筛选
4. 进行主观标准评分
5. 录入评语
6. 提交评分结果

### 4.4 结果发布流程
1. 系统自动统计分数
2. 生成统计报告
3. 项目发起人审核
4. 确认发布结果
5. 生成获奖证书
6. 更新展示界面

## 五、系统状态转换说明

### 5.1 项目状态
- 筹备中 → 正在筹稿：完成项目创建和规则设置
- 正在筹稿 → 作品统计：截止收稿，开始统计
- 作品统计 → 正在评选：完成作品整理，开始评分
- 正在评选 → 评选查看：评分完成，公示结果
- 评选查看 → 电子展厅：结果确认，展示作品
- 电子展厅 → 已结束：展览结束，项目归档

### 5.2 作品状态
- 待上传 → 已上传：完成作品信息和图片上传
- 已上传 → 待审核：等待资格审核
- 待审核 → 待评分：通过资格审核
- 待评分 → 已评分：完成评分
- 已评分 → 已公示：结果公示中
- 已公示 → 已归档：项目结束

### 5.3 评分状态
- 未开始 → 进行中：开始评分
- 进行中 → 已完成：提交评分
- 已完成 → 已确认：评分锁定
- 已确认 → 已公示：结果公示 