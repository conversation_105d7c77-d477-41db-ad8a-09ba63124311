# uni-app 开发环境搭建指南

## 一、环境准备

### 1.1 安装开发工具
```bash
# 安装 HBuilderX (推荐) 或使用 VS Code
# HBuilderX 下载地址: https://www.dcloud.io/hbuilderx.html

# 如果使用 VS Code，需要安装 uni-app 插件
# 安装 Vue CLI
npm install -g @vue/cli

# 创建 uni-app 项目
vue create -p dcloudio/uni-preset-vue my-project
```

### 1.2 选择模板
- **默认模板**: 基础项目结构
- **Hello uni-app**: 包含示例页面
- **登录模板**: 包含登录功能
- **新闻模板**: 包含列表和详情页

## 二、项目初始化

### 2.1 创建项目
```bash
# 使用 HBuilderX 创建
# 文件 -> 新建 -> 项目 -> uni-app

# 或使用命令行
npx @dcloudio/uvm@latest create calligraphy-contest-app
```

### 2.2 安装依赖
```bash
cd calligraphy-contest-app
npm install

# 安装常用插件
npm install @dcloudio/uni-ui
npm install uview-ui
npm install dayjs
```

## 三、项目配置

### 3.1 manifest.json 配置
```json
{
  "name": "书画作品评选系统",
  "appid": "__UNI__XXXXXXX",
  "description": "专业的书画作品评选打分管理平台",
  "versionName": "1.0.0",
  "versionCode": "100",
  "transformPx": false,
  "app-plus": {
    "usingComponents": true,
    "nvueStyleCompiler": "uni-app",
    "compilerVersion": 3,
    "splashscreen": {
      "alwaysShowBeforeRender": true,
      "waiting": true,
      "autoclose": true,
      "delay": 0
    }
  },
  "quickapp": {},
  "mp-weixin": {
    "appid": "你的微信小程序AppID",
    "setting": {
      "urlCheck": false,
      "es6": true,
      "enhance": true,
      "postcss": true,
      "preloadBackgroundData": false,
      "minified": true,
      "newFeature": false,
      "coverView": true,
      "nodeModules": false,
      "autoAudits": false,
      "showShadowRootInWxmlPanel": true,
      "scopeDataCheck": false,
      "uglifyFileName": false,
      "checkInvalidKey": true,
      "checkSiteMap": true,
      "uploadWithSourceMap": true,
      "compileHotReLoad": false,
      "lazyloadPlaceholderEnable": false,
      "useMultiFrameRuntime": true,
      "useApiHook": true,
      "useApiHostProcess": true
    },
    "usingComponents": true,
    "permission": {
      "scope.userLocation": {
        "desc": "你的位置信息将用于小程序位置接口的效果展示"
      }
    }
  },
  "h5": {
    "title": "书画作品评选系统",
    "template": "index.html"
  }
}
```

### 3.2 pages.json 配置
```json
{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "首页"
      }
    },
    {
      "path": "pages/project/list",
      "style": {
        "navigationBarTitleText": "项目列表"
      }
    },
    {
      "path": "pages/project/detail",
      "style": {
        "navigationBarTitleText": "项目详情"
      }
    },
    {
      "path": "pages/artwork/list",
      "style": {
        "navigationBarTitleText": "作品展示"
      }
    },
    {
      "path": "pages/artwork/detail",
      "style": {
        "navigationBarTitleText": "作品详情"
      }
    },
    {
      "path": "pages/score/index",
      "style": {
        "navigationBarTitleText": "评分"
      }
    },
    {
      "path": "pages/profile/index",
      "style": {
        "navigationBarTitleText": "个人中心"
      }
    },
    {
      "path": "pages/login/index",
      "style": {
        "navigationBarTitleText": "登录"
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "书画评选",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
  },
  "tabBar": {
    "color": "#7A7E83",
    "selectedColor": "#3cc51f",
    "borderStyle": "black",
    "backgroundColor": "#ffffff",
    "list": [
      {
        "pagePath": "pages/index/index",
        "iconPath": "static/tab-home.png",
        "selectedIconPath": "static/tab-home-current.png",
        "text": "首页"
      },
      {
        "pagePath": "pages/project/list",
        "iconPath": "static/tab-project.png",
        "selectedIconPath": "static/tab-project-current.png",
        "text": "项目"
      },
      {
        "pagePath": "pages/profile/index",
        "iconPath": "static/tab-profile.png",
        "selectedIconPath": "static/tab-profile-current.png",
        "text": "我的"
      }
    ]
  }
}
```

## 四、核心功能开发

### 4.1 API 封装
```javascript
// utils/request.js
const BASE_URL = 'https://your-api-domain.com/api'

const request = (options) => {
  return new Promise((resolve, reject) => {
    uni.request({
      url: BASE_URL + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        'Authorization': uni.getStorageSync('token') || ''
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data)
        } else {
          reject(res)
        }
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

export default request
```

### 4.2 状态管理 (Vuex)
```javascript
// store/index.js
import { createStore } from 'vuex'

const store = createStore({
  state: {
    user: null,
    token: '',
    currentProject: null
  },
  mutations: {
    SET_USER(state, user) {
      state.user = user
    },
    SET_TOKEN(state, token) {
      state.token = token
      uni.setStorageSync('token', token)
    },
    SET_CURRENT_PROJECT(state, project) {
      state.currentProject = project
    }
  },
  actions: {
    login({ commit }, userInfo) {
      commit('SET_USER', userInfo.user)
      commit('SET_TOKEN', userInfo.token)
    },
    logout({ commit }) {
      commit('SET_USER', null)
      commit('SET_TOKEN', '')
      uni.removeStorageSync('token')
    }
  }
})

export default store
```

### 4.3 微信登录功能
```javascript
// utils/auth.js
export const wxLogin = () => {
  return new Promise((resolve, reject) => {
    uni.login({
      provider: 'weixin',
      success: (loginRes) => {
        // 获取用户信息
        uni.getUserInfo({
          success: (infoRes) => {
            // 发送到后端验证
            uni.request({
              url: 'https://your-api.com/auth/wx-login',
              method: 'POST',
              data: {
                code: loginRes.code,
                userInfo: infoRes.userInfo
              },
              success: (res) => {
                resolve(res.data)
              },
              fail: reject
            })
          },
          fail: reject
        })
      },
      fail: reject
    })
  })
}
```

## 五、开发建议

### 5.1 目录结构建议
```
uni-app/
├── api/                    # API接口
│   ├── auth.js            # 认证相关
│   ├── project.js         # 项目相关
│   └── artwork.js         # 作品相关
├── components/            # 公共组件
│   ├── artwork-card/      # 作品卡片
│   ├── score-panel/       # 评分面板
│   └── project-status/    # 项目状态
├── pages/                 # 页面
├── static/               # 静态资源
├── store/                # 状态管理
├── utils/                # 工具函数
│   ├── request.js        # 请求封装
│   ├── auth.js           # 认证工具
│   └── common.js         # 通用工具
├── App.vue               # 应用入口
├── main.js               # 入口文件
├── manifest.json         # 应用配置
├── pages.json            # 页面配置
└── uni.scss              # 全局样式
```

### 5.2 开发规范
1. **组件命名**: 使用 PascalCase
2. **页面命名**: 使用 kebab-case
3. **API接口**: 统一使用 request 封装
4. **样式**: 使用 rpx 单位，兼容多端
5. **图片**: 使用相对路径，放在 static 目录

### 5.3 调试技巧
- **微信开发者工具**: 用于小程序调试
- **浏览器**: 用于 H5 调试
- **真机调试**: 使用 HBuilderX 真机运行
- **条件编译**: 使用 #ifdef 处理平台差异

## 六、部署发布

### 6.1 微信小程序发布
1. 在 HBuilderX 中选择"发行" -> "小程序-微信"
2. 填写小程序名称和AppID
3. 点击发行，生成小程序代码包
4. 使用微信开发者工具上传代码
5. 在微信公众平台提交审核

### 6.2 H5发布
1. 选择"发行" -> "网站-H5手机版"
2. 配置发布路径和域名
3. 上传到服务器

这样你就可以用uni-app一套代码同时支持微信小程序、H5和App了！