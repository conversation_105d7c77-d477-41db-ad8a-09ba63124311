# SpringBoot服务器配置推荐表格

## 不同日活用户规模的服务器配置对比

| 配置项 | 0.5万日活 | 2万日活 | 5万日活 | 20万日活 | 50万日活 | 100万日活 |
|--------|-----------|---------|---------|----------|----------|-----------|
| **服务器规格** |  |  |  |  |  |  |
| CPU核心数 | 2核 | 4核 | 4-8核 | 8-16核 | 16-32核 | 32核+ |
| 内存容量 | 4GB | 8GB | 8-16GB | 16-32GB | 32-64GB | 64GB+ |
| 网络带宽 | 5Mbps | 10Mbps | 20Mbps | 50Mbps | 100Mbps | 1Gbps |
| **Tomcat配置** |  |  |  |  |  |  |
| max threads | 50 | 100 | 200 | 500 | 1000 | 切换至WebFlux |
| min-spare threads | 10 | 20 | 20 | 50 | 100 | - |
| max-connections | 2048 | 4096 | 8192 | 10000 | 20000 | 50000 |
| accept-count | 50 | 100 | 100 | 200 | 500 | 1000 |
| connection-timeout | 20000ms | 20000ms | 20000ms | 20000ms | 15000ms | 10000ms |
| **数据库连接池** |  |  |  |  |  |  |
| maximum-pool-size | 20 | 30 | 50 | 100 | 200 | 300 |
| minimum-idle | 5 | 10 | 10 | 20 | 50 | 100 |
| connection-timeout | 2000ms | 2000ms | 2000ms | 2000ms | 2000ms | 2000ms |
| idle-timeout | 30000ms | 30000ms | 30000ms | 30000ms | 30000ms | 30000ms |
| **JVM参数** |  |  |  |  |  |  |
| 堆内存(-Xms/-Xmx) | 1g/2g | 2g/4g | 2g/4g | 4g/8g | 8g/16g | 16g/32g |
| 垃圾收集器 | G1GC | G1GC | G1GC | G1GC | G1GC | G1GC |
| GC暂停时间 | 200ms | 150ms | 100ms | 100ms | 100ms | 50ms |
| 并行GC线程 | 2 | 4 | 4-8 | 8-16 | 16-32 | 32+ |
| **缓存策略** |  |  |  |  |  |  |
| 本地缓存 | Caffeine | Caffeine | Caffeine | Caffeine | Redis | Redis集群 |
| 缓存TTL | 30min | 20min | 15min | 10min | 5min | 5min |
| 缓存大小 | 1000条 | 5000条 | 10000条 | 50000条 | 无限制 | 无限制 |
| **架构模式** |  |  |  |  |  |  |
| 应用架构 | 单体应用 | 单体应用 | 单体应用 | 单体+负载均衡 | 微服务 | 微服务集群 |
| 数据库架构 | 单库 | 单库 | 主从复制 | 读写分离 | 分库分表 | 分布式数据库 |
| 部署方式 | 单机部署 | 单机部署 | 双机热备 | 集群部署 | 容器化部署 | K8s自动扩缩 |
| **监控告警** |  |  |  |  |  |  |
| 监控工具 | Spring Actuator | Actuator+Micrometer | Prometheus+Grafana | 完整APM | 分布式链路追踪 | 全链路监控 |
| 告警阈值 |  |  |  |  |  |  |
| - CPU使用率 | >80% | >75% | >70% | >70% | >65% | >60% |
| - 内存使用率 | >85% | >80% | >75% | >75% | >70% | >65% |
| - 响应时间 | >1000ms | >800ms | >500ms | >300ms | >200ms | >100ms |
| **性能指标** |  |  |  |  |  |  |
| 预期QPS | 50-100 | 200-500 | 500-1000 | 2000-5000 | 5000-15000 | 15000+ |
| 平均响应时间 | <1000ms | <800ms | <500ms | <300ms | <200ms | <100ms |
| 99%响应时间 | <2000ms | <1500ms | <1000ms | <800ms | <500ms | <300ms |
| **成本估算** |  |  |  |  |  |  |
| 服务器成本/月 | ¥500-800 | ¥1000-1500 | ¥2000-3000 | ¥5000-8000 | ¥15000-25000 | ¥30000+ |
| 运维复杂度 | 低 | 低 | 中 | 中高 | 高 | 极高 |

## 关键配置说明

### 小规模用户（0.5万-5万）
- **特点**: 业务简单，流量稳定
- **重点**: 成本控制，基础监控
- **架构**: 单体应用，简单部署

### 中规模用户（20万-50万）  
- **特点**: 流量波动，需要弹性
- **重点**: 性能优化，缓存策略
- **架构**: 负载均衡，读写分离

### 大规模用户（100万+）
- **特点**: 高并发，低延迟要求
- **重点**: 架构升级，自动化运维
- **架构**: 微服务，容器化部署

## 升级路径建议

1. **0.5万 → 2万**: 增加服务器配置，优化数据库查询
2. **2万 → 5万**: 引入缓存，数据库主从复制  
3. **5万 → 20万**: 负载均衡，读写分离，引入Redis
4. **20万 → 50万**: 微服务拆分，容器化部署
5. **50万 → 100万+**: 分布式架构，自动扩缩容

## 注意事项

1. **逐步优化**: 不要一次性过度优化，根据实际需求逐步升级
2. **监控先行**: 在优化前建立完善的监控体系
3. **压力测试**: 每次配置调整后都要进行压力测试验证
4. **成本平衡**: 在性能和成本之间找到最佳平衡点
5. **团队能力**: 考虑团队的技术能力和运维水平 