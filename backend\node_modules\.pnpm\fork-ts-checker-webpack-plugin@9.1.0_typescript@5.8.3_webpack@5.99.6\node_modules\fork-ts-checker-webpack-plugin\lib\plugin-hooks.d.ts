import { SyncHook, Sync<PERSON><PERSON>fallHook, Async<PERSON>eries<PERSON><PERSON>fallHook } from 'tapable';
import type * as webpack from 'webpack';
import type { FilesChange } from './files-change';
import type { Issue } from './issue';
declare function createPluginHooks(): {
    start: AsyncSeriesWaterfallHook<[FilesChange, webpack.Compilation], import("tapable").UnsetAdditionalOptions>;
    waiting: SyncHook<[webpack.Compilation], void, import("tapable").UnsetAdditionalOptions>;
    canceled: SyncHook<[webpack.Compilation], void, import("tapable").UnsetAdditionalOptions>;
    error: SyncHook<[unknown, webpack.Compilation], void, import("tapable").UnsetAdditionalOptions>;
    issues: SyncWaterfallHook<[Issue[], webpack.Compilation | undefined], void>;
};
type ForkTsCheckerWebpackPluginHooks = ReturnType<typeof createPluginHooks>;
declare function getPluginHooks(compiler: webpack.Compiler | webpack.MultiCompiler): {
    start: Async<PERSON>eriesWaterfallHook<[FilesChange, webpack.Compilation], import("tapable").UnsetAdditionalOptions>;
    waiting: SyncHook<[webpack.Compilation], void, import("tapable").UnsetAdditionalOptions>;
    canceled: SyncHook<[webpack.Compilation], void, import("tapable").UnsetAdditionalOptions>;
    error: SyncHook<[unknown, webpack.Compilation], void, import("tapable").UnsetAdditionalOptions>;
    issues: SyncWaterfallHook<[Issue[], webpack.Compilation | undefined], void>;
};
export { getPluginHooks, ForkTsCheckerWebpackPluginHooks };
