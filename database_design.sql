-- 书画作品评选系统数据库设计

-- 用户表
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    openid VARCHAR(100) UNIQUE NOT NULL COMMENT '微信openid',
    unionid VARCHAR(100) COMMENT '微信unionid',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    phone VARCHAR(20) COMMENT '手机号',
    real_name VARCHAR(50) COMMENT '真实姓名',
    role ENUM('admin', 'organizer', 'artist', 'judge', 'user') DEFAULT 'user' COMMENT '用户角色',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 项目表
CREATE TABLE projects (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL COMMENT '项目名称',
    description TEXT COMMENT '项目描述',
    cover_image VARCHAR(500) COMMENT '封面图片',
    organizer_id VARCHAR(36) NOT NULL COMMENT '发起人ID',
    status ENUM('preparing', 'collecting', 'reviewing', 'judging', 'displaying', 'finished') DEFAULT 'preparing' COMMENT '项目状态',
    collect_start_time DATETIME COMMENT '征稿开始时间',
    collect_end_time DATETIME COMMENT '征稿结束时间',
    judge_start_time DATETIME COMMENT '评选开始时间',
    judge_end_time DATETIME COMMENT '评选结束时间',
    display_start_time DATETIME COMMENT '展示开始时间',
    display_end_time DATETIME COMMENT '展示结束时间',
    qr_code VARCHAR(500) COMMENT '项目二维码',
    is_public TINYINT DEFAULT 1 COMMENT '是否公开：1-公开，0-私有',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (organizer_id) REFERENCES users(id)
);

-- 评选标准模板表
CREATE TABLE scoring_templates (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL COMMENT '模板名称',
    description TEXT COMMENT '模板描述',
    criteria JSON COMMENT '评分标准JSON',
    is_default TINYINT DEFAULT 0 COMMENT '是否默认模板',
    created_by VARCHAR(36) COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 项目评选标准表
CREATE TABLE project_criteria (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    project_id VARCHAR(36) NOT NULL COMMENT '项目ID',
    objective_criteria JSON COMMENT '客观标准JSON',
    subjective_criteria JSON COMMENT '主观标准JSON',
    total_score INT DEFAULT 100 COMMENT '总分',
    pass_score INT DEFAULT 60 COMMENT '及格分',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id)
);

-- 项目成员表
CREATE TABLE project_members (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    project_id VARCHAR(36) NOT NULL COMMENT '项目ID',
    user_id VARCHAR(36) NOT NULL COMMENT '用户ID',
    role ENUM('organizer', 'artist', 'judge') NOT NULL COMMENT '角色',
    status ENUM('invited', 'accepted', 'rejected') DEFAULT 'invited' COMMENT '状态',
    invited_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    responded_at TIMESTAMP NULL,
    FOREIGN KEY (project_id) REFERENCES projects(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    UNIQUE KEY unique_project_user (project_id, user_id)
);

-- 作品表
CREATE TABLE artworks (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    project_id VARCHAR(36) NOT NULL COMMENT '项目ID',
    artwork_no VARCHAR(50) NOT NULL COMMENT '作品编号',
    title VARCHAR(200) NOT NULL COMMENT '作品名称',
    description TEXT COMMENT '作品描述',
    size VARCHAR(50) COMMENT '作品尺寸',
    technique VARCHAR(100) COMMENT '创作技法',
    creation_year YEAR COMMENT '创作年份',
    image_url VARCHAR(500) NOT NULL COMMENT '作品图片URL',
    thumbnail_url VARCHAR(500) COMMENT '缩略图URL',
    author_name VARCHAR(50) NOT NULL COMMENT '作者姓名',
    author_phone VARCHAR(20) COMMENT '作者电话',
    author_address TEXT COMMENT '作者地址',
    author_bio TEXT COMMENT '作者简介',
    tags JSON COMMENT '作品标签',
    status ENUM('uploaded', 'reviewing', 'approved', 'rejected') DEFAULT 'uploaded' COMMENT '状态',
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    review_time TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id),
    UNIQUE KEY unique_project_no (project_id, artwork_no)
);

-- 评分表
CREATE TABLE scores (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    project_id VARCHAR(36) NOT NULL COMMENT '项目ID',
    artwork_id VARCHAR(36) NOT NULL COMMENT '作品ID',
    judge_id VARCHAR(36) NOT NULL COMMENT '评委ID',
    objective_pass TINYINT COMMENT '客观标准是否通过：1-通过，0-不通过',
    subjective_scores JSON COMMENT '主观评分JSON',
    total_score DECIMAL(5,2) COMMENT '总分',
    status ENUM('draft', 'submitted') DEFAULT 'draft' COMMENT '状态',
    scored_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id),
    FOREIGN KEY (artwork_id) REFERENCES artworks(id),
    FOREIGN KEY (judge_id) REFERENCES users(id),
    UNIQUE KEY unique_artwork_judge (artwork_id, judge_id)
);

-- 评语表
CREATE TABLE comments (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    score_id VARCHAR(36) NOT NULL COMMENT '评分ID',
    content TEXT COMMENT '评语内容',
    audio_url VARCHAR(500) COMMENT '语音评语URL',
    annotations JSON COMMENT '图片标注JSON',
    is_public TINYINT DEFAULT 1 COMMENT '是否公开：1-公开，0-私有',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (score_id) REFERENCES scores(id)
);

-- 系统配置表
CREATE TABLE system_configs (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(200) COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 操作日志表
CREATE TABLE operation_logs (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) COMMENT '操作用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作动作',
    resource_type VARCHAR(50) COMMENT '资源类型',
    resource_id VARCHAR(36) COMMENT '资源ID',
    details JSON COMMENT '操作详情',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 创建索引
CREATE INDEX idx_users_openid ON users(openid);
CREATE INDEX idx_projects_organizer ON projects(organizer_id);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_artworks_project ON artworks(project_id);
CREATE INDEX idx_artworks_status ON artworks(status);
CREATE INDEX idx_scores_artwork ON scores(artwork_id);
CREATE INDEX idx_scores_judge ON scores(judge_id);
CREATE INDEX idx_operation_logs_user ON operation_logs(user_id);
CREATE INDEX idx_operation_logs_created ON operation_logs(created_at);