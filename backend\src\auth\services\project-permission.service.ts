import { Injectable, NotFoundException, ConflictException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { 
  UserProjectRole, 
  ProjectRole,
  GlobalRole 
} from '../../users/entities/user-roles.entity';
import { RoleManagementService } from './role-management.service';

/**
 * 项目权限管理服务
 * 处理项目内的角色分配、权限管理、成员邀请等功能
 */
@Injectable()
export class ProjectPermissionService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(UserProjectRole)
    private projectRoleRepository: Repository<UserProjectRole>,
    private roleManagementService: RoleManagementService,
  ) {}

  /**
   * 邀请用户加入项目
   */
  async inviteUserToProject(
    projectId: string,
    userId: string,
    role: ProjectRole,
    permissions: any,
    invitedBy: string,
    metadata?: any
  ): Promise<UserProjectRole> {
    // 检查用户是否存在
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 检查邀请人是否有权限邀请
    const canInvite = await this.hasProjectPermission(invitedBy, projectId, 'invite');
    if (!canInvite) {
      throw new ForbiddenException('您没有邀请用户的权限');
    }

    // 检查用户是否已在项目中有相同角色
    const existingRole = await this.projectRoleRepository.findOne({
      where: { userId, projectId, role }
    });

    if (existingRole && existingRole.status === 'accepted') {
      throw new ConflictException('用户已在项目中拥有此角色');
    }

    if (existingRole && existingRole.status === 'pending') {
      throw new ConflictException('用户已有待处理的邀请');
    }

    // 如果有已拒绝或已移除的记录，更新它
    if (existingRole) {
      existingRole.permissions = permissions;
      existingRole.metadata = {
        ...existingRole.metadata,
        ...metadata,
        invitedBy,
        invitedAt: new Date()
      };
      existingRole.status = 'pending';
      return await this.projectRoleRepository.save(existingRole);
    }

    // 创建新的项目角色
    const projectRole = this.projectRoleRepository.create({
      userId,
      projectId,
      role,
      permissions,
      metadata: {
        invitedBy,
        invitedAt: new Date(),
        ...metadata
      },
      status: 'pending'
    });

    return await this.projectRoleRepository.save(projectRole);
  }

  /**
   * 接受项目邀请
   */
  async acceptProjectInvitation(userId: string, projectId: string, role?: ProjectRole): Promise<void> {
    const whereCondition: any = { userId, projectId, status: 'pending' };
    if (role) {
      whereCondition.role = role;
    }

    const invitation = await this.projectRoleRepository.findOne({
      where: whereCondition
    });

    if (!invitation) {
      throw new NotFoundException('邀请不存在或已过期');
    }

    // 检查邀请是否过期
    if (invitation.expiresAt && new Date() > invitation.expiresAt) {
      invitation.status = 'removed';
      await this.projectRoleRepository.save(invitation);
      throw new ForbiddenException('邀请已过期');
    }

    invitation.status = 'accepted';
    invitation.metadata = {
      ...invitation.metadata,
      acceptedAt: new Date()
    };

    await this.projectRoleRepository.save(invitation);
  }

  /**
   * 拒绝项目邀请
   */
  async declineProjectInvitation(userId: string, projectId: string, role?: ProjectRole): Promise<void> {
    const whereCondition: any = { userId, projectId, status: 'pending' };
    if (role) {
      whereCondition.role = role;
    }

    const invitation = await this.projectRoleRepository.findOne({
      where: whereCondition
    });

    if (!invitation) {
      throw new NotFoundException('邀请不存在');
    }

    invitation.status = 'declined';
    invitation.metadata = {
      ...invitation.metadata,
      declinedAt: new Date()
    };

    await this.projectRoleRepository.save(invitation);
  }

  /**
   * 更新项目角色权限
   */
  async updateProjectRolePermissions(
    userId: string,
    projectId: string,
    permissions: any,
    updatedBy: string
  ): Promise<void> {
    // 检查更新人是否有权限
    const canManage = await this.hasProjectPermission(updatedBy, projectId, 'manage');
    if (!canManage) {
      throw new ForbiddenException('您没有管理权限');
    }

    const projectRole = await this.projectRoleRepository.findOne({
      where: { userId, projectId, status: 'accepted' }
    });

    if (!projectRole) {
      throw new NotFoundException('用户不在项目中');
    }

    projectRole.permissions = permissions;
    projectRole.metadata = {
      ...projectRole.metadata,
      lastUpdatedBy: updatedBy,
      lastUpdatedAt: new Date()
    };

    await this.projectRoleRepository.save(projectRole);
  }

  /**
   * 移除项目成员
   */
  async removeProjectMember(
    userId: string,
    projectId: string,
    removedBy: string,
    reason?: string
  ): Promise<void> {
    // 检查移除人是否有权限
    const canManage = await this.hasProjectPermission(removedBy, projectId, 'manage');
    if (!canManage) {
      throw new ForbiddenException('您没有管理权限');
    }

    // 不能移除项目所有者
    const ownerRole = await this.projectRoleRepository.findOne({
      where: { userId, projectId, role: ProjectRole.PROJECT_OWNER, status: 'accepted' }
    });

    if (ownerRole) {
      throw new ForbiddenException('不能移除项目所有者');
    }

    await this.projectRoleRepository.update(
      { userId, projectId, status: 'accepted' },
      { 
        status: 'removed',
        metadata: () => `JSON_SET(metadata, '$.removedBy', '${removedBy}', '$.removedAt', '${new Date().toISOString()}', '$.reason', '${reason || ''}')`
      }
    );
  }

  /**
   * 获取用户在项目中的角色
   */
  async getUserProjectRoles(userId: string, projectId: string): Promise<UserProjectRole[]> {
    return await this.projectRoleRepository.find({
      where: {
        userId,
        projectId,
        status: 'accepted'
      },
      order: { createdAt: 'ASC' }
    });
  }

  /**
   * 获取项目成员列表
   */
  async getProjectMembers(
    projectId: string,
    filters?: {
      role?: ProjectRole;
      status?: string;
      page?: number;
      limit?: number;
    }
  ): Promise<{ data: UserProjectRole[]; total: number; page: number; limit: number }> {
    const { role, status = 'accepted', page = 1, limit = 50 } = filters || {};

    const queryBuilder = this.projectRoleRepository
      .createQueryBuilder('pr')
      .leftJoinAndSelect('pr.user', 'user')
      .where('pr.projectId = :projectId', { projectId })
      .andWhere('pr.status = :status', { status });

    if (role) {
      queryBuilder.andWhere('pr.role = :role', { role });
    }

    queryBuilder
      .orderBy('pr.createdAt', 'ASC')
      .skip((page - 1) * limit)
      .take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();

    return { data, total, page, limit };
  }

  /**
   * 检查用户在项目中的权限
   */
  async hasProjectPermission(
    userId: string,
    projectId: string,
    permission: string
  ): Promise<boolean> {
    // 获取用户信息
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['globalRoles']
    });

    if (!user) {
      return false;
    }

    // 超级管理员和系统管理员拥有所有权限
    const userRoles = user.globalRoles?.filter(gr => gr.isActive).map(gr => gr.role) || [];
    if (userRoles.includes(GlobalRole.SUPER_ADMIN) || userRoles.includes(GlobalRole.ADMIN)) {
      return true;
    }

    // 获取用户在项目中的角色
    const projectRoles = await this.getUserProjectRoles(userId, projectId);
    
    if (projectRoles.length === 0) {
      return false;
    }

    // 检查权限
    for (const projectRole of projectRoles) {
      if (this.checkPermission(projectRole, permission)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 检查单个角色的权限
   */
  private checkPermission(projectRole: UserProjectRole, permission: string): boolean {
    const permissions = projectRole.permissions || {};
    
    // 项目所有者拥有所有权限
    if (projectRole.role === ProjectRole.PROJECT_OWNER) {
      return true;
    }

    // 检查具体权限
    switch (permission) {
      case 'manage':
        return permissions.canManageProject || projectRole.role === ProjectRole.PROJECT_ADMIN;
      case 'invite':
        return permissions.canInviteUsers;
      case 'artwork_manage':
        return permissions.canManageArtworks;
      case 'score':
        return permissions.canScore;
      case 'view_scores':
        return permissions.canViewScores;
      case 'export':
        return permissions.canExportData;
      case 'moderate':
        return permissions.canModerateContent;
      case 'schedule':
        return permissions.canManageSchedule;
      default:
        return permissions.customPermissions?.includes(permission) || false;
    }
  }

  /**
   * 合并多个角色的权限
   */
  mergePermissions(projectRoles: UserProjectRole[]): any {
    const mergedPermissions = {
      canManageProject: false,
      canInviteUsers: false,
      canManageArtworks: false,
      canScore: false,
      canViewScores: false,
      canExportData: false,
      canModerateContent: false,
      canManageSchedule: false,
      customPermissions: []
    };

    for (const role of projectRoles) {
      const permissions = role.permissions || {};
      
      // 使用 OR 逻辑合并权限
      mergedPermissions.canManageProject = mergedPermissions.canManageProject || permissions.canManageProject;
      mergedPermissions.canInviteUsers = mergedPermissions.canInviteUsers || permissions.canInviteUsers;
      mergedPermissions.canManageArtworks = mergedPermissions.canManageArtworks || permissions.canManageArtworks;
      mergedPermissions.canScore = mergedPermissions.canScore || permissions.canScore;
      mergedPermissions.canViewScores = mergedPermissions.canViewScores || permissions.canViewScores;
      mergedPermissions.canExportData = mergedPermissions.canExportData || permissions.canExportData;
      mergedPermissions.canModerateContent = mergedPermissions.canModerateContent || permissions.canModerateContent;
      mergedPermissions.canManageSchedule = mergedPermissions.canManageSchedule || permissions.canManageSchedule;

      // 合并自定义权限
      if (permissions.customPermissions) {
        mergedPermissions.customPermissions = [
          ...new Set([...mergedPermissions.customPermissions, ...permissions.customPermissions])
        ];
      }
    }

    return mergedPermissions;
  }

  /**
   * 批量邀请用户
   */
  async batchInviteUsers(
    projectId: string,
    invitations: Array<{
      userId: string;
      role: ProjectRole;
      permissions?: any;
      message?: string;
    }>,
    invitedBy: string
  ): Promise<UserProjectRole[]> {
    const results: UserProjectRole[] = [];

    for (const invitation of invitations) {
      try {
        const permissions = invitation.permissions || 
          await this.roleManagementService.getDefaultPermissions(invitation.role);

        const projectRole = await this.inviteUserToProject(
          projectId,
          invitation.userId,
          invitation.role,
          permissions,
          invitedBy,
          {
            message: invitation.message,
            batchInvitation: true
          }
        );

        results.push(projectRole);
      } catch (error) {
        console.error(`Failed to invite user ${invitation.userId}:`, error.message);
      }
    }

    return results;
  }

  /**
   * 获取项目权限统计
   */
  async getProjectPermissionStats(projectId: string): Promise<any> {
    const roleStats = await this.projectRoleRepository
      .createQueryBuilder('pr')
      .select('pr.role', 'role')
      .addSelect('COUNT(*)', 'count')
      .where('pr.projectId = :projectId', { projectId })
      .andWhere('pr.status = :status', { status: 'accepted' })
      .groupBy('pr.role')
      .getRawMany();

    const statusStats = await this.projectRoleRepository
      .createQueryBuilder('pr')
      .select('pr.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .where('pr.projectId = :projectId', { projectId })
      .groupBy('pr.status')
      .getRawMany();

    const totalMembers = await this.projectRoleRepository.count({
      where: { projectId, status: 'accepted' }
    });

    return {
      roleDistribution: roleStats,
      statusDistribution: statusStats,
      totalMembers,
      projectId
    };
  }
}
