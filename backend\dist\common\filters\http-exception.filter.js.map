{"version": 3, "file": "http-exception.filter.js", "sourceRoot": "", "sources": ["../../../src/common/filters/http-exception.filter.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA,2CAOwB;AAExB,+BAAoC;AACpC,6DAAoF;AAO7E,IAAM,mBAAmB,2BAAzB,MAAM,mBAAmB;IACb,MAAM,GAAG,IAAI,eAAM,CAAC,qBAAmB,CAAC,IAAI,CAAC,CAAC;IAE/D,KAAK,CAAC,SAAkB,EAAE,IAAmB;QAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,EAAY,CAAC;QAC7C,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,EAAW,CAAC;QAG1C,MAAM,SAAS,GAAI,OAAO,CAAC,OAAO,CAAC,cAAc,CAAY,IAAI,IAAA,SAAM,GAAE,CAAC;QAE1E,IAAI,MAAc,CAAC;QACnB,IAAI,aAA+B,CAAC;QAEpC,IAAI,SAAS,YAAY,sBAAa,EAAE,CAAC;YACvC,MAAM,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YAC/B,MAAM,iBAAiB,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;YAGlD,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE,CAAC;gBAC1C,aAAa,GAAG,IAAI,CAAC,mBAAmB,CACtC,MAAM,EACN,iBAAiB,EACjB,2BAAS,CAAC,YAAY,EACtB,SAAS,CACV,CAAC;YACJ,CAAC;iBAAM,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE,CAAC;gBACjD,MAAM,QAAQ,GAAG,iBAAwB,CAAC;gBAG1C,IAAI,QAAQ,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;oBACxD,aAAa,GAAG,IAAI,CAAC,6BAA6B,CAChD,QAAQ,CAAC,OAAO,EAChB,SAAS,CACV,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,aAAa,GAAG,IAAI,CAAC,mBAAmB,CACtC,MAAM,EACN,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,KAAK,IAAI,QAAQ,EAC9C,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EACzB,SAAS,EACT,QAAQ,CAAC,OAAO,CACjB,CAAC;gBACJ,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,aAAa,GAAG,IAAI,CAAC,mBAAmB,CACtC,MAAM,EACN,QAAQ,EACR,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EACzB,SAAS,CACV,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,CAAC;YAEN,MAAM,GAAG,mBAAU,CAAC,qBAAqB,CAAC;YAC1C,aAAa,GAAG,IAAI,CAAC,mBAAmB,CACtC,MAAM,EACN,SAAS,EACT,2BAAS,CAAC,YAAY,EACtB,SAAS,CACV,CAAC;YAGF,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,wBAAwB,SAAS,EAAE,EACnC,SAAS,YAAY,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,EACxD,eAAe,SAAS,EAAE,CAC3B,CAAC;QACJ,CAAC;QAGD,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC;QAGjD,QAAQ,CAAC,SAAS,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;QAC9C,QAAQ,CAAC,SAAS,CAAC,cAAc,EAAE,iCAAiC,CAAC,CAAC;QAGtE,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC9C,CAAC;IAKO,mBAAmB,CACzB,IAAY,EACZ,OAAe,EACf,IAAe,EACf,SAAiB,EACjB,OAAa;QAEb,OAAO;YACL,OAAO,EAAE,KAAK;YACd,IAAI;YACJ,OAAO;YACP,KAAK,EAAE;gBACL,IAAI;gBACJ,OAAO;aACR;YACD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,SAAS;SACV,CAAC;IACJ,CAAC;IAKO,6BAA6B,CACnC,gBAA0B,EAC1B,SAAiB;QAEjB,MAAM,OAAO,GAA2B,EAAE,CAAC;QAE3C,gBAAgB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAE/B,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAC5C,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,GAAG,KAAK,CAAC;gBACjC,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;YAC3B,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;YAC7B,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,KAAK;YACd,IAAI,EAAE,8BAAY,CAAC,gBAAgB;YACnC,OAAO,EAAE,QAAQ;YACjB,KAAK,EAAE;gBACL,IAAI,EAAE,2BAAS,CAAC,gBAAgB;gBAChC,OAAO;aACR;YACD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,SAAS;SACV,CAAC;IACJ,CAAC;IAKO,YAAY,CAAC,MAAc;QACjC,IAAI,MAAM,IAAI,GAAG,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC;YAClC,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,GAAG;oBACN,OAAO,2BAAS,CAAC,oBAAoB,CAAC;gBACxC,KAAK,GAAG;oBACN,OAAO,2BAAS,CAAC,mBAAmB,CAAC;gBACvC,KAAK,GAAG;oBACN,OAAO,2BAAS,CAAC,gBAAgB,CAAC;gBACpC;oBACE,OAAO,2BAAS,CAAC,cAAc,CAAC;YACpC,CAAC;QACH,CAAC;aAAM,IAAI,MAAM,IAAI,GAAG,EAAE,CAAC;YACzB,OAAO,2BAAS,CAAC,YAAY,CAAC;QAChC,CAAC;QACD,OAAO,2BAAS,CAAC,YAAY,CAAC;IAChC,CAAC;IAKO,QAAQ,CACd,OAAgB,EAChB,aAA+B,EAC/B,SAAkB;QAElB,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;QAC7C,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;QAC9C,MAAM,MAAM,GAAI,OAAe,CAAC,IAAI,EAAE,EAAE,IAAI,WAAW,CAAC;QAExD,MAAM,UAAU,GAAG;YACjB,QAAQ,aAAa,CAAC,IAAI,QAAQ;YAClC,WAAW,MAAM,EAAE;YACnB,QAAQ,GAAG,EAAE;YACb,OAAO,EAAE,EAAE;YACX,SAAS,MAAM,EAAE;YACjB,eAAe,SAAS,EAAE;YAC1B,eAAe,aAAa,CAAC,SAAS,EAAE;YACxC,YAAY,aAAa,CAAC,OAAO,EAAE;SACpC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEd,IAAI,aAAa,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;YAC9B,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,UAAU,EACV,SAAS,YAAY,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CACzD,CAAC;QACJ,CAAC;aAAM,IAAI,aAAa,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;YACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;CACF,CAAA;AA9LY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,cAAK,GAAE;GACK,mBAAmB,CA8L/B;AAMD,MAAa,iBAAkB,SAAQ,sBAAa;IAClD,YACE,OAAe,EACf,OAAqB,8BAAY,CAAC,WAAW,EAC7C,OAAa;QAEb,KAAK,CACH;YACE,OAAO;YACP,IAAI;YACJ,OAAO;SACR,EACD,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,mBAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,mBAAU,CAAC,WAAW,CACxE,CAAC;IACJ,CAAC;CACF;AAfD,8CAeC;AAMD,MAAa,mBAAoB,SAAQ,sBAAa;IACpD,YAAY,OAAe,EAAE,OAAa;QACxC,KAAK,CACH;YACE,OAAO;YACP,IAAI,EAAE,8BAAY,CAAC,gBAAgB;YACnC,OAAO;SACR,EACD,mBAAU,CAAC,oBAAoB,CAChC,CAAC;IACJ,CAAC;CACF;AAXD,kDAWC"}