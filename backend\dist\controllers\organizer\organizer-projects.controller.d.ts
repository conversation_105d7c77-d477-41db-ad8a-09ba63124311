import { BaseController } from '../../common/base/base.controller';
import { ProjectsService } from '../../projects/projects.service';
import { CreateProjectDto } from '../../projects/dto/create-project.dto';
import { UpdateProjectDto } from '../../projects/dto/update-project.dto';
import { ProjectStatus } from '../../projects/entities/project.entity';
export declare class OrganizerProjectsController extends BaseController {
    private readonly projectsService;
    constructor(projectsService: ProjectsService);
    create(createProjectDto: CreateProjectDto, coverImage: Express.Multer.File, req: any): Promise<import("../../common/base/base.controller").ApiResponse<import("../../projects/entities/project.entity").Project> | import("../../common/base/base.controller").ApiErrorResponse>;
    findMyProjects(query: any, req: any): Promise<import("../../common/base/base.controller").PaginatedResponse<unknown>>;
    findOne(id: string, req: any): Promise<import("../../common/base/base.controller").ApiResponse<import("../../projects/entities/project.entity").Project> | import("../../common/base/base.controller").ApiErrorResponse>;
    update(id: string, updateProjectDto: UpdateProjectDto, coverImage: Express.Multer.File, req: any): Promise<import("../../common/base/base.controller").ApiResponse<import("../../projects/entities/project.entity").Project> | import("../../common/base/base.controller").ApiErrorResponse>;
    updateStatus(id: string, status: ProjectStatus, req: any): Promise<import("../../common/base/base.controller").ApiResponse<import("../../projects/entities/project.entity").Project> | import("../../common/base/base.controller").ApiErrorResponse>;
    generateQrCode(id: string, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<{
        qrCode: string;
    }>>;
    remove(id: string, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<null>>;
    copyProject(id: string, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<any>>;
    getProjectStats(id: string, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<any>>;
    exportProject(id: string, type: string | undefined, format: string | undefined, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<any>>;
}
