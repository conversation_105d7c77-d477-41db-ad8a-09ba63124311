import { BaseController } from '../../common/base/base.controller';
import { AuthService } from '../../auth/auth.service';
import { LoginDto } from '../../auth/dto/login.dto';
import { WechatLoginDto } from '../../auth/dto/wechat-login.dto';
import { RegisterDto } from '../../auth/dto/register.dto';
export declare class CommonAuthController extends BaseController {
    private readonly authService;
    constructor(authService: AuthService);
    login(loginDto: LoginDto, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<{
        accessToken: any;
        refreshToken: any;
        user: {
            id: string;
            username: string;
            nickname: string;
            role: import("../../users/entities/user.entity").UserRole;
            avatarUrl: string;
        };
    }>>;
    wechatLogin(wechatLoginDto: WechatLoginDto, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<{
        accessToken: any;
        refreshToken: any;
        user: {
            id: string;
            username: string;
            nickname: string;
            role: import("../../users/entities/user.entity").UserRole;
            avatarUrl: string;
        };
    }>>;
    register(registerDto: RegisterDto, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<{
        accessToken: any;
        refreshToken: any;
        user: {
            id: string;
            username: string;
            nickname: string;
            role: import("../../users/entities/user.entity").UserRole;
            avatarUrl: string;
        };
    }>>;
    getProfile(req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<{
        id: string;
        openid: string;
        unionid: string;
        username: string;
        email: string;
        nickname: string;
        avatarUrl: string;
        phone: string;
        realName: string;
        role: import("../../users/entities/user.entity").UserRole;
        loginType: import("../../users/entities/user.entity").LoginType;
        bio: string;
        profileData: {
            address?: string;
            birthDate?: string;
            gender?: "male" | "female" | "other";
            profession?: string;
            education?: string;
            website?: string;
            socialMedia?: {
                wechat?: string;
                weibo?: string;
                qq?: string;
            };
        };
        specialties: string[];
        certificationLevel: number;
        organizationId: string;
        status: import("../../users/entities/user.entity").UserStatus;
        lastLoginAt: Date;
        loginCount: number;
        settings: {
            language?: string;
            timezone?: string;
            emailNotifications?: boolean;
            smsNotifications?: boolean;
            theme?: "light" | "dark";
        };
        createdAt: Date;
        updatedAt: Date;
        globalRoles: import("../../users/entities/user-roles.entity").UserGlobalRole[];
        projectRoles: import("../../users/entities/user-roles.entity").UserProjectRole[];
    }>>;
    refreshToken(req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        accessToken: any;
        refreshToken: any;
        user: {
            id: string;
            username: string;
            nickname: string;
            role: import("../../users/entities/user.entity").UserRole;
            avatarUrl: string;
        };
    }>>;
    logout(req: any): Promise<import("../../common/base/base.controller").ApiResponse<null>>;
    changePassword(oldPassword: string, newPassword: string, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<null>>;
    forgotPassword(email: string, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<null>>;
    resetPassword(token: string, newPassword: string, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<null>>;
    verifyToken(req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        valid: boolean;
        user: {
            id: any;
            username: any;
            role: any;
        };
    }>>;
    getLoginHistory(req: any): Promise<import("../../common/base/base.controller").ApiResponse<any>>;
}
