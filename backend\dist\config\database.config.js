"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.databaseConfig = void 0;
const user_entity_1 = require("../users/entities/user.entity");
const project_entity_1 = require("../projects/entities/project.entity");
const organizer_application_entity_1 = require("../applications/entities/organizer-application.entity");
const artwork_entity_1 = require("../artworks/entities/artwork.entity");
const score_entity_1 = require("../scores/entities/score.entity");
exports.databaseConfig = {
    type: 'mysql',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 3306,
    username: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_DATABASE || 'art_score',
    entities: [
        user_entity_1.User,
        project_entity_1.Project,
        organizer_application_entity_1.OrganizerApplication,
        artwork_entity_1.Artwork,
        score_entity_1.Score,
        score_entity_1.Comment,
    ],
    synchronize: process.env.NODE_ENV !== 'production',
    logging: process.env.NODE_ENV === 'development',
    timezone: '+08:00',
};
//# sourceMappingURL=database.config.js.map