"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminUsersController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const base_controller_1 = require("../../common/base/base.controller");
const users_service_1 = require("../../users/users.service");
const create_user_dto_1 = require("../../users/dto/create-user.dto");
const update_user_dto_1 = require("../../users/dto/update-user.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../../auth/guards/roles.guard");
const roles_decorator_1 = require("../../auth/decorators/roles.decorator");
const user_entity_1 = require("../../users/entities/user.entity");
let AdminUsersController = class AdminUsersController extends base_controller_1.BaseController {
    usersService;
    constructor(usersService) {
        super();
        this.usersService = usersService;
    }
    async create(createUserDto, req) {
        try {
            const user = await this.usersService.create(createUserDto);
            return this.created(user, '用户创建成功', req);
        }
        catch (error) {
            if (error.message.includes('已存在')) {
                return this.conflict(error.message, req);
            }
            throw error;
        }
    }
    async findAll(query, req) {
        const { page, limit } = this.parsePagination(query);
        const { sort, order } = this.parseSort(query);
        const result = await this.usersService.findAll({
            page,
            limit,
            role: query.role,
            status: query.status ? parseInt(query.status) : undefined,
            search: query.search,
            sort,
            order
        });
        return this.paginated(result.data, result.total, result.page, result.limit, '用户列表获取成功', req);
    }
    async findOne(id, req) {
        try {
            const user = await this.usersService.findOne(id);
            return this.success(user, '用户详情获取成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('用户不存在', req);
            }
            throw error;
        }
    }
    async update(id, updateUserDto, req) {
        try {
            const user = await this.usersService.update(id, updateUserDto);
            return this.success(user, '用户信息更新成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('用户不存在', req);
            }
            throw error;
        }
    }
    async updateStatus(id, status, req) {
        try {
            const user = await this.usersService.updateStatus(id, status);
            const statusText = status === 1 ? '启用' : '禁用';
            return this.success(user, `用户${statusText}成功`, req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('用户不存在', req);
            }
            throw error;
        }
    }
    async updateRole(id, role, req) {
        try {
            const user = await this.usersService.updateRole(id, role);
            return this.success(user, '用户角色更新成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('用户不存在', req);
            }
            throw error;
        }
    }
    async remove(id, req) {
        try {
            await this.usersService.remove(id);
            return this.success(null, '用户删除成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('用户不存在', req);
            }
            throw error;
        }
    }
    async batchRemove(ids, req) {
        try {
            const result = await this.usersService.batchRemove(ids);
            return this.success(result, '批量删除完成', req);
        }
        catch (error) {
            throw error;
        }
    }
    async resetPassword(id, req) {
        try {
            const newPassword = await this.usersService.resetPassword(id);
            return this.success({ temporaryPassword: newPassword }, '密码重置成功，请通知用户及时修改', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('用户不存在', req);
            }
            throw error;
        }
    }
    async getStats(req) {
        const stats = await this.usersService.getUserStats();
        return this.success(stats, '用户统计信息获取成功', req);
    }
    async exportUsers(query, req) {
        const exportData = await this.usersService.exportUsers({
            role: query.role,
            status: query.status ? parseInt(query.status) : undefined,
            startDate: query.startDate,
            endDate: query.endDate,
            format: query.format || 'excel'
        });
        return this.success(exportData, '用户数据导出成功', req);
    }
};
exports.AdminUsersController = AdminUsersController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建用户' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '用户创建成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数验证失败' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '用户已存在' }),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.SUPER_ADMIN),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_user_dto_1.CreateUserDto, Object]),
    __metadata("design:returntype", Promise)
], AdminUsersController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取用户列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AdminUsersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AdminUsersController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新用户信息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_user_dto_1.UpdateUserDto, Object]),
    __metadata("design:returntype", Promise)
], AdminUsersController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, swagger_1.ApiOperation)({ summary: '更新用户状态' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '状态更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('status')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Object]),
    __metadata("design:returntype", Promise)
], AdminUsersController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Patch)(':id/role'),
    (0, swagger_1.ApiOperation)({ summary: '更新用户角色' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '角色更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.SUPER_ADMIN),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('role')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], AdminUsersController.prototype, "updateRole", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除用户' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.SUPER_ADMIN),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AdminUsersController.prototype, "remove", null);
__decorate([
    (0, common_1.Delete)('batch'),
    (0, swagger_1.ApiOperation)({ summary: '批量删除用户' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '批量删除成功' }),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.SUPER_ADMIN),
    __param(0, (0, common_1.Body)('ids')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, Object]),
    __metadata("design:returntype", Promise)
], AdminUsersController.prototype, "batchRemove", null);
__decorate([
    (0, common_1.Patch)(':id/reset-password'),
    (0, swagger_1.ApiOperation)({ summary: '重置用户密码' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '密码重置成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.SUPER_ADMIN),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AdminUsersController.prototype, "resetPassword", null);
__decorate([
    (0, common_1.Get)('stats/overview'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户统计信息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminUsersController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)('export'),
    (0, swagger_1.ApiOperation)({ summary: '导出用户数据' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '导出成功' }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AdminUsersController.prototype, "exportUsers", null);
exports.AdminUsersController = AdminUsersController = __decorate([
    (0, swagger_1.ApiTags)('管理端-用户管理'),
    (0, common_1.Controller)('admin/users'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.SUPER_ADMIN, user_entity_1.UserRole.ADMIN),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [users_service_1.UsersService])
], AdminUsersController);
//# sourceMappingURL=admin-users.controller.js.map