#!/bin/bash

echo "正在生成书画评选系统架构图表..."
echo

# 检查是否安装了mermaid-cli
if ! command -v mmdc &> /dev/null; then
    echo "错误: 未找到 mermaid-cli"
    echo "请先安装: npm install -g @mermaid-js/mermaid-cli"
    echo
    exit 1
fi

# 创建images目录
mkdir -p images

echo "生成多角色权限系统架构图..."
mmdc -i multi-role-system.mmd -o images/multi-role-system.png -w 1200 -H 800
mmdc -i multi-role-system.mmd -o images/multi-role-system.svg

echo "生成角色关系图..."
mmdc -i role-relationships.mmd -o images/role-relationships.png -w 1000 -H 600
mmdc -i role-relationships.mmd -o images/role-relationships.svg

echo "生成业务流程图..."
mmdc -i business-flow.mmd -o images/business-flow.png -w 1200 -H 800
mmdc -i business-flow.mmd -o images/business-flow.svg

echo
echo "图表生成完成！"
echo "生成的文件位置："
echo "- images/multi-role-system.png"
echo "- images/multi-role-system.svg"
echo "- images/role-relationships.png"
echo "- images/role-relationships.svg"
echo "- images/business-flow.png"
echo "- images/business-flow.svg"
echo
echo "您可以直接打开PNG文件查看图表"
