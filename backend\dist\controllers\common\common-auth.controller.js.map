{"version": 3, "file": "common-auth.controller.js", "sourceRoot": "", "sources": ["../../../src/controllers/common/common-auth.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CASwB;AACxB,6CAAoF;AACpF,uEAAmE;AACnE,0DAAsD;AACtD,wDAAoD;AACpD,sEAAiE;AACjE,8DAA0D;AAC1D,qEAAgE;AAQzD,IAAM,oBAAoB,GAA1B,MAAM,oBAAqB,SAAQ,gCAAc;IACzB;IAA7B,YAA6B,WAAwB;QACnD,KAAK,EAAE,CAAC;QADmB,gBAAW,GAAX,WAAW,CAAa;IAErD,CAAC;IAWK,AAAN,KAAK,CAAC,KAAK,CAAS,QAAkB,EAAa,GAAG;QACpD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACtD,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBACvC,OAAO,IAAI,CAAC,KAAK,CACf,UAAU,EACV,IAAI,CAAC,YAAY,CAAC,YAAY,EAC9B,sBAAsB,EACtB,SAAS,EACT,SAAS,EACT,GAAG,CACJ,CAAC;YACJ,CAAC;YACD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACnC,OAAO,IAAI,CAAC,KAAK,CACf,eAAe,EACf,IAAI,CAAC,YAAY,CAAC,aAAa,EAC/B,sBAAsB,EACtB,SAAS,EACT,SAAS,EACT,GAAG,CACJ,CAAC;YACJ,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAUK,AAAN,KAAK,CAAC,WAAW,CAAS,cAA8B,EAAa,GAAG;QACtE,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;YAClE,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACrC,OAAO,IAAI,CAAC,KAAK,CACf,YAAY,EACZ,IAAI,CAAC,YAAY,CAAC,WAAW,EAC7B,sBAAsB,EACtB,SAAS,EACT,SAAS,EACT,GAAG,CACJ,CAAC;YACJ,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAWK,AAAN,KAAK,CAAC,QAAQ,CAAS,WAAwB,EAAa,GAAG;QAC7D,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAC5D,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YAC3C,CAAC;YACD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACnC,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YACjE,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAWK,AAAN,KAAK,CAAC,UAAU,CAAY,GAAG;QAC7B,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC/D,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YACrC,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAYK,AAAN,KAAK,CAAC,YAAY,CAAY,GAAG;QAC/B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAWK,AAAN,KAAK,CAAC,MAAM,CAAY,GAAG;QACzB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC3C,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAYK,AAAN,KAAK,CAAC,cAAc,CACG,WAAmB,EACnB,WAAmB,EAC7B,GAAG;QAEd,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;YAC7E,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACpC,OAAO,IAAI,CAAC,KAAK,CACf,OAAO,EACP,IAAI,CAAC,YAAY,CAAC,WAAW,EAC7B,gBAAgB,EAChB,SAAS,EACT,SAAS,EACT,GAAG,CACJ,CAAC;YACJ,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAUK,AAAN,KAAK,CAAC,cAAc,CAAgB,KAAa,EAAa,GAAG;QAC/D,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;YACrD,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,eAAe,EAAE,GAAG,CAAC,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YACrC,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAUK,AAAN,KAAK,CAAC,aAAa,CACF,KAAa,EACP,WAAmB,EAC7B,GAAG;QAEd,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBACpE,OAAO,IAAI,CAAC,KAAK,CACf,YAAY,EACZ,IAAI,CAAC,YAAY,CAAC,WAAW,EAC7B,gBAAgB,EAChB,SAAS,EACT,SAAS,EACT,GAAG,CACJ,CAAC;YACJ,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAWK,AAAN,KAAK,CAAC,WAAW,CAAY,GAAG;QAC9B,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,KAAK,EAAE,IAAI;YACX,IAAI,EAAE;gBACJ,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE;gBACf,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ;gBAC3B,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI;aACpB;SACF,EACD,WAAW,EACX,GAAG,CACJ,CAAC;IACJ,CAAC;IAUK,AAAN,KAAK,CAAC,eAAe,CAAY,GAAG;QAClC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACpE,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AApRY,oDAAoB;AAczB;IANL,IAAA,aAAI,EAAC,OAAO,CAAC;IACb,IAAA,iBAAQ,EAAC,mBAAU,CAAC,EAAE,CAAC;IACvB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IACjC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACjD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC;IACrD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC;IACvC,WAAA,IAAA,aAAI,GAAE,CAAA;IAAsB,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCAApB,oBAAQ;;iDA2BrC;AAUK;IALL,IAAA,aAAI,EAAC,cAAc,CAAC;IACpB,IAAA,iBAAQ,EAAC,mBAAU,CAAC,EAAE,CAAC;IACvB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;IACpC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACjD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC;IACjC,WAAA,IAAA,aAAI,GAAE,CAAA;IAAkC,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCAA1B,iCAAc;;uDAiBvD;AAWK;IANL,IAAA,aAAI,EAAC,UAAU,CAAC;IAChB,IAAA,iBAAQ,EAAC,mBAAU,CAAC,OAAO,CAAC;IAC5B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IACjC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACjD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;IACtD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC;IACpC,WAAA,IAAA,aAAI,GAAE,CAAA;IAA4B,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCAAvB,0BAAW;;oDAa9C;AAWK;IANL,IAAA,YAAG,EAAC,SAAS,CAAC;IACd,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,uBAAa,GAAE;IACf,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACrC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACjD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;IACvC,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;sDAU1B;AAYK;IAPL,IAAA,aAAI,EAAC,SAAS,CAAC;IACf,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,uBAAa,GAAE;IACf,IAAA,iBAAQ,EAAC,mBAAU,CAAC,EAAE,CAAC;IACvB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;IACpC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;IACtD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;IACrC,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;wDAO5B;AAWK;IANL,IAAA,aAAI,EAAC,QAAQ,CAAC;IACd,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,uBAAa,GAAE;IACf,IAAA,iBAAQ,EAAC,mBAAU,CAAC,EAAE,CAAC;IACvB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IACjC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACpC,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;kDAOtB;AAYK;IAPL,IAAA,aAAI,EAAC,iBAAiB,CAAC;IACvB,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,uBAAa,GAAE;IACf,IAAA,iBAAQ,EAAC,mBAAU,CAAC,EAAE,CAAC;IACvB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IACjC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC;IACnD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC;IAEhD,WAAA,IAAA,aAAI,EAAC,aAAa,CAAC,CAAA;IACnB,WAAA,IAAA,aAAI,EAAC,aAAa,CAAC,CAAA;IACnB,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;0DAkBX;AAUK;IALL,IAAA,aAAI,EAAC,iBAAiB,CAAC;IACvB,IAAA,iBAAQ,EAAC,mBAAU,CAAC,EAAE,CAAC;IACvB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC;IAC1C,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC;IACrD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC;IAC7B,WAAA,IAAA,aAAI,EAAC,OAAO,CAAC,CAAA;IAAiB,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;0DAU5D;AAUK;IALL,IAAA,aAAI,EAAC,gBAAgB,CAAC;IACtB,IAAA,iBAAQ,EAAC,mBAAU,CAAC,EAAE,CAAC;IACvB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IACjC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC;IACnD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,YAAY,EAAE,CAAC;IAErD,WAAA,IAAA,aAAI,EAAC,OAAO,CAAC,CAAA;IACb,WAAA,IAAA,aAAI,EAAC,aAAa,CAAC,CAAA;IACnB,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;yDAkBX;AAWK;IANL,IAAA,YAAG,EAAC,cAAc,CAAC;IACnB,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,uBAAa,GAAE;IACf,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;IACvC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC;IACpD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;IACtC,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;uDAa3B;AAUK;IALL,IAAA,YAAG,EAAC,eAAe,CAAC;IACpB,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,uBAAa,GAAE;IACf,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IACnC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAC3B,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;2DAO/B;+BAnRU,oBAAoB;IAFhC,IAAA,iBAAO,EAAC,WAAW,CAAC;IACpB,IAAA,mBAAU,EAAC,aAAa,CAAC;qCAEkB,0BAAW;GAD1C,oBAAoB,CAoRhC"}