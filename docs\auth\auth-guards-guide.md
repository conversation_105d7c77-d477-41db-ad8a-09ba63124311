# 认证守卫使用指南

## 概述

本系统提供了三种认证守卫，满足不同的业务场景：

1. **NoAuthGuard** - 不需要认证（公开接口）
2. **RequiredAuthGuard** - 必须认证（受保护接口）
3. **OptionalAuthGuard** - 可选认证（灵活接口）

## 🔧 快速使用

### 1. 不需要认证的接口

```typescript
import { NoAuth } from '../../auth/decorators/auth.decorator';

@Controller('public')
export class PublicController {
  
  @NoAuth()
  @Get('info')
  async getPublicInfo() {
    return { message: '这是公开信息，任何人都可以访问' };
  }
}
```

### 2. 必须认证的接口

```typescript
import { RequiredAuth } from '../../auth/decorators/auth.decorator';
import { CurrentUser, UserId } from '../../auth/decorators/user.decorator';

@Controller('protected')
export class ProtectedController {
  
  @RequiredAuth()
  @Get('profile')
  async getProfile(@CurrentUser() user: User) {
    return {
      id: user.id,
      username: user.username,
      role: user.role
    };
  }

  @RequiredAuth()
  @Post('data')
  async createData(
    @UserId() userId: string,
    @Body() data: any
  ) {
    return this.service.create(userId, data);
  }
}
```

### 3. 可选认证的接口

```typescript
import { OptionalAuth } from '../../auth/decorators/auth.decorator';
import { CurrentUser, IsAuthenticated } from '../../auth/decorators/user.decorator';

@Controller('flexible')
export class FlexibleController {
  
  @OptionalAuth()
  @Get('articles')
  async getArticles(
    @CurrentUser() user: User | null,
    @IsAuthenticated() isAuth: boolean
  ) {
    if (user) {
      // 已登录用户 - 返回个性化内容
      return this.service.getPersonalizedArticles(user.id);
    } else {
      // 未登录用户 - 返回公开内容
      return this.service.getPublicArticles();
    }
  }
}
```

## 🎯 角色权限控制

### 预定义角色装饰器

```typescript
import { 
  AdminAuth, 
  SuperAdminAuth, 
  OrganizerAuth, 
  JudgeAuth 
} from '../../auth/decorators/auth.decorator';

@Controller('admin')
export class AdminController {
  
  // 只有管理员和超级管理员可以访问
  @AdminAuth()
  @Get('users')
  async getUsers() {
    return this.usersService.findAll();
  }

  // 只有超级管理员可以访问
  @SuperAdminAuth()
  @Delete('dangerous-operation')
  async dangerousOperation() {
    return { message: '危险操作完成' };
  }
}

@Controller('organizer')
export class OrganizerController {
  
  // 办展方、管理员、超级管理员可以访问
  @OrganizerAuth()
  @Post('projects')
  async createProject(@Body() data: any) {
    return this.projectsService.create(data);
  }
}

@Controller('judge')
export class JudgeController {
  
  // 评委、管理员、超级管理员可以访问
  @JudgeAuth()
  @Post('scores')
  async createScore(@Body() data: any) {
    return this.scoresService.create(data);
  }
}
```

### 自定义角色权限

```typescript
import { RoleAuth } from '../../auth/decorators/auth.decorator';
import { UserRole } from '../../users/entities/user.entity';

@Controller('custom')
export class CustomController {
  
  // 只允许办展方和美工组访问
  @RoleAuth([UserRole.ORGANIZER, UserRole.ARTIST])
  @Get('artwork-management')
  async artworkManagement() {
    return { message: '作品管理功能' };
  }

  // 只允许评委和管理员访问
  @RoleAuth([UserRole.JUDGE, UserRole.ADMIN, UserRole.SUPER_ADMIN])
  @Get('scoring-panel')
  async scoringPanel() {
    return { message: '评分面板' };
  }
}
```

## 📝 用户信息获取

### 基础用户信息

```typescript
import { 
  CurrentUser, 
  UserId, 
  UserRole, 
  SafeUser 
} from '../../auth/decorators/user.decorator';

@Controller('user-info')
export class UserInfoController {
  
  @RequiredAuth()
  @Get('basic')
  async getBasicInfo(
    @CurrentUser() user: User,           // 完整用户对象
    @UserId() userId: string,            // 只获取用户ID
    @UserRole() userRole: string,        // 只获取用户角色
    @SafeUser() safeUser: any           // 安全的用户信息（已过滤敏感字段）
  ) {
    return {
      fullUser: user,
      userId,
      userRole,
      safeUser
    };
  }

  @RequiredAuth()
  @Get('specific-field')
  async getSpecificField(
    @CurrentUser('username') username: string,  // 获取特定字段
    @CurrentUser('email') email: string
  ) {
    return { username, email };
  }
}
```

### 权限检查

```typescript
import { 
  IsAdmin, 
  IsSuperAdmin, 
  IsOrganizer, 
  IsJudge,
  IsAuthenticated 
} from '../../auth/decorators/user.decorator';

@Controller('permissions')
export class PermissionsController {
  
  @OptionalAuth()
  @Get('check')
  async checkPermissions(
    @IsAuthenticated() isAuth: boolean,
    @IsAdmin() isAdmin: boolean,
    @IsSuperAdmin() isSuperAdmin: boolean,
    @IsOrganizer() isOrganizer: boolean,
    @IsJudge() isJudge: boolean
  ) {
    return {
      isAuthenticated: isAuth,
      isAdmin,
      isSuperAdmin,
      isOrganizer,
      isJudge
    };
  }

  @RequiredAuth()
  @Get('conditional-logic')
  async conditionalLogic(
    @IsAdmin() isAdmin: boolean,
    @CurrentUser() user: User
  ) {
    if (isAdmin) {
      return this.getAdminData();
    } else {
      return this.getUserData(user.id);
    }
  }
}
```

## 🌐 客户端信息获取

```typescript
import { 
  ClientIP, 
  UserAgent, 
  RequestId 
} from '../../auth/decorators/user.decorator';

@Controller('client-info')
export class ClientInfoController {
  
  @OptionalAuth()
  @Get('info')
  async getClientInfo(
    @ClientIP() clientIP: string,
    @UserAgent() userAgent: string,
    @RequestId() requestId: string,
    @CurrentUser() user: User | null
  ) {
    return {
      client: {
        ip: clientIP,
        userAgent,
        requestId
      },
      user: user ? {
        id: user.id,
        username: user.username
      } : null,
      timestamp: new Date().toISOString()
    };
  }
}
```

## 🔄 实际业务场景示例

### 1. 文章系统

```typescript
@Controller('articles')
export class ArticlesController {
  
  // 公开文章列表
  @NoAuth()
  @Get()
  async getArticles() {
    return this.articlesService.getPublicArticles();
  }

  // 个性化文章推荐（可选登录）
  @OptionalAuth()
  @Get('recommended')
  async getRecommended(@CurrentUser() user: User | null) {
    if (user) {
      return this.articlesService.getPersonalizedArticles(user.id);
    }
    return this.articlesService.getPopularArticles();
  }

  // 创建文章（必须登录）
  @RequiredAuth()
  @Post()
  async createArticle(
    @Body() data: CreateArticleDto,
    @UserId() userId: string
  ) {
    return this.articlesService.create(userId, data);
  }

  // 管理文章（管理员专用）
  @AdminAuth()
  @Get('manage')
  async manageArticles() {
    return this.articlesService.getAllArticlesForAdmin();
  }
}
```

### 2. 用户中心

```typescript
@Controller('user-center')
export class UserCenterController {
  
  // 获取个人资料
  @RequiredAuth()
  @Get('profile')
  async getProfile(@CurrentUser() user: User) {
    return this.userService.getProfile(user.id);
  }

  // 更新个人资料
  @RequiredAuth()
  @Patch('profile')
  async updateProfile(
    @Body() data: UpdateProfileDto,
    @UserId() userId: string
  ) {
    return this.userService.updateProfile(userId, data);
  }

  // 获取用户统计（可选登录）
  @OptionalAuth()
  @Get('stats')
  async getUserStats(
    @CurrentUser() user: User | null,
    @IsAuthenticated() isAuth: boolean
  ) {
    if (isAuth && user) {
      return this.userService.getDetailedStats(user.id);
    }
    return this.userService.getPublicStats();
  }
}
```

### 3. 评分系统

```typescript
@Controller('scores')
export class ScoresController {
  
  // 查看公开评分
  @NoAuth()
  @Get('public/:artworkId')
  async getPublicScores(@Param('artworkId') artworkId: string) {
    return this.scoresService.getPublicScores(artworkId);
  }

  // 提交评分（评委专用）
  @JudgeAuth()
  @Post()
  async createScore(
    @Body() data: CreateScoreDto,
    @UserId() judgeId: string
  ) {
    return this.scoresService.create(judgeId, data);
  }

  // 查看详细评分（办展方和管理员）
  @RoleAuth([UserRole.ORGANIZER, UserRole.ADMIN, UserRole.SUPER_ADMIN])
  @Get('detailed/:projectId')
  async getDetailedScores(@Param('projectId') projectId: string) {
    return this.scoresService.getDetailedScores(projectId);
  }
}
```

## ⚠️ 注意事项

### 1. 错误处理

```typescript
@RequiredAuth()
@Get('sensitive-data')
async getSensitiveData(
  @CurrentUser() user: User,
  @IsAdmin() isAdmin: boolean
) {
  // 在业务逻辑中进行额外的权限检查
  if (!isAdmin && user.role !== 'super_admin') {
    throw new ForbiddenException('权限不足');
  }
  
  return this.getSensitiveData();
}
```

### 2. 可选认证的空值处理

```typescript
@OptionalAuth()
@Get('data')
async getData(@CurrentUser() user: User | null) {
  // 始终检查user是否为null
  const userId = user?.id || null;
  
  if (user) {
    // 已登录逻辑
    return this.getPersonalData(user.id);
  } else {
    // 未登录逻辑
    return this.getPublicData();
  }
}
```

### 3. 性能考虑

```typescript
// 对于高频访问的接口，考虑缓存用户信息
@OptionalAuth()
@Get('high-frequency')
async highFrequencyEndpoint(@CurrentUser() user: User | null) {
  // 避免在每次请求中都查询数据库
  // 可以考虑在守卫中实现用户信息缓存
}
```

## 🧪 测试示例

```typescript
describe('AuthGuards', () => {
  test('NoAuth should allow access without token', async () => {
    const response = await request(app)
      .get('/public/info')
      .expect(200);
    
    expect(response.body.success).toBe(true);
  });

  test('RequiredAuth should require valid token', async () => {
    await request(app)
      .get('/protected/profile')
      .expect(401);

    await request(app)
      .get('/protected/profile')
      .set('Authorization', `Bearer ${validToken}`)
      .expect(200);
  });

  test('OptionalAuth should work with and without token', async () => {
    // 无Token
    const response1 = await request(app)
      .get('/flexible/articles')
      .expect(200);
    
    expect(response1.body.data.user).toBeNull();

    // 有Token
    const response2 = await request(app)
      .get('/flexible/articles')
      .set('Authorization', `Bearer ${validToken}`)
      .expect(200);
    
    expect(response2.body.data.user).toBeDefined();
  });
});
```

这套认证守卫系统提供了灵活、安全、易用的认证解决方案，满足各种业务场景的需求。
