import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { JwtService } from '@nestjs/jwt';

describe('Auth Guards (e2e)', () => {
  let app: INestApplication;
  let jwtService: JwtService;
  let validToken: string;
  let adminToken: string;
  let judgeToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    jwtService = moduleFixture.get<JwtService>(JwtService);
    
    await app.init();

    // 生成测试Token
    validToken = jwtService.sign({ 
      sub: 'user-id-123', 
      username: 'testuser',
      role: 'user' 
    });

    adminToken = jwtService.sign({ 
      sub: 'admin-id-123', 
      username: 'admin',
      role: 'admin' 
    });

    judgeToken = jwtService.sign({ 
      sub: 'judge-id-123', 
      username: 'judge',
      role: 'judge' 
    });
  });

  afterAll(async () => {
    await app.close();
  });

  describe('NoAuth Guard', () => {
    it('should allow access without token', () => {
      return request(app.getHttpServer())
        .get('/examples/auth/public')
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.hasUser).toBe(false);
        });
    });

    it('should still work with token but ignore it', () => {
      return request(app.getHttpServer())
        .get('/examples/auth/public')
        .set('Authorization', `Bearer ${validToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.hasUser).toBe(false);
        });
    });
  });

  describe('RequiredAuth Guard', () => {
    it('should reject requests without token', () => {
      return request(app.getHttpServer())
        .get('/examples/auth/protected')
        .expect(401)
        .expect((res) => {
          expect(res.body.success).toBe(false);
          expect(res.body.message).toContain('请先登录');
        });
    });

    it('should reject requests with invalid token', () => {
      return request(app.getHttpServer())
        .get('/examples/auth/protected')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });

    it('should allow requests with valid token', () => {
      return request(app.getHttpServer())
        .get('/examples/auth/protected')
        .set('Authorization', `Bearer ${validToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.user).toBeDefined();
          expect(res.body.data.userId).toBe('user-id-123');
        });
    });
  });

  describe('OptionalAuth Guard', () => {
    it('should work without token', () => {
      return request(app.getHttpServer())
        .get('/examples/auth/optional')
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.isAuthenticated).toBe(false);
          expect(res.body.data.publicContent).toBeDefined();
          expect(res.body.data.personalizedContent).toBeUndefined();
        });
    });

    it('should work with valid token', () => {
      return request(app.getHttpServer())
        .get('/examples/auth/optional')
        .set('Authorization', `Bearer ${validToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.isAuthenticated).toBe(true);
          expect(res.body.data.user).toBeDefined();
          expect(res.body.data.personalizedContent).toBeDefined();
        });
    });

    it('should work with invalid token (fallback to unauthenticated)', () => {
      return request(app.getHttpServer())
        .get('/examples/auth/optional')
        .set('Authorization', 'Bearer invalid-token')
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.isAuthenticated).toBe(false);
          expect(res.body.data.publicContent).toBeDefined();
        });
    });
  });

  describe('Role-based Auth', () => {
    it('should allow admin access to admin-only endpoint', () => {
      return request(app.getHttpServer())
        .get('/examples/auth/admin-only')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.isAdmin).toBe(true);
        });
    });

    it('should deny non-admin access to admin-only endpoint', () => {
      return request(app.getHttpServer())
        .get('/examples/auth/admin-only')
        .set('Authorization', `Bearer ${validToken}`)
        .expect(403);
    });

    it('should allow judge access to judge-only endpoint', () => {
      return request(app.getHttpServer())
        .get('/examples/auth/judge-only')
        .set('Authorization', `Bearer ${judgeToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.judgeFeatures).toBeDefined();
        });
    });

    it('should deny non-judge access to judge-only endpoint', () => {
      return request(app.getHttpServer())
        .get('/examples/auth/judge-only')
        .set('Authorization', `Bearer ${validToken}`)
        .expect(403);
    });
  });

  describe('POST requests with auth', () => {
    it('should handle POST requests with authentication', () => {
      const testData = { title: 'Test Title', content: 'Test Content' };
      
      return request(app.getHttpServer())
        .post('/examples/auth/create-something')
        .set('Authorization', `Bearer ${validToken}`)
        .send(testData)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.createdBy).toBeDefined();
          expect(res.body.data.userId).toBe('user-id-123');
          expect(res.body.data.data).toEqual(testData);
        });
    });

    it('should reject POST requests without authentication', () => {
      return request(app.getHttpServer())
        .post('/examples/auth/create-something')
        .send({ title: 'Test' })
        .expect(401);
    });
  });

  describe('Error handling', () => {
    it('should handle business logic errors properly', () => {
      return request(app.getHttpServer())
        .get('/examples/auth/error-example')
        .set('Authorization', `Bearer ${validToken}`)
        .expect(403)
        .expect((res) => {
          expect(res.body.success).toBe(false);
          expect(res.body.message).toContain('普通用户无权访问');
        });
    });

    it('should allow admin to access error-example endpoint', () => {
      return request(app.getHttpServer())
        .get('/examples/auth/error-example')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
        });
    });
  });

  describe('User decorators', () => {
    it('should extract user information correctly', () => {
      return request(app.getHttpServer())
        .get('/examples/auth/protected')
        .set('Authorization', `Bearer ${validToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.data.user.id).toBe('user-id-123');
          expect(res.body.data.userId).toBe('user-id-123');
          expect(res.body.data.userRole).toBe('user');
        });
    });

    it('should provide client information', () => {
      return request(app.getHttpServer())
        .get('/examples/auth/optional')
        .set('User-Agent', 'Test-Agent/1.0')
        .expect(200)
        .expect((res) => {
          expect(res.body.data.clientInfo).toBeDefined();
          expect(res.body.data.clientInfo.userAgent).toBe('Test-Agent/1.0');
          expect(res.body.data.clientInfo.ip).toBeDefined();
        });
    });
  });
});
