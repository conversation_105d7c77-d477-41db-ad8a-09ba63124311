import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { BaseController } from '../../common/base/base.controller';
import { OrganizerAuth } from '../../auth/decorators/auth.decorator';
import {
  ProjectPermission,
  ProjectManager,
  ProjectId,
  CurrentProjectRole,
  CurrentProjectPermissions,
  IsProjectManager,
} from '../../auth/decorators/project-auth.decorator';
import { CurrentUser, UserId } from '../../auth/decorators/user.decorator';
import { User } from '../../users/entities/user.entity';
import { ProjectPermissionService } from '../../auth/services/project-permission.service';
import { RoleManagementService } from '../../auth/services/role-management.service';
import {
  InviteUserToProjectDto,
  BatchInviteUsersDto,
  UpdateProjectPermissionsDto,
  RemoveProjectMemberDto,
  QueryProjectMembersDto,
} from '../../users/dto/role-management.dto';

/**
 * 办展方项目权限管理控制器
 * 处理项目内的成员邀请、权限分配、角色管理等功能
 */
@ApiTags('办展方-项目权限管理')
@Controller('organizer/projects/:projectId/permissions')
@OrganizerAuth()
export class OrganizerProjectPermissionsController extends BaseController {
  constructor(
    private readonly projectPermissionService: ProjectPermissionService,
    private readonly roleManagementService: RoleManagementService,
  ) {
    super();
  }

  /**
   * 邀请用户加入项目
   */
  @ProjectPermission('invite')
  @Post('invite')
  @ApiOperation({ summary: '邀请用户加入项目' })
  @ApiResponse({ status: 201, description: '邀请发送成功' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  @ApiResponse({ status: 409, description: '用户已在项目中' })
  async inviteUser(
    @ProjectId() projectId: string,
    @Body() inviteDto: InviteUserToProjectDto,
    @UserId() invitedBy: string,
    @Request() req
  ) {
    try {
      // 获取默认权限或使用自定义权限
      const permissions = inviteDto.permissions || 
        await this.roleManagementService.getDefaultPermissions(inviteDto.role);

      const invitation = await this.projectPermissionService.inviteUserToProject(
        projectId,
        inviteDto.userId,
        inviteDto.role,
        permissions,
        invitedBy,
        {
          message: inviteDto.message,
          roleDescription: inviteDto.roleDescription,
          responsibilities: inviteDto.responsibilities,
        }
      );

      return this.created(
        {
          invitation,
          projectId,
          invitedRole: inviteDto.role,
          permissions,
        },
        '用户邀请发送成功',
        req
      );
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('用户不存在', req);
      }
      if (error.message.includes('已在项目中') || error.message.includes('已有待处理')) {
        return this.conflict(error.message, req);
      }
      if (error.message.includes('没有邀请用户的权限')) {
        return this.forbidden('您没有邀请用户的权限', req);
      }
      throw error;
    }
  }

  /**
   * 批量邀请用户
   */
  @ProjectPermission('invite')
  @Post('batch-invite')
  @ApiOperation({ summary: '批量邀请用户加入项目' })
  @ApiResponse({ status: 201, description: '批量邀请完成' })
  async batchInviteUsers(
    @ProjectId() projectId: string,
    @Body() batchDto: BatchInviteUsersDto,
    @UserId() invitedBy: string,
    @Request() req
  ) {
    try {
      const results = await this.projectPermissionService.batchInviteUsers(
        projectId,
        batchDto.invitations,
        invitedBy
      );

      return this.created(
        {
          successful: results.length,
          total: batchDto.invitations.length,
          results,
          projectId,
        },
        '批量邀请完成',
        req
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * 获取项目成员列表
   */
  @ProjectManager()
  @Get('members')
  @ApiOperation({ summary: '获取项目成员列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getProjectMembers(
    @ProjectId() projectId: string,
    @Query() query: QueryProjectMembersDto,
    @CurrentProjectPermissions() permissions: any,
    @Request() req
  ) {
    try {
      const result = await this.projectPermissionService.getProjectMembers(projectId, query);

      // 根据权限过滤敏感信息
      const canViewDetails = permissions.canViewScores || permissions.canManageProject;
      
      const filteredMembers = result.data.map(member => ({
        id: member.id,
        userId: member.userId,
        role: member.role,
        status: member.status,
        user: {
          id: member.user.id,
          username: member.user.username,
          nickname: member.user.nickname,
          realName: member.user.realName,
          avatar: member.user.avatar,
        },
        // 只有有权限的用户才能看到详细权限
        permissions: canViewDetails ? member.permissions : undefined,
        metadata: {
          invitedAt: member.metadata?.invitedAt,
          acceptedAt: member.metadata?.acceptedAt,
          roleDescription: member.metadata?.roleDescription,
        },
        createdAt: member.createdAt,
      }));

      return this.paginated(
        filteredMembers,
        result.total,
        result.page,
        result.limit,
        '项目成员列表获取成功',
        req
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * 更新成员权限
   */
  @ProjectManager()
  @Patch('members/:userId/permissions')
  @ApiOperation({ summary: '更新成员权限' })
  @ApiResponse({ status: 200, description: '权限更新成功' })
  @ApiResponse({ status: 404, description: '用户不在项目中' })
  async updateMemberPermissions(
    @ProjectId() projectId: string,
    @Param('userId') targetUserId: string,
    @Body() updateDto: UpdateProjectPermissionsDto,
    @UserId() updatedBy: string,
    @Request() req
  ) {
    try {
      await this.projectPermissionService.updateProjectRolePermissions(
        targetUserId,
        projectId,
        updateDto.permissions,
        updatedBy
      );

      return this.success(
        {
          projectId,
          targetUserId,
          newPermissions: updateDto.permissions,
          updatedBy,
        },
        '成员权限更新成功',
        req
      );
    } catch (error) {
      if (error.message.includes('不在项目中')) {
        return this.notFound('用户不在项目中', req);
      }
      if (error.message.includes('没有管理权限')) {
        return this.forbidden('您没有管理权限', req);
      }
      throw error;
    }
  }

  /**
   * 移除项目成员
   */
  @ProjectManager()
  @Delete('members/:userId')
  @ApiOperation({ summary: '移除项目成员' })
  @ApiResponse({ status: 200, description: '成员移除成功' })
  @ApiResponse({ status: 403, description: '不能移除项目所有者' })
  async removeProjectMember(
    @ProjectId() projectId: string,
    @Param('userId') targetUserId: string,
    @Body() removeDto: RemoveProjectMemberDto,
    @UserId() removedBy: string,
    @Request() req
  ) {
    try {
      await this.projectPermissionService.removeProjectMember(
        targetUserId,
        projectId,
        removedBy,
        removeDto.reason
      );

      return this.success(
        {
          projectId,
          removedUserId: targetUserId,
          removedBy,
          reason: removeDto.reason,
        },
        '项目成员移除成功',
        req
      );
    } catch (error) {
      if (error.message.includes('不能移除项目所有者')) {
        return this.forbidden('不能移除项目所有者', req);
      }
      if (error.message.includes('没有管理权限')) {
        return this.forbidden('您没有管理权限', req);
      }
      throw error;
    }
  }

  /**
   * 获取成员详细信息
   */
  @ProjectManager()
  @Get('members/:userId')
  @ApiOperation({ summary: '获取成员详细信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getMemberDetails(
    @ProjectId() projectId: string,
    @Param('userId') userId: string,
    @Request() req
  ) {
    try {
      const roles = await this.projectPermissionService.getUserProjectRoles(userId, projectId);

      if (roles.length === 0) {
        return this.notFound('用户不在项目中', req);
      }

      const mergedPermissions = this.projectPermissionService.mergePermissions(roles);

      return this.success(
        {
          userId,
          projectId,
          roles: roles.map(role => ({
            id: role.id,
            role: role.role,
            permissions: role.permissions,
            status: role.status,
            metadata: role.metadata,
            createdAt: role.createdAt,
          })),
          mergedPermissions,
          totalRoles: roles.length,
        },
        '成员详细信息获取成功',
        req
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * 获取项目权限统计
   */
  @ProjectManager()
  @Get('statistics')
  @ApiOperation({ summary: '获取项目权限统计' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getProjectPermissionStats(
    @ProjectId() projectId: string,
    @Request() req
  ) {
    try {
      const stats = await this.projectPermissionService.getProjectPermissionStats(projectId);

      return this.success(
        stats,
        '项目权限统计获取成功',
        req
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * 获取可用的权限模板
   */
  @ProjectManager()
  @Get('templates')
  @ApiOperation({ summary: '获取可用的权限模板' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getPermissionTemplates(@Query('role') role: string, @Request() req) {
    try {
      const templates = await this.roleManagementService.getPermissionTemplates(role as any);

      return this.success(
        templates,
        '权限模板获取成功',
        req
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * 我的项目权限信息
   */
  @ProjectManager()
  @Get('my-permissions')
  @ApiOperation({ summary: '获取我的项目权限信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getMyPermissions(
    @ProjectId() projectId: string,
    @CurrentUser() user: User,
    @CurrentProjectRole() roles: string[],
    @CurrentProjectPermissions() permissions: any,
    @IsProjectManager() isManager: boolean,
    @Request() req
  ) {
    try {
      return this.success(
        {
          projectId,
          user: {
            id: user.id,
            username: user.username,
            globalRoles: user.roles,
          },
          projectRoles: roles,
          permissions,
          isManager,
          capabilities: {
            canInviteUsers: permissions.canInviteUsers,
            canManageMembers: permissions.canManageProject,
            canViewAllPermissions: permissions.canViewScores || permissions.canManageProject,
            canExportData: permissions.canExportData,
          },
        },
        '我的项目权限信息获取成功',
        req
      );
    } catch (error) {
      throw error;
    }
  }
}
