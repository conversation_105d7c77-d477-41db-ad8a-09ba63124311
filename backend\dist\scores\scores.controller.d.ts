import { ScoresService } from './scores.service';
import { CreateScoreDto } from './dto/create-score.dto';
import { UpdateScoreDto } from './dto/update-score.dto';
import { CreateCommentDto } from './dto/create-comment.dto';
import { ScoreStatus } from './entities/score.entity';
export declare class ScoresController {
    private readonly scoresService;
    constructor(scoresService: ScoresService);
    create(createScoreDto: CreateScoreDto, req: any): Promise<import("./entities/score.entity").Score>;
    findByProject(projectId: string, page: number | undefined, limit: number | undefined, judgeId?: string, status?: ScoreStatus, req: any): Promise<{
        data: any;
        total: any;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    findByArtwork(artworkId: string, req: any): Promise<any>;
    findMyScores(projectId: string, page: number | undefined, limit: number | undefined, req: any): Promise<{
        data: any;
        total: any;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    findOne(id: string, req: any): Promise<import("./entities/score.entity").Score>;
    update(id: string, updateScoreDto: UpdateScoreDto, req: any): Promise<import("./entities/score.entity").Score>;
    submit(id: string, req: any): Promise<import("./entities/score.entity").Score>;
    remove(id: string, req: any): Promise<void>;
    createComment(scoreId: string, createCommentDto: CreateCommentDto, req: any): Promise<import("./entities/score.entity").Comment>;
    getComments(scoreId: string, req: any): Promise<import("./entities/score.entity").Comment[]>;
    updateComment(commentId: string, updateData: Partial<CreateCommentDto>, req: any): Promise<import("./entities/score.entity").Comment>;
    removeComment(commentId: string, req: any): Promise<void>;
    getProjectScoreStats(projectId: string, req: any): Promise<any>;
    getArtworkScoreStats(artworkId: string, req: any): Promise<any>;
}
