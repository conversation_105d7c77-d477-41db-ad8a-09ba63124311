import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  Request,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { BaseController } from '../../common/base/base.controller';
import { ProjectsService } from '../../projects/projects.service';
import { CreateProjectDto } from '../../projects/dto/create-project.dto';
import { UpdateProjectDto } from '../../projects/dto/update-project.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/entities/user.entity';
import { ProjectStatus } from '../../projects/entities/project.entity';

/**
 * 办展方项目管理控制器
 * 路由前缀: /api/v1/org/projects
 */
@ApiTags('办展方-项目管理')
@Controller('org/projects')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ORGANIZER, UserRole.ADMIN, UserRole.SUPER_ADMIN)
@ApiBearerAuth()
export class OrganizerProjectsController extends BaseController {
  constructor(private readonly projectsService: ProjectsService) {
    super();
  }

  /**
   * 创建项目
   */
  @Post()
  @ApiOperation({ summary: '创建项目' })
  @ApiResponse({ status: 201, description: '项目创建成功' })
  @ApiResponse({ status: 400, description: '参数验证失败' })
  @UseInterceptors(FileInterceptor('coverImage'))
  async create(
    @Body() createProjectDto: CreateProjectDto,
    @UploadedFile() coverImage: Express.Multer.File,
    @Request() req
  ) {
    try {
      const project = await this.projectsService.create(
        createProjectDto,
        req.user.id,
        coverImage
      );
      return this.created(project, '项目创建成功', req);
    } catch (error) {
      if (error.message.includes('验证失败')) {
        return this.validationError(error.details, error.message, req);
      }
      throw error;
    }
  }

  /**
   * 获取我的项目列表
   */
  @Get('my')
  @ApiOperation({ summary: '获取我的项目列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async findMyProjects(@Query() query: any, @Request() req) {
    const { page, limit } = this.parsePagination(query);
    const { sort, order } = this.parseSort(query);
    
    const result = await this.projectsService.findByOrganizer(req.user.id, {
      page,
      limit,
      status: query.status as ProjectStatus,
      search: query.search,
      sort,
      order
    });

    return this.paginated(
      result.data,
      result.total,
      result.page,
      result.limit,
      '项目列表获取成功',
      req
    );
  }

  /**
   * 获取项目详情
   */
  @Get(':id')
  @ApiOperation({ summary: '获取项目详情' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '项目不存在' })
  async findOne(@Param('id') id: string, @Request() req) {
    try {
      const project = await this.projectsService.findOne(id);
      
      // 检查权限：只有项目创建者、管理员和超级管理员可以查看
      if (
        req.user.role !== UserRole.SUPER_ADMIN &&
        req.user.role !== UserRole.ADMIN &&
        project.organizerId !== req.user.id
      ) {
        return this.forbidden('无权限查看此项目', req);
      }

      return this.success(project, '项目详情获取成功', req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('项目不存在', req);
      }
      throw error;
    }
  }

  /**
   * 更新项目信息
   */
  @Patch(':id')
  @ApiOperation({ summary: '更新项目信息' })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiResponse({ status: 404, description: '项目不存在' })
  @ApiResponse({ status: 403, description: '权限不足' })
  @UseInterceptors(FileInterceptor('coverImage'))
  async update(
    @Param('id') id: string,
    @Body() updateProjectDto: UpdateProjectDto,
    @UploadedFile() coverImage: Express.Multer.File,
    @Request() req
  ) {
    try {
      const project = await this.projectsService.update(
        id,
        updateProjectDto,
        req.user,
        coverImage
      );
      return this.success(project, '项目信息更新成功', req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('项目不存在', req);
      }
      if (error.message.includes('权限')) {
        return this.forbidden(error.message, req);
      }
      if (error.message.includes('验证失败')) {
        return this.validationError(error.details, error.message, req);
      }
      throw error;
    }
  }

  /**
   * 更新项目状态
   */
  @Patch(':id/status')
  @ApiOperation({ summary: '更新项目状态' })
  @ApiResponse({ status: 200, description: '状态更新成功' })
  @ApiResponse({ status: 400, description: '状态流转不合法' })
  @ApiResponse({ status: 404, description: '项目不存在' })
  async updateStatus(
    @Param('id') id: string,
    @Body('status') status: ProjectStatus,
    @Request() req
  ) {
    try {
      const project = await this.projectsService.updateStatus(id, status, req.user);
      const statusMap = {
        preparing: '准备中',
        collecting: '征集中',
        reviewing: '审核中',
        judging: '评选中',
        displaying: '展示中',
        finished: '已结束'
      };
      
      return this.success(
        project,
        `项目状态已更新为：${statusMap[status]}`,
        req
      );
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('项目不存在', req);
      }
      if (error.message.includes('权限')) {
        return this.forbidden(error.message, req);
      }
      if (error.message.includes('状态转换')) {
        return this.error(error.message, 400, 'BUSINESS_ERROR', undefined, undefined, req);
      }
      throw error;
    }
  }

  /**
   * 生成项目二维码
   */
  @Post(':id/qrcode')
  @ApiOperation({ summary: '生成项目二维码' })
  @ApiResponse({ status: 200, description: '二维码生成成功' })
  @ApiResponse({ status: 404, description: '项目不存在' })
  async generateQrCode(@Param('id') id: string, @Request() req) {
    try {
      const project = await this.projectsService.generateQrCode(id, req.user);
      return this.success(
        { qrCode: project.qrCode },
        '项目二维码生成成功',
        req
      );
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('项目不存在', req);
      }
      if (error.message.includes('权限')) {
        return this.forbidden(error.message, req);
      }
      throw error;
    }
  }

  /**
   * 删除项目
   */
  @Delete(':id')
  @ApiOperation({ summary: '删除项目' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 404, description: '项目不存在' })
  @ApiResponse({ status: 400, description: '项目状态不允许删除' })
  async remove(@Param('id') id: string, @Request() req) {
    try {
      await this.projectsService.remove(id, req.user);
      return this.success(null, '项目删除成功', req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('项目不存在', req);
      }
      if (error.message.includes('权限')) {
        return this.forbidden(error.message, req);
      }
      if (error.message.includes('不能删除')) {
        return this.error(error.message, 400, 'BUSINESS_ERROR', undefined, undefined, req);
      }
      throw error;
    }
  }

  /**
   * 复制项目
   */
  @Post(':id/copy')
  @ApiOperation({ summary: '复制项目' })
  @ApiResponse({ status: 201, description: '项目复制成功' })
  @ApiResponse({ status: 404, description: '项目不存在' })
  async copyProject(@Param('id') id: string, @Request() req) {
    try {
      const newProject = await this.projectsService.copyProject(id, req.user);
      return this.created(newProject, '项目复制成功', req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('项目不存在', req);
      }
      if (error.message.includes('权限')) {
        return this.forbidden(error.message, req);
      }
      throw error;
    }
  }

  /**
   * 获取项目统计信息
   */
  @Get(':id/stats')
  @ApiOperation({ summary: '获取项目统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '项目不存在' })
  async getProjectStats(@Param('id') id: string, @Request() req) {
    try {
      const stats = await this.projectsService.getProjectStats(id, req.user);
      return this.success(stats, '项目统计信息获取成功', req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('项目不存在', req);
      }
      if (error.message.includes('权限')) {
        return this.forbidden(error.message, req);
      }
      throw error;
    }
  }

  /**
   * 导出项目数据
   */
  @Get(':id/export')
  @ApiOperation({ summary: '导出项目数据' })
  @ApiResponse({ status: 200, description: '导出成功' })
  @ApiResponse({ status: 404, description: '项目不存在' })
  async exportProject(
    @Param('id') id: string,
    @Query('type') type: string = 'all',
    @Query('format') format: string = 'excel',
    @Request() req
  ) {
    try {
      const exportData = await this.projectsService.exportProject(
        id,
        req.user,
        { type, format }
      );
      return this.success(exportData, '项目数据导出成功', req);
    } catch (error) {
      if (error.message.includes('不存在')) {
        return this.notFound('项目不存在', req);
      }
      if (error.message.includes('权限')) {
        return this.forbidden(error.message, req);
      }
      throw error;
    }
  }
}
