{"version": 3, "file": "auth-examples.controller.js", "sourceRoot": "", "sources": ["../../../src/controllers/examples/auth-examples.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAMwB;AACxB,6CAAqE;AACrE,uEAAmE;AAGnE,yEAS8C;AAG9C,yEAU8C;AAE9C,kEAAkF;AAQ3E,IAAM,sBAAsB,GAA5B,MAAM,sBAAuB,SAAQ,gCAAc;IAUlD,AAAN,KAAK,CAAC,cAAc,CAAY,GAAG;QACjC,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,OAAO,EAAE,mBAAmB;YAC5B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI;SACpB,EACD,UAAU,EACV,GAAG,CACJ,CAAC;IACJ,CAAC;IAWK,AAAN,KAAK,CAAC,iBAAiB,CACN,IAAU,EACf,MAAc,EACZ,QAAgB,EACjB,GAAG;QAEd,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,OAAO,EAAE,qBAAqB;YAC9B,IAAI,EAAE;gBACJ,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB;YACD,MAAM;YACN,QAAQ;SACT,EACD,WAAW,EACX,GAAG,CACJ,CAAC;IACJ,CAAC;IAUK,AAAN,KAAK,CAAC,gBAAgB,CACL,IAAiB,EACb,MAAe,EACtB,QAAa,EACb,QAAgB,EACf,SAAiB,EACnB,GAAG;QAEd,IAAI,IAAI,EAAE,CAAC;YAET,OAAO,IAAI,CAAC,OAAO,CACjB;gBACE,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE;oBACJ,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,IAAI,EAAE,IAAI,CAAC,IAAI;iBAChB;gBACD,QAAQ;gBACR,eAAe,EAAE,MAAM;gBACvB,mBAAmB,EAAE;oBACnB,QAAQ;oBACR,QAAQ;oBACR,QAAQ;iBACT;gBACD,UAAU,EAAE;oBACV,EAAE,EAAE,QAAQ;oBACZ,SAAS;iBACV;aACF,EACD,WAAW,EACX,GAAG,CACJ,CAAC;QACJ,CAAC;aAAM,CAAC;YAEN,OAAO,IAAI,CAAC,OAAO,CACjB;gBACE,OAAO,EAAE,uBAAuB;gBAChC,eAAe,EAAE,MAAM;gBACvB,aAAa,EAAE;oBACb,OAAO;oBACP,OAAO;oBACP,OAAO;iBACR;gBACD,QAAQ,EAAE,cAAc;gBACxB,UAAU,EAAE;oBACV,EAAE,EAAE,QAAQ;oBACZ,SAAS;iBACV;aACF,EACD,UAAU,EACV,GAAG,CACJ,CAAC;QACJ,CAAC;IACH,CAAC;IAQK,AAAN,KAAK,CAAC,iBAAiB,CACN,IAAU,EACd,OAAgB,EAChB,GAAG;QAEd,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,OAAO,EAAE,WAAW;YACpB,SAAS,EAAE;gBACT,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB;YACD,OAAO;YACP,aAAa,EAAE;gBACb,MAAM;gBACN,MAAM;gBACN,MAAM;aACP;SACF,EACD,WAAW,EACX,GAAG,CACJ,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,sBAAsB,CACX,IAAU,EACT,YAAqB,EAC1B,GAAG;QAEd,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,OAAO,EAAE,aAAa;YACtB,cAAc,EAAE;gBACd,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB;YACD,YAAY;YACZ,kBAAkB,EAAE;gBAClB,MAAM;gBACN,MAAM;gBACN,MAAM;aACP;SACF,EACD,aAAa,EACb,GAAG,CACJ,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,qBAAqB,CAAgB,IAAU,EAAa,GAAG;QACnE,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,OAAO,EAAE,WAAW;YACpB,aAAa,EAAE;gBACb,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB;YACD,iBAAiB,EAAE;gBACjB,MAAM;gBACN,MAAM;gBACN,MAAM;aACP;SACF,EACD,WAAW,EACX,GAAG,CACJ,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,iBAAiB,CAAgB,IAAU,EAAa,GAAG;QAC/D,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,OAAO,EAAE,UAAU;YACnB,SAAS,EAAE;gBACT,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB;YACD,aAAa,EAAE;gBACb,MAAM;gBACN,MAAM;gBACN,MAAM;aACP;SACF,EACD,UAAU,EACV,GAAG,CACJ,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,mBAAmB,CAAgB,IAAU,EAAa,GAAG;QACjE,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,OAAO,EAAE,kBAAkB;YAC3B,IAAI,EAAE;gBACJ,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB;YACD,YAAY,EAAE,CAAC,WAAW,EAAE,QAAQ,CAAC;SACtC,EACD,aAAa,EACb,GAAG,CACJ,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,eAAe,CACX,IAAS,EACF,IAAU,EACf,MAAc,EACb,GAAG;QAEd,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,OAAO,EAAE,QAAQ;YACjB,SAAS,EAAE;gBACT,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB;YACD,MAAM;YACN,IAAI;YACJ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,EACD,QAAQ,EACR,GAAG,CACJ,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,YAAY,CAAgB,IAAU,EAAa,GAAG;QAE1D,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,OAAO,EAAE,aAAa;YACtB,IAAI,EAAE;gBACJ,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB;SACF,EACD,MAAM,EACN,GAAG,CACJ,CAAC;IACJ,CAAC;CACF,CAAA;AAlTY,wDAAsB;AAU3B;IAJL,IAAA,uBAAM,GAAE;IACR,IAAA,YAAG,EAAC,QAAQ,CAAC;IACb,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IACnC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAC5B,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;4DAU9B;AAWK;IALL,IAAA,6BAAY,GAAE;IACd,IAAA,YAAG,EAAC,WAAW,CAAC;IAChB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;IACpC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACjD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;IAE9C,WAAA,IAAA,4BAAW,GAAE,CAAA;IACb,WAAA,IAAA,uBAAM,GAAE,CAAA;IACR,WAAA,IAAA,yBAAQ,GAAE,CAAA;IACV,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCAHW,kBAAI;;+DAmB1B;AAUK;IAJL,IAAA,6BAAY,GAAE;IACd,IAAA,YAAG,EAAC,UAAU,CAAC;IACf,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACrC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAE/C,WAAA,IAAA,4BAAW,GAAE,CAAA;IACb,WAAA,IAAA,gCAAe,GAAE,CAAA;IACjB,WAAA,IAAA,yBAAQ,GAAE,CAAA;IACV,WAAA,IAAA,yBAAQ,GAAE,CAAA;IACV,WAAA,IAAA,0BAAS,GAAE,CAAA;IACX,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;8DAiDX;AAQK;IAHL,IAAA,0BAAS,GAAE;IACX,IAAA,YAAG,EAAC,YAAY,CAAC;IACjB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;IAElC,WAAA,IAAA,4BAAW,GAAE,CAAA;IACb,WAAA,IAAA,wBAAO,GAAE,CAAA;IACT,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCAFW,kBAAI;;+DAsB1B;AAQK;IAHL,IAAA,+BAAc,GAAE;IAChB,IAAA,YAAG,EAAC,kBAAkB,CAAC;IACvB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IAEpC,WAAA,IAAA,4BAAW,GAAE,CAAA;IACb,WAAA,IAAA,6BAAY,GAAE,CAAA;IACd,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCAFW,kBAAI;;oEAsB1B;AAQK;IAHL,IAAA,8BAAa,GAAE;IACf,IAAA,YAAG,EAAC,gBAAgB,CAAC;IACrB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;IACR,WAAA,IAAA,4BAAW,GAAE,CAAA;IAAc,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCAAhB,kBAAI;;mEAkBpD;AAQK;IAHL,IAAA,0BAAS,GAAE;IACX,IAAA,YAAG,EAAC,YAAY,CAAC;IACjB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IACX,WAAA,IAAA,4BAAW,GAAE,CAAA;IAAc,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCAAhB,kBAAI;;+DAkBhD;AAQK;IAHL,IAAA,yBAAQ,EAAC,CAAC,sBAAY,CAAC,SAAS,EAAE,sBAAY,CAAC,MAAM,CAAC,CAAC;IACvD,IAAA,YAAG,EAAC,cAAc,CAAC;IACnB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IACZ,WAAA,IAAA,4BAAW,GAAE,CAAA;IAAc,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCAAhB,kBAAI;;iEAclD;AAQK;IAHL,IAAA,6BAAY,GAAE;IACd,IAAA,aAAI,EAAC,kBAAkB,CAAC;IACxB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;IAErC,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,4BAAW,GAAE,CAAA;IACb,WAAA,IAAA,uBAAM,GAAE,CAAA;IACR,WAAA,IAAA,gBAAO,GAAE,CAAA;;6CAFW,kBAAI;;6DAkB1B;AAQK;IAHL,IAAA,6BAAY,GAAE;IACd,IAAA,YAAG,EAAC,eAAe,CAAC;IACpB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IAChB,WAAA,IAAA,4BAAW,GAAE,CAAA;IAAc,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCAAhB,kBAAI;;0DAiB3C;iCAjTU,sBAAsB;IAFlC,IAAA,iBAAO,EAAC,MAAM,CAAC;IACf,IAAA,mBAAU,EAAC,eAAe,CAAC;GACf,sBAAsB,CAkTlC"}