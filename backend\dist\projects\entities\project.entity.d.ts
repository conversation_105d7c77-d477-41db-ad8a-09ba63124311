import { User } from '../../users/entities/user.entity';
export declare enum ProjectStatus {
    PREPARING = "preparing",
    COLLECTING = "collecting",
    REVIEWING = "reviewing",
    JUDGING = "judging",
    DISPLAYING = "displaying",
    FINISHED = "finished"
}
export declare class Project {
    id: string;
    name: string;
    description: string;
    coverImage: string;
    organizerId: string;
    status: ProjectStatus;
    collectStartTime: Date;
    collectEndTime: Date;
    judgeStartTime: Date;
    judgeEndTime: Date;
    displayStartTime: Date;
    displayEndTime: Date;
    qrCode: string;
    isPublic: number;
    createdAt: Date;
    updatedAt: Date;
    organizer: User;
}
