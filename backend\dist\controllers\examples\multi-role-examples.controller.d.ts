import { BaseController } from '../../common/base/base.controller';
import { User } from '../../users/entities/user.entity';
import { ProjectRole } from '../../users/entities/user-roles.entity';
import { PermissionService } from '../../auth/services/permission.service';
export declare class MultiRoleExamplesController extends BaseController {
    private readonly permissionService;
    constructor(permissionService: PermissionService);
    getMyRoles(user: User, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        userId: string;
        username: string;
        globalRoles: string[];
        primaryRole: string;
        isAdmin: boolean;
        specialties: string[];
        certificationLevel: number;
    }>>;
    getOwnerDashboard(projectId: string, user: User, projectRoles: ProjectRole[], req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        message: string;
        projectId: string;
        owner: {
            id: string;
            username: string;
        };
        projectRoles: ProjectRole[];
        ownerFeatures: string[];
    }>>;
    getProjectManagement(projectId: string, user: User, isOwner: boolean, isManager: boolean, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        message: string;
        projectId: string;
        manager: {
            id: string;
            username: string;
        };
        permissions: {
            isOwner: boolean;
            isManager: boolean;
        };
        managementFeatures: string[];
    }>>;
    getJudgingPanel(projectId: string, user: User, canScore: boolean, canViewScores: boolean, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        message: string;
        projectId: string;
        judge: {
            id: string;
            username: string;
            specialties: string[];
        };
        permissions: {
            canScore: boolean;
            canViewScores: boolean;
        };
        judgingFeatures: string[];
    }>>;
    getMemberInfo(projectId: string, user: User, projectRoles: ProjectRole[], permissions: any, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        message: string;
        projectId: string;
        member: {
            id: string;
            username: string;
        };
        projectRoles: ProjectRole[];
        permissions: any;
        memberFeatures: string[];
    }>>;
    exportProjectData(projectId: string, user: User, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        message: string;
        projectId: string;
        exporter: {
            id: string;
            username: string;
        };
        exportData: {
            projects: string;
            artworks: string;
            scores: string;
            members: string;
        };
    }>>;
    inviteUser(projectId: string, inviteData: {
        userId: string;
        role: ProjectRole;
        permissions?: any;
        message?: string;
    }, user: User, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        invitation: import("../../users/entities/user-roles.entity").UserProjectRole;
        inviter: {
            id: string;
            username: string;
        };
        invitedRole: ProjectRole;
        permissions: any;
    }> | import("../../common/base/base.controller").ApiErrorResponse>;
    acceptInvitation(projectId: string, userId: string, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<{
        projectId: string;
        userId: string;
        status: string;
    }>>;
    updateMemberPermissions(projectId: string, targetUserId: string, newPermissions: any, user: User, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<{
        projectId: string;
        targetUserId: string;
        newPermissions: any;
        updatedBy: {
            id: string;
            username: string;
        };
    }>>;
    getProjectMembers(projectId: string, canViewScores: boolean, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        projectId: string;
        members: {
            id: string;
            userId: string;
            role: ProjectRole;
            status: "accepted" | "pending" | "declined" | "removed";
            user: {
                id: string;
                username: string;
                nickname: string;
                avatar: string;
            };
            permissions: {
                canManageProject?: boolean;
                canInviteUsers?: boolean;
                canManageArtworks?: boolean;
                canScore?: boolean;
                canViewScores?: boolean;
                canExportData?: boolean;
                canModerateContent?: boolean;
                canManageSchedule?: boolean;
                customPermissions?: string[];
            } | undefined;
            metadata: {
                invitedAt: Date | undefined;
                acceptedAt: Date | undefined;
            };
        }[];
        totalMembers: number;
        canViewDetails: boolean;
    }>>;
    getConditionalData(projectId: string, user: User, isOwner: boolean, isManager: boolean, canScore: boolean, canViewScores: boolean, canExport: boolean, req: any): Promise<import("../../common/base/base.controller").ApiResponse<any>>;
}
