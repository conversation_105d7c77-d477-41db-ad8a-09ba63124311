/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { ValidateFunction } from 'ajv';
import { Observable } from 'rxjs';
import { BaseException } from '../../exception';
import { JsonObject } from '../utils';
import { JsonVisitor, PromptProvider, SchemaFormat, SchemaRegistry, SchemaValidator, SchemaValidatorError, SmartDefaultProvider } from './interface';
import { JsonSchema } from './schema';
export type UriHandler = (uri: string) => Observable<JsonObject> | Promise<JsonObject> | null | undefined;
export declare class SchemaValidationException extends BaseException {
    readonly errors: SchemaValidatorError[];
    constructor(errors?: SchemaValidatorError[], baseMessage?: string);
    static createMessages(errors?: SchemaValidatorError[]): string[];
}
export declare class CoreSchemaRegistry implements SchemaRegistry {
    private _ajv;
    private _uriCache;
    private _uriHandlers;
    private _pre;
    private _post;
    private _currentCompilationSchemaInfo?;
    private _smartDefaultKeyword;
    private _promptProvider?;
    private _sourceMap;
    constructor(formats?: SchemaFormat[]);
    private _fetch;
    /**
     * Add a transformation step before the validation of any Json.
     * @param {JsonVisitor} visitor The visitor to transform every value.
     * @param {JsonVisitor[]} deps A list of other visitors to run before.
     */
    addPreTransform(visitor: JsonVisitor, deps?: JsonVisitor[]): void;
    /**
     * Add a transformation step after the validation of any Json. The JSON will not be validated
     * after the POST, so if transformations are not compatible with the Schema it will not result
     * in an error.
     * @param {JsonVisitor} visitor The visitor to transform every value.
     * @param {JsonVisitor[]} deps A list of other visitors to run before.
     */
    addPostTransform(visitor: JsonVisitor, deps?: JsonVisitor[]): void;
    protected _resolver(ref: string, validate?: ValidateFunction): {
        context?: ValidateFunction;
        schema?: JsonObject;
    };
    /**
     * Flatten the Schema, resolving and replacing all the refs. Makes it into a synchronous schema
     * that is also easier to traverse. Does not cache the result.
     *
     * Producing a flatten schema document does not in all cases produce a schema with identical behavior to the original.
     * See: https://json-schema.org/draft/2019-09/json-schema-core.html#rfc.appendix.B.2
     *
     * @param schema The schema or URI to flatten.
     * @returns An Observable of the flattened schema object.
     * @private since 11.2 without replacement.
     */
    ɵflatten(schema: JsonObject): Promise<JsonObject>;
    /**
     * Compile and return a validation function for the Schema.
     *
     * @param schema The schema to validate. If a string, will fetch the schema before compiling it
     * (using schema as a URI).
     */
    compile(schema: JsonSchema): Promise<SchemaValidator>;
    private _compile;
    addFormat(format: SchemaFormat): void;
    addSmartDefaultProvider<T>(source: string, provider: SmartDefaultProvider<T>): void;
    registerUriHandler(handler: UriHandler): void;
    usePromptProvider(provider: PromptProvider): void;
    private _applyPrompts;
    private static _set;
    private _applySmartDefaults;
    useXDeprecatedProvider(onUsage: (message: string) => void): void;
    private normalizeDataPathArr;
}
