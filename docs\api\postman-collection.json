{"info": {"name": "书画作品评选系统 API", "description": "书画作品评选管理系统的完整 API 接口集合", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3000/api/v1", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}], "item": [{"name": "认证接口", "item": [{"name": "用户登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data.accessToken) {", "        pm.collectionVariables.set('access_token', response.data.accessToken);", "    }", "}"]}}]}, {"name": "微信登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"code\": \"wx_login_code\",\n  \"userInfo\": {\n    \"nickname\": \"张评委\",\n    \"avatarUrl\": \"https://wx.qlogo.cn/...\"\n  }\n}"}, "url": {"raw": "{{base_url}}/auth/wechat-login", "host": ["{{base_url}}"], "path": ["auth", "wechat-login"]}}}, {"name": "获取用户信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/profile", "host": ["{{base_url}}"], "path": ["auth", "profile"]}}}]}, {"name": "用户管理", "item": [{"name": "获取用户列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users?page=1&limit=10&role=organizer", "host": ["{{base_url}}"], "path": ["users"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "role", "value": "organizer"}]}}}, {"name": "更新用户状态", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": 1\n}"}, "url": {"raw": "{{base_url}}/users/{{user_id}}/status", "host": ["{{base_url}}"], "path": ["users", "{{user_id}}", "status"]}}}]}, {"name": "项目管理", "item": [{"name": "创建项目", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"2024年春季书法大赛\",\n  \"description\": \"面向全国的书法作品征集活动\",\n  \"collectStartTime\": \"2024-01-01T00:00:00Z\",\n  \"collectEndTime\": \"2024-01-31T23:59:59Z\",\n  \"judgeStartTime\": \"2024-02-01T00:00:00Z\",\n  \"judgeEndTime\": \"2024-02-15T23:59:59Z\",\n  \"isPublic\": true\n}"}, "url": {"raw": "{{base_url}}/projects", "host": ["{{base_url}}"], "path": ["projects"]}}}, {"name": "获取项目列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/projects?page=1&limit=10&public=true", "host": ["{{base_url}}"], "path": ["projects"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "public", "value": "true"}]}}}, {"name": "更新项目状态", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"collecting\"\n}"}, "url": {"raw": "{{base_url}}/projects/{{project_id}}/status", "host": ["{{base_url}}"], "path": ["projects", "{{project_id}}", "status"]}}}]}, {"name": "作品管理", "item": [{"name": "上传作品", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "projectId", "value": "{{project_id}}", "type": "text"}, {"key": "title", "value": "临王羲之兰亭序", "type": "text"}, {"key": "<PERSON><PERSON><PERSON>", "value": "王书法", "type": "text"}, {"key": "image", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/artworks", "host": ["{{base_url}}"], "path": ["artworks"]}}}, {"name": "获取作品列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/artworks/project/{{project_id}}?page=1&limit=20", "host": ["{{base_url}}"], "path": ["artworks", "project", "{{project_id}}"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}]}, {"name": "评分系统", "item": [{"name": "创建评分", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"projectId\": \"{{project_id}}\",\n  \"artworkId\": \"{{artwork_id}}\",\n  \"objectivePass\": true,\n  \"subjectiveScores\": {\n    \"临帖传承\": 25,\n    \"结体结构\": 28,\n    \"艺术创意\": 35\n  },\n  \"totalScore\": 88\n}"}, "url": {"raw": "{{base_url}}/scores", "host": ["{{base_url}}"], "path": ["scores"]}}}, {"name": "获取我的评分", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/scores/my/{{project_id}}?page=1&limit=10", "host": ["{{base_url}}"], "path": ["scores", "my", "{{project_id}}"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}]}, {"name": "申请管理", "item": [{"name": "提交申请", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"organizationName\": \"中华书法文化协会\",\n  \"organizationType\": \"association\",\n  \"contactPerson\": \"李主任\",\n  \"contactPhone\": \"13888888888\",\n  \"contactEmail\": \"<EMAIL>\",\n  \"introduction\": \"协会成立于1990年，致力于传承和发扬中华书法文化\"\n}"}, "url": {"raw": "{{base_url}}/applications", "host": ["{{base_url}}"], "path": ["applications"]}}}, {"name": "审批申请", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"approved\",\n  \"reviewComment\": \"申请材料齐全，机构资质符合要求，同意通过申请。\"\n}"}, "url": {"raw": "{{base_url}}/applications/{{application_id}}/review", "host": ["{{base_url}}"], "path": ["applications", "{{application_id}}", "review"]}}}]}]}