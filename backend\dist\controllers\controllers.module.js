"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ControllersModule = void 0;
const common_1 = require("@nestjs/common");
const admin_users_controller_1 = require("./admin/admin-users.controller");
const admin_projects_controller_1 = require("./admin/admin-projects.controller");
const admin_applications_controller_1 = require("./admin/admin-applications.controller");
const admin_stats_controller_1 = require("./admin/admin-stats.controller");
const organizer_projects_controller_1 = require("./organizer/organizer-projects.controller");
const organizer_artworks_controller_1 = require("./organizer/organizer-artworks.controller");
const organizer_scores_controller_1 = require("./organizer/organizer-scores.controller");
const mp_projects_controller_1 = require("./miniprogram/mp-projects.controller");
const mp_artworks_controller_1 = require("./miniprogram/mp-artworks.controller");
const mp_scores_controller_1 = require("./miniprogram/mp-scores.controller");
const common_auth_controller_1 = require("./common/common-auth.controller");
const common_files_controller_1 = require("./common/common-files.controller");
const users_module_1 = require("../users/users.module");
const projects_module_1 = require("../projects/projects.module");
const artworks_module_1 = require("../artworks/artworks.module");
const scores_module_1 = require("../scores/scores.module");
const applications_module_1 = require("../applications/applications.module");
const files_module_1 = require("../files/files.module");
const auth_module_1 = require("../auth/auth.module");
let ControllersModule = class ControllersModule {
};
exports.ControllersModule = ControllersModule;
exports.ControllersModule = ControllersModule = __decorate([
    (0, common_1.Module)({
        imports: [
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
            projects_module_1.ProjectsModule,
            artworks_module_1.ArtworksModule,
            scores_module_1.ScoresModule,
            applications_module_1.ApplicationsModule,
            files_module_1.FilesModule,
        ],
        controllers: [
            admin_users_controller_1.AdminUsersController,
            admin_projects_controller_1.AdminProjectsController,
            admin_applications_controller_1.AdminApplicationsController,
            admin_stats_controller_1.AdminStatsController,
            organizer_projects_controller_1.OrganizerProjectsController,
            organizer_artworks_controller_1.OrganizerArtworksController,
            organizer_scores_controller_1.OrganizerScoresController,
            mp_projects_controller_1.MpProjectsController,
            mp_artworks_controller_1.MpArtworksController,
            mp_scores_controller_1.MpScoresController,
            common_auth_controller_1.CommonAuthController,
            common_files_controller_1.CommonFilesController,
        ],
    })
], ControllersModule);
//# sourceMappingURL=controllers.module.js.map