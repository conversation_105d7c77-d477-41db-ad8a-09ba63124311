export declare enum UserRole {
    SUPER_ADMIN = "super_admin",
    ADMIN = "admin",
    ORGANIZER = "organizer",
    ARTIST = "artist",
    JUDGE = "judge",
    USER = "user"
}
export declare enum LoginType {
    WECHAT = "wechat",
    WEBSITE = "website"
}
export declare class User {
    id: string;
    openid: string;
    unionid: string;
    username: string;
    password: string;
    email: string;
    nickname: string;
    avatarUrl: string;
    phone: string;
    realName: string;
    role: UserRole;
    loginType: LoginType;
    status: number;
    createdAt: Date;
    updatedAt: Date;
}
