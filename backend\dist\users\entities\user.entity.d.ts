import { UserGlobalRole, UserProjectRole } from './user-roles.entity';
export declare enum UserRole {
    SUPER_ADMIN = "super_admin",
    ADMIN = "admin",
    ORGANIZER = "organizer",
    ARTIST = "artist",
    JUDGE = "judge",
    USER = "user"
}
export declare enum UserStatus {
    ACTIVE = 1,
    INACTIVE = 0,
    PENDING = 2,
    REJECTED = 3
}
export declare enum LoginType {
    WECHAT = "wechat",
    WEBSITE = "website"
}
export declare class User {
    id: string;
    openid: string;
    unionid: string;
    username: string;
    password: string;
    email: string;
    nickname: string;
    avatarUrl: string;
    phone: string;
    realName: string;
    role: UserRole;
    loginType: LoginType;
    bio: string;
    profileData: {
        address?: string;
        birthDate?: string;
        gender?: 'male' | 'female' | 'other';
        profession?: string;
        education?: string;
        website?: string;
        socialMedia?: {
            wechat?: string;
            weibo?: string;
            qq?: string;
        };
    };
    specialties: string[];
    certificationLevel: number;
    organizationId: string;
    status: UserStatus;
    lastLoginAt: Date;
    loginCount: number;
    settings: {
        language?: string;
        timezone?: string;
        emailNotifications?: boolean;
        smsNotifications?: boolean;
        theme?: 'light' | 'dark';
    };
    createdAt: Date;
    updatedAt: Date;
    globalRoles: UserGlobalRole[];
    projectRoles: UserProjectRole[];
    get roles(): string[];
    hasGlobalRole(role: string): boolean;
    get isAdmin(): boolean;
    get primaryRole(): string;
}
