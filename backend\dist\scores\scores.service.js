"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScoresService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const score_entity_1 = require("./entities/score.entity");
const user_entity_1 = require("../users/entities/user.entity");
const projects_service_1 = require("../projects/projects.service");
const artworks_service_1 = require("../artworks/artworks.service");
let ScoresService = class ScoresService {
    scoreRepository;
    commentRepository;
    projectsService;
    artworksService;
    constructor(scoreRepository, commentRepository, projectsService, artworksService) {
        this.scoreRepository = scoreRepository;
        this.commentRepository = commentRepository;
        this.projectsService = projectsService;
        this.artworksService = artworksService;
    }
    async create(createScoreDto, judgeId) {
        await this.projectsService.findOne(createScoreDto.projectId);
        await this.artworksService.findOne(createScoreDto.artworkId);
        const existingScore = await this.scoreRepository.findOne({
            where: {
                artworkId: createScoreDto.artworkId,
                judgeId,
            },
        });
        if (existingScore) {
            throw new common_1.BadRequestException('您已经对此作品进行过评分');
        }
        const totalScore = this.calculateTotalScore(createScoreDto.objectivePass, createScoreDto.subjectiveScores);
        const score = this.scoreRepository.create({
            ...createScoreDto,
            judgeId,
            objectivePass: createScoreDto.objectivePass ? 1 : 0,
            totalScore,
        });
        return await this.scoreRepository.save(score);
    }
    async findByProject(projectId, options) {
        await this.validateProjectAccess(projectId, options.user);
        const { page, limit, judgeId, status } = options;
        const queryBuilder = this.scoreRepository
            .createQueryBuilder('score')
            .leftJoinAndSelect('score.artwork', 'artwork')
            .leftJoinAndSelect('score.judge', 'judge')
            .where('score.projectId = :projectId', { projectId });
        if (judgeId) {
            queryBuilder.andWhere('score.judgeId = :judgeId', { judgeId });
        }
        if (status) {
            queryBuilder.andWhere('score.status = :status', { status });
        }
        queryBuilder
            .orderBy('score.scoredAt', 'DESC')
            .skip((page - 1) * limit)
            .take(limit);
        const [scores, total] = await queryBuilder.getManyAndCount();
        return {
            data: scores,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
        };
    }
    async findByArtwork(artworkId, user) {
        const artwork = await this.artworksService.findOne(artworkId);
        await this.validateProjectAccess(artwork.projectId, user);
        const scores = await this.scoreRepository.find({
            where: { artworkId },
            relations: ['judge'],
            order: { scoredAt: 'DESC' },
        });
        return scores;
    }
    async findMyScores(projectId, judgeId, options) {
        const { page, limit } = options;
        const queryBuilder = this.scoreRepository
            .createQueryBuilder('score')
            .leftJoinAndSelect('score.artwork', 'artwork')
            .where('score.projectId = :projectId', { projectId })
            .andWhere('score.judgeId = :judgeId', { judgeId });
        queryBuilder
            .orderBy('score.scoredAt', 'DESC')
            .skip((page - 1) * limit)
            .take(limit);
        const [scores, total] = await queryBuilder.getManyAndCount();
        return {
            data: scores,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
        };
    }
    async findOne(id, user) {
        const score = await this.scoreRepository.findOne({
            where: { id },
            relations: ['artwork', 'judge', 'project'],
        });
        if (!score) {
            throw new common_1.NotFoundException('评分记录不存在');
        }
        await this.validateProjectAccess(score.projectId, user);
        return score;
    }
    async update(id, updateScoreDto, user) {
        const score = await this.findOne(id, user);
        if (score.judgeId !== user.id) {
            throw new common_1.ForbiddenException('只能修改自己的评分');
        }
        if (score.status === score_entity_1.ScoreStatus.SUBMITTED) {
            throw new common_1.BadRequestException('已提交的评分不能修改');
        }
        const totalScore = this.calculateTotalScore(updateScoreDto.objectivePass, updateScoreDto.subjectiveScores || score.subjectiveScores);
        Object.assign(score, {
            ...updateScoreDto,
            objectivePass: updateScoreDto.objectivePass !== undefined
                ? (updateScoreDto.objectivePass ? 1 : 0)
                : score.objectivePass,
            totalScore,
        });
        return await this.scoreRepository.save(score);
    }
    async submit(id, user) {
        const score = await this.findOne(id, user);
        if (score.judgeId !== user.id) {
            throw new common_1.ForbiddenException('只能提交自己的评分');
        }
        if (score.objectivePass === null || !score.subjectiveScores || !score.totalScore) {
            throw new common_1.BadRequestException('评分信息不完整，无法提交');
        }
        score.status = score_entity_1.ScoreStatus.SUBMITTED;
        return await this.scoreRepository.save(score);
    }
    async remove(id, user) {
        const score = await this.findOne(id, user);
        if (user.role !== user_entity_1.UserRole.SUPER_ADMIN &&
            user.role !== user_entity_1.UserRole.ADMIN &&
            score.judgeId !== user.id) {
            throw new common_1.ForbiddenException('无权限删除此评分');
        }
        await this.scoreRepository.remove(score);
    }
    async createComment(scoreId, createCommentDto, user) {
        const score = await this.findOne(scoreId, user);
        if (score.judgeId !== user.id) {
            throw new common_1.ForbiddenException('只能为自己的评分添加评语');
        }
        const comment = this.commentRepository.create({
            ...createCommentDto,
            scoreId,
        });
        return await this.commentRepository.save(comment);
    }
    async getComments(scoreId, user) {
        const score = await this.findOne(scoreId, user);
        return await this.commentRepository.find({
            where: { scoreId },
            order: { createdAt: 'ASC' },
        });
    }
    async updateComment(commentId, updateData, user) {
        const comment = await this.commentRepository.findOne({
            where: { id: commentId },
            relations: ['score'],
        });
        if (!comment) {
            throw new common_1.NotFoundException('评语不存在');
        }
        if (comment.score.judgeId !== user.id) {
            throw new common_1.ForbiddenException('只能修改自己的评语');
        }
        Object.assign(comment, updateData);
        return await this.commentRepository.save(comment);
    }
    async removeComment(commentId, user) {
        const comment = await this.commentRepository.findOne({
            where: { id: commentId },
            relations: ['score'],
        });
        if (!comment) {
            throw new common_1.NotFoundException('评语不存在');
        }
        if (user.role !== user_entity_1.UserRole.SUPER_ADMIN &&
            user.role !== user_entity_1.UserRole.ADMIN &&
            comment.score.judgeId !== user.id) {
            throw new common_1.ForbiddenException('无权限删除此评语');
        }
        await this.commentRepository.remove(comment);
    }
    async getProjectScoreStats(projectId, user) {
        await this.validateProjectAccess(projectId, user);
        const stats = await this.scoreRepository
            .createQueryBuilder('score')
            .select([
            'COUNT(*) as totalScores',
            'COUNT(CASE WHEN score.status = "submitted" THEN 1 END) as submittedScores',
            'AVG(score.totalScore) as averageScore',
            'MAX(score.totalScore) as maxScore',
            'MIN(score.totalScore) as minScore',
        ])
            .where('score.projectId = :projectId', { projectId })
            .getRawOne();
        return stats;
    }
    async getArtworkScoreStats(artworkId, user) {
        const artwork = await this.artworksService.findOne(artworkId);
        await this.validateProjectAccess(artwork.projectId, user);
        const stats = await this.scoreRepository
            .createQueryBuilder('score')
            .select([
            'COUNT(*) as totalScores',
            'AVG(score.totalScore) as averageScore',
            'MAX(score.totalScore) as maxScore',
            'MIN(score.totalScore) as minScore',
        ])
            .where('score.artworkId = :artworkId', { artworkId })
            .andWhere('score.status = :status', { status: score_entity_1.ScoreStatus.SUBMITTED })
            .getRawOne();
        return stats;
    }
    calculateTotalScore(objectivePass, subjectiveScores) {
        if (!objectivePass) {
            return 0;
        }
        if (!subjectiveScores) {
            return 0;
        }
        return Object.values(subjectiveScores).reduce((sum, score) => sum + score, 0);
    }
    async validateProjectAccess(projectId, user) {
        const project = await this.projectsService.findOne(projectId);
        if (user.role !== user_entity_1.UserRole.SUPER_ADMIN &&
            user.role !== user_entity_1.UserRole.ADMIN &&
            project.organizerId !== user.id) {
            if (user.role === user_entity_1.UserRole.JUDGE) {
                return;
            }
            throw new common_1.ForbiddenException('无权限访问此项目');
        }
    }
};
exports.ScoresService = ScoresService;
exports.ScoresService = ScoresService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(score_entity_1.Score)),
    __param(1, (0, typeorm_1.InjectRepository)(score_entity_1.Comment)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, projects_service_1.ProjectsService,
        artworks_service_1.ArtworksService])
], ScoresService);
//# sourceMappingURL=scores.service.js.map