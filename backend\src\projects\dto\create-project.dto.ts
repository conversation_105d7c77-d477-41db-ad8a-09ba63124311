import {
  IsString,
  IsOptional,
  IsDateString,
  IsBoolean,
  MaxLength,
  IsUrl,
} from 'class-validator';

export class CreateProjectDto {
  @IsString()
  @MaxLength(100)
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsUrl()
  coverImage?: string;

  @IsOptional()
  @IsDateString()
  collectStartTime?: string;

  @IsOptional()
  @IsDateString()
  collectEndTime?: string;

  @IsOptional()
  @IsDateString()
  judgeStartTime?: string;

  @IsOptional()
  @IsDateString()
  judgeEndTime?: string;

  @IsOptional()
  @IsDateString()
  displayStartTime?: string;

  @IsOptional()
  @IsDateString()
  displayEndTime?: string;

  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;
}
