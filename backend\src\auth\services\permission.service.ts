import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { UserGlobalRole, UserProjectRole, GlobalRole, ProjectRole } from '../../users/entities/user-roles.entity';

/**
 * 权限管理服务
 * 处理多角色权限检查和项目级权限管理
 */
@Injectable()
export class PermissionService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(UserGlobalRole)
    private globalRoleRepository: Repository<UserGlobalRole>,
    @InjectRepository(UserProjectRole)
    private projectRoleRepository: Repository<UserProjectRole>,
  ) {}

  /**
   * 检查用户是否有全局权限
   */
  async hasGlobalPermission(
    userId: string,
    resource: string,
    action: string
  ): Promise<boolean> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['globalRoles']
    });

    if (!user) return false;

    // 超级管理员拥有所有权限
    if (user.hasGlobalRole(GlobalRole.SUPER_ADMIN)) return true;

    // 检查用户的全局角色权限
    const userRoles = user.roles;
    return this.checkRolePermissions(userRoles, resource, action);
  }

  /**
   * 检查用户在特定项目中的权限
   */
  async hasProjectPermission(
    userId: string,
    projectId: string,
    permission: string
  ): Promise<boolean> {
    // 先检查全局权限
    const hasGlobal = await this.hasGlobalPermission(userId, 'project', 'manage');
    if (hasGlobal) return true;

    // 检查项目级权限
    const projectRole = await this.projectRoleRepository.findOne({
      where: {
        userId,
        projectId,
        status: 'accepted'
      }
    });

    if (!projectRole) return false;

    // 检查权限是否过期
    if (projectRole.expiresAt && new Date() > projectRole.expiresAt) {
      return false;
    }

    // 检查具体权限
    return this.checkProjectPermission(projectRole, permission);
  }

  /**
   * 为用户添加全局角色
   */
  async addGlobalRole(
    userId: string,
    role: GlobalRole,
    grantedBy: string,
    metadata?: any
  ): Promise<UserGlobalRole> {
    // 检查是否已存在
    const existing = await this.globalRoleRepository.findOne({
      where: { userId, role }
    });

    if (existing) {
      existing.isActive = true;
      existing.metadata = { ...existing.metadata, ...metadata, grantedBy, grantedAt: new Date() };
      return await this.globalRoleRepository.save(existing);
    }

    // 创建新角色
    const globalRole = this.globalRoleRepository.create({
      userId,
      role,
      metadata: {
        grantedBy,
        grantedAt: new Date(),
        ...metadata
      }
    });

    return await this.globalRoleRepository.save(globalRole);
  }

  /**
   * 移除用户的全局角色
   */
  async removeGlobalRole(userId: string, role: GlobalRole): Promise<void> {
    await this.globalRoleRepository.update(
      { userId, role },
      { isActive: false }
    );
  }

  /**
   * 邀请用户加入项目并分配角色
   */
  async inviteUserToProject(
    projectId: string,
    userId: string,
    role: ProjectRole,
    permissions: any,
    invitedBy: string,
    metadata?: any
  ): Promise<UserProjectRole> {
    // 检查是否已存在
    const existing = await this.projectRoleRepository.findOne({
      where: { userId, projectId, role }
    });

    if (existing) {
      existing.permissions = permissions;
      existing.status = 'pending';
      existing.metadata = { ...existing.metadata, ...metadata, invitedBy, invitedAt: new Date() };
      return await this.projectRoleRepository.save(existing);
    }

    // 创建新的项目角色
    const projectRole = this.projectRoleRepository.create({
      userId,
      projectId,
      role,
      permissions,
      metadata: {
        invitedBy,
        invitedAt: new Date(),
        ...metadata
      },
      status: 'pending'
    });

    return await this.projectRoleRepository.save(projectRole);
  }

  /**
   * 用户接受项目邀请
   */
  async acceptProjectInvitation(userId: string, projectId: string): Promise<void> {
    await this.projectRoleRepository.update(
      { userId, projectId, status: 'pending' },
      { 
        status: 'accepted',
        metadata: () => `JSON_SET(metadata, '$.acceptedAt', '${new Date().toISOString()}')`
      }
    );
  }

  /**
   * 更新项目角色权限
   */
  async updateProjectRolePermissions(
    userId: string,
    projectId: string,
    permissions: any
  ): Promise<void> {
    await this.projectRoleRepository.update(
      { userId, projectId },
      { permissions }
    );
  }

  /**
   * 获取用户在项目中的角色和权限
   */
  async getUserProjectRoles(userId: string, projectId: string): Promise<UserProjectRole[]> {
    return await this.projectRoleRepository.find({
      where: {
        userId,
        projectId,
        status: 'accepted'
      }
    });
  }

  /**
   * 获取项目的所有成员
   */
  async getProjectMembers(projectId: string): Promise<UserProjectRole[]> {
    return await this.projectRoleRepository.find({
      where: { projectId, status: 'accepted' },
      relations: ['user']
    });
  }

  /**
   * 检查角色权限
   */
  private checkRolePermissions(roles: string[], resource: string, action: string): boolean {
    const rolePermissions = {
      [GlobalRole.SUPER_ADMIN]: ['*:*'],
      [GlobalRole.ADMIN]: [
        'user:read', 'user:update', 'user:manage',
        'project:read', 'project:audit',
        'application:review', 'application:approve'
      ],
      [GlobalRole.ORGANIZER]: [
        'project:create', 'project:manage',
        'artwork:manage', 'user:invite'
      ],
      [GlobalRole.ARTIST]: [
        'artwork:create', 'artwork:update',
        'project:read', 'project:join'
      ],
      [GlobalRole.JUDGE]: [
        'score:create', 'score:update',
        'artwork:read', 'project:read'
      ],
      [GlobalRole.USER]: [
        'project:read', 'artwork:read'
      ]
    };

    for (const role of roles) {
      const permissions = rolePermissions[role] || [];
      
      for (const permission of permissions) {
        if (permission === '*:*') return true;
        
        const [permResource, permAction] = permission.split(':');
        if ((permResource === resource || permResource === '*') &&
            (permAction === action || permAction === '*')) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * 检查项目权限
   */
  private checkProjectPermission(projectRole: UserProjectRole, permission: string): boolean {
    const permissions = projectRole.permissions || {};
    
    // 项目所有者拥有所有权限
    if (projectRole.role === ProjectRole.PROJECT_OWNER) return true;

    // 检查具体权限
    switch (permission) {
      case 'manage':
        return permissions.canManageProject || projectRole.role === ProjectRole.PROJECT_ADMIN;
      case 'invite':
        return permissions.canInviteUsers;
      case 'artwork_manage':
        return permissions.canManageArtworks;
      case 'score':
        return permissions.canScore;
      case 'view_scores':
        return permissions.canViewScores;
      case 'export':
        return permissions.canExportData;
      case 'moderate':
        return permissions.canModerateContent;
      case 'schedule':
        return permissions.canManageSchedule;
      default:
        return permissions.customPermissions?.includes(permission) || false;
    }
  }

  /**
   * 获取默认项目角色权限
   */
  getDefaultProjectPermissions(role: ProjectRole): any {
    const defaultPermissions = {
      [ProjectRole.PROJECT_OWNER]: {
        canManageProject: true,
        canInviteUsers: true,
        canManageArtworks: true,
        canScore: false,
        canViewScores: true,
        canExportData: true,
        canModerateContent: true,
        canManageSchedule: true
      },
      [ProjectRole.PROJECT_ADMIN]: {
        canManageProject: true,
        canInviteUsers: true,
        canManageArtworks: true,
        canScore: false,
        canViewScores: true,
        canExportData: true,
        canModerateContent: true,
        canManageSchedule: true
      },
      [ProjectRole.PROJECT_JUDGE]: {
        canManageProject: false,
        canInviteUsers: false,
        canManageArtworks: false,
        canScore: true,
        canViewScores: true,
        canExportData: false,
        canModerateContent: false,
        canManageSchedule: false
      },
      [ProjectRole.PROJECT_ARTIST]: {
        canManageProject: false,
        canInviteUsers: false,
        canManageArtworks: false,
        canScore: false,
        canViewScores: false,
        canExportData: false,
        canModerateContent: false,
        canManageSchedule: false
      },
      [ProjectRole.PROJECT_VIEWER]: {
        canManageProject: false,
        canInviteUsers: false,
        canManageArtworks: false,
        canScore: false,
        canViewScores: false,
        canExportData: false,
        canModerateContent: false,
        canManageSchedule: false
      },
      [ProjectRole.PROJECT_MODERATOR]: {
        canManageProject: false,
        canInviteUsers: false,
        canManageArtworks: true,
        canScore: false,
        canViewScores: true,
        canExportData: false,
        canModerateContent: true,
        canManageSchedule: false
      }
    };

    return defaultPermissions[role] || {};
  }
}
