"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommonRoleApplicationsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const base_controller_1 = require("../../common/base/base.controller");
const auth_decorator_1 = require("../../auth/decorators/auth.decorator");
const user_decorator_1 = require("../../auth/decorators/user.decorator");
const role_management_service_1 = require("../../auth/services/role-management.service");
const project_permission_service_1 = require("../../auth/services/project-permission.service");
const role_management_dto_1 = require("../../users/dto/role-management.dto");
let CommonRoleApplicationsController = class CommonRoleApplicationsController extends base_controller_1.BaseController {
    roleManagementService;
    projectPermissionService;
    constructor(roleManagementService, projectPermissionService) {
        super();
        this.roleManagementService = roleManagementService;
        this.projectPermissionService = projectPermissionService;
    }
    async applyForGlobalRole(applyDto, userId, req) {
        try {
            const application = await this.roleManagementService.applyForGlobalRole(userId, applyDto.targetRole, applyDto.applicationData);
            return this.created({
                application,
                targetRole: applyDto.targetRole,
                status: 'pending',
                message: '您的申请已提交，请等待管理员审核',
            }, '角色申请提交成功', req);
        }
        catch (error) {
            if (error.message.includes('已经拥有此角色')) {
                return this.conflict('您已经拥有此角色', req);
            }
            if (error.message.includes('待处理的申请')) {
                return this.conflict('您已有待处理的申请，请等待审核结果', req);
            }
            throw error;
        }
    }
    async getMyApplications(userId, query, req) {
        try {
            const result = await this.roleManagementService.getRoleApplications({
                userId,
                ...query,
            });
            return this.paginated(result.data, result.total, result.page, result.limit, '我的申请列表获取成功', req);
        }
        catch (error) {
            throw error;
        }
    }
    async getMyGlobalRoles(userId, req) {
        try {
            const roles = await this.roleManagementService.getUserGlobalRoles(userId);
            return this.success({
                userId,
                roles: roles.map(role => ({
                    id: role.id,
                    role: role.role,
                    isActive: role.isActive,
                    metadata: role.metadata,
                    createdAt: role.createdAt,
                })),
                totalRoles: roles.length,
                activeRoles: roles.filter(r => r.isActive).length,
            }, '我的角色列表获取成功', req);
        }
        catch (error) {
            throw error;
        }
    }
    async acceptProjectInvitation(projectId, userId, req) {
        try {
            await this.projectPermissionService.acceptProjectInvitation(userId, projectId);
            return this.success({
                projectId,
                userId,
                status: 'accepted',
                message: '您已成功加入项目',
            }, '项目邀请接受成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在') || error.message.includes('已过期')) {
                return this.notFound('邀请不存在或已过期', req);
            }
            throw error;
        }
    }
    async declineProjectInvitation(projectId, userId, req) {
        try {
            await this.projectPermissionService.declineProjectInvitation(userId, projectId);
            return this.success({
                projectId,
                userId,
                status: 'declined',
            }, '项目邀请拒绝成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('邀请不存在', req);
            }
            throw error;
        }
    }
    async getMyProjectInvitations(userId, query, req) {
        try {
            const mockInvitations = {
                data: [],
                total: 0,
                page: query.page || 1,
                limit: query.limit || 10,
            };
            return this.paginated(mockInvitations.data, mockInvitations.total, mockInvitations.page, mockInvitations.limit, '我的项目邀请获取成功', req);
        }
        catch (error) {
            throw error;
        }
    }
    async getMyProjects(userId, query, req) {
        try {
            const mockProjects = {
                data: [],
                total: 0,
                page: query.page || 1,
                limit: query.limit || 10,
            };
            return this.paginated(mockProjects.data, mockProjects.total, mockProjects.page, mockProjects.limit, '我的项目列表获取成功', req);
        }
        catch (error) {
            throw error;
        }
    }
    async getRoleApplicationGuide(user, req) {
        try {
            const guide = {
                roles: [
                    {
                        role: 'organizer',
                        name: '办展方',
                        description: '可以创建和管理评选项目',
                        requirements: [
                            '具有合法的机构资质',
                            '有组织活动的经验',
                            '提供机构证明材料',
                        ],
                        applicationFields: [
                            'organizationName',
                            'organizationType',
                            'contactPerson',
                            'contactPhone',
                            'qualifications',
                        ],
                    },
                    {
                        role: 'judge',
                        name: '评委',
                        description: '可以对作品进行专业评审',
                        requirements: [
                            '具有相关专业背景',
                            '有评审经验',
                            '提供专业资质证明',
                        ],
                        applicationFields: [
                            'experience',
                            'qualifications',
                            'reason',
                        ],
                    },
                    {
                        role: 'artist',
                        name: '美工组/作者',
                        description: '可以参与评选活动',
                        requirements: [
                            '有作品创作能力',
                            '同意平台使用条款',
                        ],
                        applicationFields: [
                            'experience',
                            'reason',
                        ],
                    },
                ],
                currentUser: user ? {
                    id: user.id,
                    username: user.username,
                    currentRoles: user.roles,
                    canApplyFor: this.getAvailableRoles(user.roles),
                } : null,
                applicationProcess: [
                    '填写申请表单',
                    '提交相关材料',
                    '等待管理员审核',
                    '审核通过后获得角色权限',
                ],
            };
            return this.success(guide, '角色申请指南获取成功', req);
        }
        catch (error) {
            throw error;
        }
    }
    getAvailableRoles(currentRoles) {
        const allRoles = ['organizer', 'judge', 'artist'];
        return allRoles.filter(role => !currentRoles.includes(role));
    }
};
exports.CommonRoleApplicationsController = CommonRoleApplicationsController;
__decorate([
    (0, auth_decorator_1.RequiredAuth)(),
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '申请全局角色' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '申请提交成功' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '已拥有此角色或有待处理申请' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, user_decorator_1.UserId)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [role_management_dto_1.ApplyGlobalRoleDto, String, Object]),
    __metadata("design:returntype", Promise)
], CommonRoleApplicationsController.prototype, "applyForGlobalRole", null);
__decorate([
    (0, auth_decorator_1.RequiredAuth)(),
    (0, common_1.Get)('my-applications'),
    (0, swagger_1.ApiOperation)({ summary: '获取我的角色申请' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, user_decorator_1.UserId)()),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], CommonRoleApplicationsController.prototype, "getMyApplications", null);
__decorate([
    (0, auth_decorator_1.RequiredAuth)(),
    (0, common_1.Get)('my-roles'),
    (0, swagger_1.ApiOperation)({ summary: '获取我的全局角色' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, user_decorator_1.UserId)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CommonRoleApplicationsController.prototype, "getMyGlobalRoles", null);
__decorate([
    (0, auth_decorator_1.RequiredAuth)(),
    (0, common_1.Post)('project-invitations/:projectId/accept'),
    (0, swagger_1.ApiOperation)({ summary: '接受项目邀请' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '邀请接受成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '邀请不存在或已过期' }),
    __param(0, (0, common_1.Param)('projectId')),
    __param(1, (0, user_decorator_1.UserId)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], CommonRoleApplicationsController.prototype, "acceptProjectInvitation", null);
__decorate([
    (0, auth_decorator_1.RequiredAuth)(),
    (0, common_1.Post)('project-invitations/:projectId/decline'),
    (0, swagger_1.ApiOperation)({ summary: '拒绝项目邀请' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '邀请拒绝成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '邀请不存在' }),
    __param(0, (0, common_1.Param)('projectId')),
    __param(1, (0, user_decorator_1.UserId)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], CommonRoleApplicationsController.prototype, "declineProjectInvitation", null);
__decorate([
    (0, auth_decorator_1.RequiredAuth)(),
    (0, common_1.Get)('project-invitations'),
    (0, swagger_1.ApiOperation)({ summary: '获取我的项目邀请' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, user_decorator_1.UserId)()),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], CommonRoleApplicationsController.prototype, "getMyProjectInvitations", null);
__decorate([
    (0, auth_decorator_1.RequiredAuth)(),
    (0, common_1.Get)('my-projects'),
    (0, swagger_1.ApiOperation)({ summary: '获取我参与的项目' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, user_decorator_1.UserId)()),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], CommonRoleApplicationsController.prototype, "getMyProjects", null);
__decorate([
    (0, auth_decorator_1.OptionalAuth)(),
    (0, common_1.Get)('guide'),
    (0, swagger_1.ApiOperation)({ summary: '获取角色申请指南' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], CommonRoleApplicationsController.prototype, "getRoleApplicationGuide", null);
exports.CommonRoleApplicationsController = CommonRoleApplicationsController = __decorate([
    (0, swagger_1.ApiTags)('公共-角色申请'),
    (0, common_1.Controller)('common/role-applications'),
    __metadata("design:paramtypes", [role_management_service_1.RoleManagementService,
        project_permission_service_1.ProjectPermissionService])
], CommonRoleApplicationsController);
//# sourceMappingURL=common-role-applications.controller.js.map