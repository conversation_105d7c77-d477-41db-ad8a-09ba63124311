"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.JudgeAuth = exports.OrganizerAuth = exports.SuperAdminAuth = exports.AdminAuth = exports.RoleAuth = exports.SmartAuth = exports.RequiredAuth = exports.NoAuth = exports.OptionalAuth = exports.Public = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const auth_guard_1 = require("../guards/auth.guard");
const roles_guard_1 = require("../guards/roles.guard");
const user_entity_1 = require("../../users/entities/user.entity");
const Public = () => (0, common_1.SetMetadata)('isPublic', true);
exports.Public = Public;
const OptionalAuth = () => (0, common_1.SetMetadata)('isOptionalAuth', true);
exports.OptionalAuth = OptionalAuth;
const NoAuth = () => {
    return (0, common_1.applyDecorators)((0, common_1.UseGuards)(auth_guard_1.NoAuthGuard), (0, exports.Public)());
};
exports.NoAuth = NoAuth;
const RequiredAuth = (roles) => {
    const decorators = [
        (0, common_1.UseGuards)(auth_guard_1.RequiredAuthGuard),
        (0, swagger_1.ApiBearerAuth)(),
        (0, swagger_1.ApiUnauthorizedResponse)({ description: '未授权访问' }),
    ];
    if (roles && roles.length > 0) {
        decorators.push((0, common_1.UseGuards)(roles_guard_1.RolesGuard), (0, common_1.SetMetadata)('roles', roles));
    }
    return (0, common_1.applyDecorators)(...decorators);
};
exports.RequiredAuth = RequiredAuth;
const OptionalAuth = () => {
    return (0, common_1.applyDecorators)((0, common_1.UseGuards)(auth_guard_1.OptionalAuthGuard), (0, exports.OptionalAuth)(), (0, swagger_1.ApiBearerAuth)());
};
exports.OptionalAuth = OptionalAuth;
const SmartAuth = () => {
    return (0, common_1.applyDecorators)((0, common_1.UseGuards)(auth_guard_1.SmartAuthGuard), (0, swagger_1.ApiBearerAuth)());
};
exports.SmartAuth = SmartAuth;
const RoleAuth = (roles) => {
    return (0, common_1.applyDecorators)((0, common_1.UseGuards)(auth_guard_1.RequiredAuthGuard, roles_guard_1.RolesGuard), (0, common_1.SetMetadata)('roles', roles), (0, swagger_1.ApiBearerAuth)(), (0, swagger_1.ApiUnauthorizedResponse)({ description: '未授权访问' }));
};
exports.RoleAuth = RoleAuth;
const AdminAuth = () => {
    return (0, exports.RoleAuth)([user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.SUPER_ADMIN]);
};
exports.AdminAuth = AdminAuth;
const SuperAdminAuth = () => {
    return (0, exports.RoleAuth)([user_entity_1.UserRole.SUPER_ADMIN]);
};
exports.SuperAdminAuth = SuperAdminAuth;
const OrganizerAuth = () => {
    return (0, exports.RoleAuth)([user_entity_1.UserRole.ORGANIZER, user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.SUPER_ADMIN]);
};
exports.OrganizerAuth = OrganizerAuth;
const JudgeAuth = () => {
    return (0, exports.RoleAuth)([user_entity_1.UserRole.JUDGE, user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.SUPER_ADMIN]);
};
exports.JudgeAuth = JudgeAuth;
//# sourceMappingURL=auth.decorator.js.map