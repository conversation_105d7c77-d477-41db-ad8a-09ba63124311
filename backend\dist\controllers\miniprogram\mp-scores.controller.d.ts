import { BaseController } from '../../common/base/base.controller';
import { ScoresService } from '../../scores/scores.service';
import { CreateScoreDto } from '../../scores/dto/create-score.dto';
import { UpdateScoreDto } from '../../scores/dto/update-score.dto';
import { CreateCommentDto } from '../../scores/dto/create-comment.dto';
export declare class MpScoresController extends BaseController {
    private readonly scoresService;
    constructor(scoresService: ScoresService);
    create(createScoreDto: CreateScoreDto, req: any): Promise<import("../../common/base/base.controller").ApiResponse<import("../../scores/entities/score.entity").Score> | import("../../common/base/base.controller").ApiErrorResponse>;
    findMyScores(projectId: string, query: any, req: any): Promise<import("../../common/base/base.controller").PaginatedResponse<unknown>>;
    findOne(id: string, req: any): Promise<import("../../common/base/base.controller").ApiResponse<import("../../scores/entities/score.entity").Score> | import("../../common/base/base.controller").ApiErrorResponse>;
    update(id: string, updateScoreDto: UpdateScoreDto, req: any): Promise<import("../../common/base/base.controller").ApiResponse<import("../../scores/entities/score.entity").Score> | import("../../common/base/base.controller").ApiErrorResponse>;
    submit(id: string, req: any): Promise<import("../../common/base/base.controller").ApiResponse<import("../../scores/entities/score.entity").Score> | import("../../common/base/base.controller").ApiErrorResponse>;
    remove(id: string, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<null>>;
    createComment(scoreId: string, createCommentDto: CreateCommentDto, audio: Express.Multer.File, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<import("../../scores/entities/score.entity").Comment>>;
    getComments(scoreId: string, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<import("../../scores/entities/score.entity").Comment[]>>;
    updateComment(commentId: string, updateData: Partial<CreateCommentDto>, audio: Express.Multer.File, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<import("../../scores/entities/score.entity").Comment>>;
    removeComment(commentId: string, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<null>>;
    getScoreProgress(projectId: string, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<any>>;
    batchSave(scores: CreateScoreDto[], req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<any>>;
}
