@startuml 系统功能模块图
skinparam backgroundColor white
skinparam handwritten false
skinparam defaultFontName Microsoft YaHei

' 系统功能模块图
title 书画作品评选打分管理系统 - 功能模块图

package "书画作品评选打分管理系统" {
  [项目管理] as PM
  [作品管理] as AM
  [评分系统] as SS
  [统计分析] as SA
  [创新功能] as IF
}

package "项目管理" {
  [项目创建] as PC
  [评选规则] as PR
  [评委管理] as JM
  [状态流转] as SF
}

package "作品管理" {
  [作品上传] as AU
  [作品展示] as AD
  [信息管理] as IM
}

package "评分系统" {
  [评分功能] as SF
  [评语系统] as CS
}

package "统计分析" {
  [成绩统计] as ST
  [数据分析] as DA
}

package "创新功能" {
  [AI辅助] as AI
  [社交互动] as SI
  [直播功能] as LF
  [增强展示] as ED
}

PM --> PC
PM --> PR
PM --> JM
PM --> SF

AM --> AU
AM --> AD
AM --> IM

SS --> SF
SS --> CS

SA --> ST
SA --> DA

IF --> AI
IF --> SI
IF --> LF
IF --> ED

@enduml

@startuml 用户角色权限图
skinparam backgroundColor white
skinparam handwritten false
skinparam defaultFontName Microsoft YaHei

title 书画作品评选打分管理系统 - 用户角色权限图

actor "超级管理员" as Admin
actor "项目发起人" as Initiator
actor "美工组成员" as Artist
actor "评委组成员" as Judge
actor "普通用户" as User

rectangle "系统权限" {
  usecase "系统配置管理" as UC1
  usecase "用户权限管理" as UC2
  usecase "数据备份恢复" as UC3
  usecase "项目创建管理" as UC4
  usecase "评委邀请管理" as UC5
  usecase "作品上传管理" as UC6
  usecase "评分操作" as UC7
  usecase "项目浏览" as UC8
}

Admin --> UC1
Admin --> UC2
Admin --> UC3

Initiator --> UC4
Initiator --> UC5

Artist --> UC6

Judge --> UC7

User --> UC8

@enduml

@startuml 数据流转流程图
skinparam backgroundColor white
skinparam handwritten false
skinparam defaultFontName Microsoft YaHei

title 书画作品评选打分管理系统 - 数据流转流程图

participant "项目发起人" as Initiator
participant "美工组" as Artist
participant "评委组" as Judge
participant "系统" as System
participant "普通用户" as User

Initiator -> System: 创建项目
Initiator -> System: 设置评选规则
Initiator -> Artist: 邀请美工组
Initiator -> Judge: 邀请评委组

Artist -> System: 上传作品信息
Artist -> System: 上传作品图片
System -> System: 自动匹配信息

System -> Judge: 通知开始评分
Judge -> System: 进行评分
Judge -> System: 提交评语

System -> System: 统计分析
System -> Initiator: 生成统计报告

Initiator -> System: 确认发布结果
System -> User: 展示评选结果

User -> System: 查看/互动
System -> Initiator: 反馈数据分析

@enduml

@startuml 状态转换图
skinparam backgroundColor white
skinparam handwritten false
skinparam defaultFontName Microsoft YaHei

title 书画作品评选打分管理系统 - 状态转换图

[*] --> 筹备中
筹备中 --> 正在筹稿: 完成项目创建
正在筹稿 --> 作品统计: 截止收稿
作品统计 --> 正在评选: 开始评分
正在评选 --> 评选查看: 评分完成
评选查看 --> 电子展厅: 结果确认
电子展厅 --> [*]: 项目结束

state 筹备中 {
  [*] --> 创建项目
  创建项目 --> 设置规则
  设置规则 --> 邀请成员
}

state 正在筹稿 {
  [*] --> 收集作品
  收集作品 --> 整理信息
}

state 作品统计 {
  [*] --> 数据核验
  数据核验 --> 生成报表
}

state 正在评选 {
  [*] --> 评委打分
  评委打分 --> 评语录入
}

state 评选查看 {
  [*] --> 结果公示
  结果公示 --> 数据导出
}

state 电子展厅 {
  [*] --> 作品展示
  作品展示 --> 互动反馈
}

@enduml 