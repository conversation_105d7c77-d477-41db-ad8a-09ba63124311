import type * as webpack from 'webpack';
import type { ForkTsCheckerWebpackPluginState } from '../plugin-state';
import type { RpcWorker } from '../rpc';
declare function tapStopToTerminateWorkers(compiler: webpack.Compiler, getIssuesWorker: Rp<PERSON><PERSON><PERSON><PERSON>, getDependenciesWorker: Rpc<PERSON>or<PERSON>, state: ForkTsCheckerWebpackPluginState): void;
export { tapStopToTerminateWorkers };
