# 书画作品评选系统 API 文档

## 文档概述

本系统提供统一的 RESTful API 服务，支持三个不同的前端应用。API 文档按照使用端进行分类，便于不同角色的开发者查阅。

## 文档结构

### 📱 [小程序端 API](./miniprogram-api.md)
**适用对象**: 评委、普通用户
**主要功能**:
- 微信登录认证
- 项目浏览和查看
- 作品浏览和详情
- 评分功能（评委专用）
- 评语录入（文字、语音、标注）
- 个人评分记录查看

**核心接口**:
- `POST /auth/wechat-login` - 微信登录
- `GET /projects` - 获取项目列表
- `GET /artworks/project/{id}` - 获取项目作品
- `POST /scores` - 创建评分
- `POST /scores/{id}/comments` - 添加评语

### 🏢 [办展方管理端 API](./organizer-api.md)
**适用对象**: 办展方、美工组
**主要功能**:
- 用户名密码登录
- 项目全生命周期管理
- 作品批量上传和管理
- Excel 导入作品信息
- 评分结果查看和统计
- 项目数据导出

**核心接口**:
- `POST /auth/login` - 用户登录
- `POST /projects` - 创建项目
- `POST /artworks/batch-upload` - 批量上传作品
- `POST /artworks/import-excel` - Excel 导入
- `GET /scores/project/{id}` - 查看评分结果

### ⚙️ [系统管理端 API](./admin-api.md)
**适用对象**: 超级管理员、系统管理员
**主要功能**:
- 用户管理和权限控制
- 办展方申请审批
- 系统配置管理
- 数据统计和分析
- 系统维护和监控
- 数据导出和备份

**核心接口**:
- `GET /users` - 用户管理
- `PATCH /applications/{id}/review` - 申请审批
- `GET /stats/overview` - 系统统计
- `POST /system/backup` - 数据备份

## 通用规范

### 基础信息
- **Base URL**: `https://api.artscore.com/api/v1`
- **认证方式**: Bearer Token (JWT)
- **数据格式**: JSON
- **字符编码**: UTF-8

### 认证机制
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应格式
```json
{
  "success": true,
  "data": {},
  "message": "操作成功"
}
```

### 分页格式
```json
{
  "success": true,
  "data": {
    "items": [],
    "total": 100,
    "page": 1,
    "limit": 10,
    "totalPages": 10
  }
}
```

### 错误格式
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {}
  }
}
```

## 权限说明

### 用户角色
| 角色 | 说明 | 权限范围 |
|------|------|----------|
| super_admin | 超级管理员 | 所有权限 |
| admin | 管理员 | 用户管理、申请审批、项目监管 |
| organizer | 办展方 | 项目管理、作品管理 |
| artist | 美工组 | 作品上传和编辑 |
| judge | 评委 | 作品评分和评语 |
| user | 普通用户 | 项目浏览、作品查看 |

### 权限控制
- 使用 JWT Token 进行身份认证
- 基于角色的访问控制 (RBAC)
- 资源级权限验证（如项目创建者权限）
- API 接口级权限控制

## 接口分类

### 认证相关
- 用户登录/注册
- 微信登录
- Token 刷新
- 用户信息获取

### 用户管理
- 用户 CRUD 操作
- 角色和状态管理
- 权限控制

### 项目管理
- 项目生命周期管理
- 状态流转控制
- 二维码生成

### 作品管理
- 单个/批量上传
- Excel 导入
- 状态审核
- 信息管理

### 评分系统
- 评分录入和修改
- 评语管理（文字、语音、标注）
- 统计分析

### 申请审批
- 申请提交
- 审批流程
- 状态管理

### 文件管理
- 图片上传和压缩
- 文档上传
- 缩略图生成
- 文件服务

### 系统管理
- 系统配置
- 数据统计
- 日志管理
- 数据导出

## 开发指南

### 环境配置
1. 开发环境：`https://dev-api.artscore.com/api/v1`
2. 测试环境：`https://test-api.artscore.com/api/v1`
3. 生产环境：`https://api.artscore.com/api/v1`

### 调试工具
- **Postman**: 导入 API 集合进行测试
- **Swagger**: 访问 `/api` 查看交互式文档
- **日志**: 查看详细的请求和响应日志

### 错误处理
- 统一的错误码和错误信息
- 详细的错误描述和解决建议
- 支持国际化的错误信息

### 性能优化
- 分页查询减少数据传输
- 图片压缩和缩略图
- 缓存机制提升响应速度
- 请求限流防止滥用

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 完整的用户认证系统
- 项目和作品管理功能
- 评分系统实现
- 申请审批流程

### 后续版本规划
- v1.1.0: 增加实时通知功能
- v1.2.0: 支持多语言国际化
- v1.3.0: 增加数据分析和报表功能
- v2.0.0: 微服务架构重构

## 联系方式

- **技术支持**: <EMAIL>
- **API 问题**: <EMAIL>
- **文档反馈**: <EMAIL>

## 许可证

本 API 文档遵循 MIT 许可证。
