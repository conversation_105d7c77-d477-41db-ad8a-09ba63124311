# 书画作品评选系统架构设计

## 系统概述

本系统是一个多端协作的书画作品评选管理平台，包含5个不同的前端应用和1个统一的后端API服务。

## 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        前端应用层                              │
├─────────────────────────────────────────────────────────────┤
│  网站首页    │  办展方后台  │  系统管理后台  │  小程序端  │  H5端  │
│  (Vue3)     │  (Vue3)     │   (Vue3)     │ (uni-app) │(uni-app)│
│             │             │              │           │        │
│ - 平台介绍   │ - 项目管理   │ - 用户管理    │ - 项目浏览 │- 移动端 │
│ - 申请入驻   │ - 作品管理   │ - 申请审批    │ - 作品评分 │- 适配   │
│ - 项目展示   │ - 数据统计   │ - 系统配置    │ - 评语录入 │        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                        API网关层                              │
├─────────────────────────────────────────────────────────────┤
│  认证中间件  │  权限控制  │  请求限流  │  日志记录  │  错误处理  │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                        业务逻辑层                              │
├─────────────────────────────────────────────────────────────┤
│  用户管理    │  项目管理   │  作品管理   │  评分系统   │  统计分析  │
│             │            │            │            │           │
│ - 注册登录   │ - 项目创建  │ - 作品上传  │ - 评分录入  │ - 成绩统计 │
│ - 权限管理   │ - 状态流转  │ - 信息管理  │ - 评语管理  │ - 数据分析 │
│ - 申请审批   │ - 团队管理  │ - 展示管理  │ - 结果计算  │ - 报表导出 │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                        数据访问层                              │
├─────────────────────────────────────────────────────────────┤
│    MySQL数据库    │    Redis缓存    │    文件存储服务         │
│                  │                │                        │
│ - 用户数据        │ - 会话缓存      │ - 图片存储              │
│ - 项目数据        │ - 热点数据      │ - 文档存储              │
│ - 作品数据        │ - 计算结果      │ - 备份存储              │
│ - 评分数据        │                │                        │
└─────────────────────────────────────────────────────────────┘
```

## 用户角色与权限

### 1. 超级管理员 (super_admin)
- **访问端**: 系统管理后台
- **主要功能**:
  - 办展方申请审批
  - 用户权限管理
  - 系统配置管理
  - 数据备份恢复
  - 系统监控

### 2. 办展方 (organizer)
- **访问端**: 办展方管理后台
- **主要功能**:
  - 项目创建与管理
  - 评委邀请管理
  - 作品审核管理
  - 评选结果发布
  - 数据统计分析

### 3. 美工组 (artist)
- **访问端**: 办展方管理后台
- **主要功能**:
  - 作品批量上传
  - 作品信息编辑
  - 展示效果调整

### 4. 评委 (judge)
- **访问端**: 小程序端、H5端
- **主要功能**:
  - 作品评分
  - 评语录入
  - 评分修改（限时）

### 5. 普通用户 (user)
- **访问端**: 小程序端、H5端、网站首页
- **主要功能**:
  - 项目浏览
  - 作品查看
  - 评论互动
  - 申请成为办展方

## 技术选型详解

### 后端技术栈
- **框架**: Node.js + Express.js
- **数据库**: MySQL 8.0 + Sequelize ORM
- **缓存**: Redis 6.0
- **认证**: JWT + 微信OAuth
- **文件上传**: Multer + 云存储（阿里云OSS/腾讯云COS）
- **API文档**: Swagger
- **日志**: Winston
- **进程管理**: PM2

### 前端技术栈
- **小程序端**: uni-app + Vue 3 + uView UI
- **网站首页**: Vue 3 + Vite + Element Plus
- **管理后台**: Vue 3 + Vite + Element Plus
- **状态管理**: Pinia
- **HTTP客户端**: Axios
- **构建工具**: Vite
- **CSS预处理**: Sass

### 开发工具
- **版本控制**: Git + GitLab/GitHub
- **API测试**: Postman
- **数据库管理**: MySQL Workbench
- **代码编辑器**: VS Code
- **容器化**: Docker + Docker Compose

## 部署架构

### 开发环境
```
开发机 → Git仓库 → 本地Docker容器
```

### 生产环境
```
负载均衡器 → Web服务器集群 → 数据库集群
    │              │              │
   Nginx        Node.js应用     MySQL主从
                    │              │
                Redis集群      文件存储服务
```

## 安全策略

### 1. 认证安全
- JWT Token过期机制
- 微信登录安全验证
- 密码加密存储（bcrypt）

### 2. 权限控制
- 基于角色的访问控制（RBAC）
- API接口权限验证
- 前端路由权限控制

### 3. 数据安全
- SQL注入防护
- XSS攻击防护
- CSRF攻击防护
- 敏感数据加密

### 4. 网络安全
- HTTPS强制加密
- API请求限流
- IP白名单机制

## 性能优化

### 1. 数据库优化
- 索引优化
- 查询优化
- 读写分离
- 连接池管理

### 2. 缓存策略
- Redis热点数据缓存
- 静态资源CDN加速
- 浏览器缓存策略

### 3. 前端优化
- 代码分割
- 懒加载
- 图片压缩
- 资源预加载

## 监控与运维

### 1. 系统监控
- 服务器性能监控
- 应用性能监控
- 数据库性能监控
- 错误日志监控

### 2. 业务监控
- 用户行为分析
- 接口调用统计
- 业务指标监控

### 3. 运维自动化
- 自动化部署
- 自动化测试
- 自动化备份
- 故障自动恢复

## 扩展性设计

### 1. 水平扩展
- 微服务架构预留
- 数据库分库分表
- 缓存集群扩展

### 2. 功能扩展
- 插件化架构
- 配置化功能开关
- API版本管理

### 3. 多租户支持
- 数据隔离
- 权限隔离
- 资源隔离
