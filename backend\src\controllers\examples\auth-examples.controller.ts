import {
  Controller,
  Get,
  Post,
  Body,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { BaseController } from '../../common/base/base.controller';

// 导入认证装饰器
import {
  NoAuth,
  RequiredAuth,
  OptionalAuth,
  AdminAuth,
  SuperAdminAuth,
  OrganizerAuth,
  JudgeAuth,
  RoleAuth,
} from '../../auth/decorators/auth.decorator';

// 导入用户装饰器
import {
  CurrentUser,
  UserId,
  UserRole,
  IsAdmin,
  IsSuperAdmin,
  IsAuthenticated,
  SafeUser,
  ClientIP,
  UserAgent,
} from '../../auth/decorators/user.decorator';

import { User, UserRole as UserRoleEnum } from '../../users/entities/user.entity';

/**
 * 认证守卫使用示例控制器
 * 演示三种认证守卫的使用方法
 */
@ApiTags('认证示例')
@Controller('examples/auth')
export class AuthExamplesController extends BaseController {

  /**
   * 示例1：不需要认证的公开接口
   * 任何人都可以访问，不需要Token
   */
  @NoAuth()
  @Get('public')
  @ApiOperation({ summary: '公开接口示例' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async publicEndpoint(@Request() req) {
    return this.success(
      {
        message: '这是一个公开接口，任何人都可以访问',
        timestamp: new Date().toISOString(),
        hasUser: !!req.user, // 应该始终为false
      },
      '公开数据获取成功',
      req
    );
  }

  /**
   * 示例2：必须认证的接口
   * 必须提供有效Token才能访问
   */
  @RequiredAuth()
  @Get('protected')
  @ApiOperation({ summary: '受保护接口示例' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 401, description: '未授权' })
  async protectedEndpoint(
    @CurrentUser() user: User,
    @UserId() userId: string,
    @UserRole() userRole: string,
    @Request() req
  ) {
    return this.success(
      {
        message: '这是一个受保护的接口，需要登录才能访问',
        user: {
          id: user.id,
          username: user.username,
          role: user.role,
        },
        userId, // 通过装饰器直接获取用户ID
        userRole, // 通过装饰器直接获取用户角色
      },
      '受保护数据获取成功',
      req
    );
  }

  /**
   * 示例3：可选认证的接口
   * 可以不登录访问，但登录后会有更多信息
   */
  @OptionalAuth()
  @Get('optional')
  @ApiOperation({ summary: '可选认证接口示例' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async optionalEndpoint(
    @CurrentUser() user: User | null,
    @IsAuthenticated() isAuth: boolean,
    @SafeUser() safeUser: any,
    @ClientIP() clientIP: string,
    @UserAgent() userAgent: string,
    @Request() req
  ) {
    if (user) {
      // 已登录用户的逻辑
      return this.success(
        {
          message: '欢迎回来！这里是为登录用户定制的内容',
          user: {
            id: user.id,
            username: user.username,
            nickname: user.nickname,
            role: user.role,
          },
          safeUser, // 安全的用户信息（已过滤敏感字段）
          isAuthenticated: isAuth,
          personalizedContent: [
            '个性化推荐1',
            '个性化推荐2',
            '个性化推荐3',
          ],
          clientInfo: {
            ip: clientIP,
            userAgent,
          },
        },
        '个性化内容获取成功',
        req
      );
    } else {
      // 未登录用户的逻辑
      return this.success(
        {
          message: '这是公开内容，登录后可以看到更多个性化内容',
          isAuthenticated: isAuth,
          publicContent: [
            '公开内容1',
            '公开内容2',
            '公开内容3',
          ],
          loginTip: '登录后可以获得个性化推荐',
          clientInfo: {
            ip: clientIP,
            userAgent,
          },
        },
        '公开内容获取成功',
        req
      );
    }
  }

  /**
   * 示例4：管理员专用接口
   */
  @AdminAuth()
  @Get('admin-only')
  @ApiOperation({ summary: '管理员专用接口' })
  async adminOnlyEndpoint(
    @CurrentUser() user: User,
    @IsAdmin() isAdmin: boolean,
    @Request() req
  ) {
    return this.success(
      {
        message: '这是管理员专用接口',
        adminUser: {
          id: user.id,
          username: user.username,
          role: user.role,
        },
        isAdmin,
        adminFeatures: [
          '用户管理',
          '系统配置',
          '数据统计',
        ],
      },
      '管理员数据获取成功',
      req
    );
  }

  /**
   * 示例5：超级管理员专用接口
   */
  @SuperAdminAuth()
  @Get('super-admin-only')
  @ApiOperation({ summary: '超级管理员专用接口' })
  async superAdminOnlyEndpoint(
    @CurrentUser() user: User,
    @IsSuperAdmin() isSuperAdmin: boolean,
    @Request() req
  ) {
    return this.success(
      {
        message: '这是超级管理员专用接口',
        superAdminUser: {
          id: user.id,
          username: user.username,
          role: user.role,
        },
        isSuperAdmin,
        superAdminFeatures: [
          '系统维护',
          '危险操作',
          '全局配置',
        ],
      },
      '超级管理员数据获取成功',
      req
    );
  }

  /**
   * 示例6：办展方专用接口
   */
  @OrganizerAuth()
  @Get('organizer-only')
  @ApiOperation({ summary: '办展方专用接口' })
  async organizerOnlyEndpoint(@CurrentUser() user: User, @Request() req) {
    return this.success(
      {
        message: '这是办展方专用接口',
        organizerUser: {
          id: user.id,
          username: user.username,
          role: user.role,
        },
        organizerFeatures: [
          '项目管理',
          '作品管理',
          '评分查看',
        ],
      },
      '办展方数据获取成功',
      req
    );
  }

  /**
   * 示例7：评委专用接口
   */
  @JudgeAuth()
  @Get('judge-only')
  @ApiOperation({ summary: '评委专用接口' })
  async judgeOnlyEndpoint(@CurrentUser() user: User, @Request() req) {
    return this.success(
      {
        message: '这是评委专用接口',
        judgeUser: {
          id: user.id,
          username: user.username,
          role: user.role,
        },
        judgeFeatures: [
          '作品评分',
          '评语录入',
          '评分历史',
        ],
      },
      '评委数据获取成功',
      req
    );
  }

  /**
   * 示例8：自定义角色权限接口
   */
  @RoleAuth([UserRoleEnum.ORGANIZER, UserRoleEnum.ARTIST])
  @Get('custom-roles')
  @ApiOperation({ summary: '自定义角色权限接口' })
  async customRolesEndpoint(@CurrentUser() user: User, @Request() req) {
    return this.success(
      {
        message: '这个接口只允许办展方和美工组访问',
        user: {
          id: user.id,
          username: user.username,
          role: user.role,
        },
        allowedRoles: ['organizer', 'artist'],
      },
      '自定义角色数据获取成功',
      req
    );
  }

  /**
   * 示例9：POST请求的认证示例
   */
  @RequiredAuth()
  @Post('create-something')
  @ApiOperation({ summary: 'POST请求认证示例' })
  async createSomething(
    @Body() data: any,
    @CurrentUser() user: User,
    @UserId() userId: string,
    @Request() req
  ) {
    return this.success(
      {
        message: '数据创建成功',
        createdBy: {
          id: user.id,
          username: user.username,
        },
        userId,
        data,
        createdAt: new Date().toISOString(),
      },
      '数据创建成功',
      req
    );
  }

  /**
   * 示例10：错误处理示例
   */
  @RequiredAuth()
  @Get('error-example')
  @ApiOperation({ summary: '错误处理示例' })
  async errorExample(@CurrentUser() user: User, @Request() req) {
    // 模拟业务逻辑错误
    if (user.role === 'user') {
      return this.forbidden('普通用户无权访问此功能', req);
    }

    return this.success(
      {
        message: '这是一个可能出错的接口',
        user: {
          id: user.id,
          role: user.role,
        },
      },
      '操作成功',
      req
    );
  }
}
