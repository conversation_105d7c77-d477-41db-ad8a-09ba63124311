{"name": "has-own-prop", "version": "2.0.0", "description": "A safer `.hasOwnProperty()`", "license": "MIT", "repository": "sindresorhus/has-own-prop", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["object", "has", "own", "property"], "devDependencies": {"ava": "^2.1.0", "tsd": "^0.7.3", "xo": "^0.24.0"}}