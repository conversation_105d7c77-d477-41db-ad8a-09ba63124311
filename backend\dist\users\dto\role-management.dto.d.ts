import { GlobalRole, ProjectRole } from '../entities/user-roles.entity';
export declare class ApplyGlobalRoleDto {
    targetRole: GlobalRole;
    applicationData: {
        organizationName?: string;
        organizationType?: string;
        contactPerson?: string;
        contactPhone?: string;
        qualifications?: string[];
        experience?: string;
        reason?: string;
        attachments?: string[];
    };
}
export declare class ReviewRoleApplicationDto {
    approved: boolean;
    reviewComment?: string;
}
export declare class AssignGlobalRoleDto {
    userId: string;
    role: GlobalRole;
    metadata?: {
        reason?: string;
        expiresAt?: Date;
        certifications?: string[];
    };
}
export declare class BatchAssignRolesDto {
    assignments: AssignGlobalRoleDto[];
}
export declare class InviteUserToProjectDto {
    userId: string;
    role: ProjectRole;
    permissions?: {
        canManageProject?: boolean;
        canInviteUsers?: boolean;
        canManageArtworks?: boolean;
        canScore?: boolean;
        canViewScores?: boolean;
        canExportData?: boolean;
        canModerateContent?: boolean;
        canManageSchedule?: boolean;
        customPermissions?: string[];
    };
    message?: string;
    roleDescription?: string;
    responsibilities?: string[];
}
export declare class BatchInviteUsersDto {
    invitations: InviteUserToProjectDto[];
}
export declare class UpdateProjectPermissionsDto {
    permissions: {
        canManageProject?: boolean;
        canInviteUsers?: boolean;
        canManageArtworks?: boolean;
        canScore?: boolean;
        canViewScores?: boolean;
        canExportData?: boolean;
        canModerateContent?: boolean;
        canManageSchedule?: boolean;
        customPermissions?: string[];
    };
}
export declare class RemoveProjectMemberDto {
    reason?: string;
}
export declare class CreatePermissionTemplateDto {
    name: string;
    role: ProjectRole;
    permissions: {
        canManageProject?: boolean;
        canInviteUsers?: boolean;
        canManageArtworks?: boolean;
        canScore?: boolean;
        canViewScores?: boolean;
        canExportData?: boolean;
        canModerateContent?: boolean;
        canManageSchedule?: boolean;
        customPermissions?: string[];
    };
    description?: string;
    isDefault?: boolean;
}
export declare class QueryRoleApplicationsDto {
    status?: string;
    targetRole?: GlobalRole;
    userId?: string;
    page?: number;
    limit?: number;
}
export declare class QueryProjectMembersDto {
    role?: ProjectRole;
    status?: string;
    page?: number;
    limit?: number;
}
