import { BaseController } from '../../common/base/base.controller';
import { User } from '../../users/entities/user.entity';
import { ProjectPermissionService } from '../../auth/services/project-permission.service';
import { RoleManagementService } from '../../auth/services/role-management.service';
import { InviteUserToProjectDto, BatchInviteUsersDto, UpdateProjectPermissionsDto, RemoveProjectMemberDto, QueryProjectMembersDto } from '../../users/dto/role-management.dto';
export declare class OrganizerProjectPermissionsController extends BaseController {
    private readonly projectPermissionService;
    private readonly roleManagementService;
    constructor(projectPermissionService: ProjectPermissionService, roleManagementService: RoleManagementService);
    inviteUser(projectId: string, inviteDto: InviteUserToProjectDto, invitedBy: string, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        invitation: import("../../users/entities/user-roles.entity").UserProjectRole;
        projectId: string;
        invitedRole: import("../../users/entities/user-roles.entity").ProjectRole;
        permissions: any;
    }> | import("../../common/base/base.controller").ApiErrorResponse>;
    batchInviteUsers(projectId: string, batchDto: BatchInviteUsersDto, invitedBy: string, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        successful: number;
        total: number;
        results: import("../../users/entities/user-roles.entity").UserProjectRole[];
        projectId: string;
    }>>;
    getProjectMembers(projectId: string, query: QueryProjectMembersDto, permissions: any, req: any): Promise<import("../../common/base/base.controller").PaginatedResponse<{
        id: string;
        userId: string;
        role: import("../../users/entities/user-roles.entity").ProjectRole;
        status: "pending" | "accepted" | "declined" | "removed";
        user: {
            id: string;
            username: string;
            nickname: string;
            realName: string;
            avatar: any;
        };
        permissions: {
            canManageProject?: boolean;
            canInviteUsers?: boolean;
            canManageArtworks?: boolean;
            canScore?: boolean;
            canViewScores?: boolean;
            canExportData?: boolean;
            canModerateContent?: boolean;
            canManageSchedule?: boolean;
            customPermissions?: string[];
        } | undefined;
        metadata: {
            invitedAt: Date | undefined;
            acceptedAt: Date | undefined;
            roleDescription: any;
        };
        createdAt: Date;
    }>>;
    updateMemberPermissions(projectId: string, targetUserId: string, updateDto: UpdateProjectPermissionsDto, updatedBy: string, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<{
        projectId: string;
        targetUserId: string;
        newPermissions: {
            canManageProject?: boolean;
            canInviteUsers?: boolean;
            canManageArtworks?: boolean;
            canScore?: boolean;
            canViewScores?: boolean;
            canExportData?: boolean;
            canModerateContent?: boolean;
            canManageSchedule?: boolean;
            customPermissions?: string[];
        };
        updatedBy: string;
    }>>;
    removeProjectMember(projectId: string, targetUserId: string, removeDto: RemoveProjectMemberDto, removedBy: string, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<{
        projectId: string;
        removedUserId: string;
        removedBy: string;
        reason: string | undefined;
    }>>;
    getMemberDetails(projectId: string, userId: string, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<{
        userId: string;
        projectId: string;
        roles: {
            id: string;
            role: import("../../users/entities/user-roles.entity").ProjectRole;
            permissions: {
                canManageProject?: boolean;
                canInviteUsers?: boolean;
                canManageArtworks?: boolean;
                canScore?: boolean;
                canViewScores?: boolean;
                canExportData?: boolean;
                canModerateContent?: boolean;
                canManageSchedule?: boolean;
                customPermissions?: string[];
            };
            status: "pending" | "accepted" | "declined" | "removed";
            metadata: {
                invitedBy?: string;
                invitedAt?: Date;
                acceptedAt?: Date;
                role_description?: string;
                responsibilities?: string[];
            };
            createdAt: Date;
        }[];
        mergedPermissions: any;
        totalRoles: number;
    }>>;
    getProjectPermissionStats(projectId: string, req: any): Promise<import("../../common/base/base.controller").ApiResponse<any>>;
    getPermissionTemplates(role: string, req: any): Promise<import("../../common/base/base.controller").ApiResponse<import("../../users/entities/user-roles.entity").PermissionTemplate[]>>;
    getMyPermissions(projectId: string, user: User, roles: string[], permissions: any, isManager: boolean, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        projectId: string;
        user: {
            id: string;
            username: string;
            globalRoles: string[];
        };
        projectRoles: string[];
        permissions: any;
        isManager: boolean;
        capabilities: {
            canInviteUsers: any;
            canManageMembers: any;
            canViewAllPermissions: any;
            canExportData: any;
        };
    }>>;
}
