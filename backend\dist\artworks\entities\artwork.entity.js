"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Artwork = exports.ArtworkStatus = void 0;
const typeorm_1 = require("typeorm");
const project_entity_1 = require("../../projects/entities/project.entity");
var ArtworkStatus;
(function (ArtworkStatus) {
    ArtworkStatus["UPLOADED"] = "uploaded";
    ArtworkStatus["REVIEWING"] = "reviewing";
    ArtworkStatus["APPROVED"] = "approved";
    ArtworkStatus["REJECTED"] = "rejected";
})(ArtworkStatus || (exports.ArtworkStatus = ArtworkStatus = {}));
let Artwork = class Artwork {
    id;
    projectId;
    artworkNo;
    title;
    description;
    size;
    technique;
    creationYear;
    imageUrl;
    thumbnailUrl;
    authorName;
    authorPhone;
    authorAddress;
    authorBio;
    tags;
    status;
    uploadTime;
    reviewTime;
    createdAt;
    updatedAt;
    project;
};
exports.Artwork = Artwork;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Artwork.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'project_id' }),
    __metadata("design:type", String)
], Artwork.prototype, "projectId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'artwork_no', length: 50 }),
    __metadata("design:type", String)
], Artwork.prototype, "artworkNo", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 200 }),
    __metadata("design:type", String)
], Artwork.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Artwork.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, nullable: true }),
    __metadata("design:type", String)
], Artwork.prototype, "size", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], Artwork.prototype, "technique", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'creation_year', type: 'year', nullable: true }),
    __metadata("design:type", Number)
], Artwork.prototype, "creationYear", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'image_url' }),
    __metadata("design:type", String)
], Artwork.prototype, "imageUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'thumbnail_url', nullable: true }),
    __metadata("design:type", String)
], Artwork.prototype, "thumbnailUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'author_name', length: 50 }),
    __metadata("design:type", String)
], Artwork.prototype, "authorName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'author_phone', length: 20, nullable: true }),
    __metadata("design:type", String)
], Artwork.prototype, "authorPhone", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'author_address', type: 'text', nullable: true }),
    __metadata("design:type", String)
], Artwork.prototype, "authorAddress", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'author_bio', type: 'text', nullable: true }),
    __metadata("design:type", String)
], Artwork.prototype, "authorBio", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Artwork.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ArtworkStatus,
        default: ArtworkStatus.UPLOADED,
    }),
    __metadata("design:type", String)
], Artwork.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'upload_time', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' }),
    __metadata("design:type", Date)
], Artwork.prototype, "uploadTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'review_time', type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], Artwork.prototype, "reviewTime", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], Artwork.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], Artwork.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => project_entity_1.Project),
    (0, typeorm_1.JoinColumn)({ name: 'project_id' }),
    __metadata("design:type", project_entity_1.Project)
], Artwork.prototype, "project", void 0);
exports.Artwork = Artwork = __decorate([
    (0, typeorm_1.Entity)('artworks')
], Artwork);
//# sourceMappingURL=artwork.entity.js.map