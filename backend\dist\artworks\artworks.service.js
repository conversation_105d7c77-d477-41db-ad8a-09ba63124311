"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArtworksService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const artwork_entity_1 = require("./entities/artwork.entity");
const user_entity_1 = require("../users/entities/user.entity");
const projects_service_1 = require("../projects/projects.service");
const files_service_1 = require("../files/files.service");
const XLSX = require("xlsx");
let ArtworksService = class ArtworksService {
    artworkRepository;
    projectsService;
    filesService;
    constructor(artworkRepository, projectsService, filesService) {
        this.artworkRepository = artworkRepository;
        this.projectsService = projectsService;
        this.filesService = filesService;
    }
    async create(createArtworkDto, image, user) {
        await this.validateProjectAccess(createArtworkDto.projectId, user);
        const artworkNo = await this.generateArtworkNo(createArtworkDto.projectId);
        const imageUrl = await this.filesService.uploadImage(image, 'artworks');
        const thumbnailUrl = await this.filesService.generateThumbnail(imageUrl);
        const artwork = this.artworkRepository.create({
            ...createArtworkDto,
            artworkNo,
            imageUrl,
            thumbnailUrl,
        });
        return await this.artworkRepository.save(artwork);
    }
    async batchUpload(batchUploadDto, images, user) {
        await this.validateProjectAccess(batchUploadDto.projectId, user);
        const artworks = [];
        for (let i = 0; i < batchUploadDto.artworks.length; i++) {
            const artworkData = batchUploadDto.artworks[i];
            const image = images[i];
            if (!image) {
                throw new common_1.BadRequestException(`第 ${i + 1} 个作品缺少图片文件`);
            }
            const artworkNo = await this.generateArtworkNo(batchUploadDto.projectId);
            const imageUrl = await this.filesService.uploadImage(image, 'artworks');
            const thumbnailUrl = await this.filesService.generateThumbnail(imageUrl);
            const artwork = this.artworkRepository.create({
                ...artworkData,
                projectId: batchUploadDto.projectId,
                artworkNo,
                imageUrl,
                thumbnailUrl,
            });
            artworks.push(await this.artworkRepository.save(artwork));
        }
        return artworks;
    }
    async importFromExcel(projectId, excel, user) {
        await this.validateProjectAccess(projectId, user);
        const workbook = XLSX.read(excel.buffer, { type: 'buffer' });
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        const data = XLSX.utils.sheet_to_json(worksheet);
        let success = 0;
        let failed = 0;
        const errors = [];
        for (let i = 0; i < data.length; i++) {
            try {
                const row = data[i];
                if (!row['作品名称'] || !row['作者姓名']) {
                    throw new Error('作品名称和作者姓名为必填项');
                }
                const artworkNo = await this.generateArtworkNo(projectId);
                const artwork = this.artworkRepository.create({
                    projectId,
                    artworkNo,
                    title: row['作品名称'],
                    description: row['作品描述'] || '',
                    size: row['作品尺寸'] || '',
                    technique: row['创作技法'] || '',
                    creationYear: row['创作年份'] ? parseInt(row['创作年份']) : null,
                    authorName: row['作者姓名'],
                    authorPhone: row['作者电话'] || '',
                    authorAddress: row['作者地址'] || '',
                    authorBio: row['作者简介'] || '',
                    tags: row['作品标签'] ? row['作品标签'].split(',') : [],
                    imageUrl: '',
                    status: artwork_entity_1.ArtworkStatus.UPLOADED,
                });
                await this.artworkRepository.save(artwork);
                success++;
            }
            catch (error) {
                failed++;
                errors.push(`第 ${i + 1} 行: ${error.message}`);
            }
        }
        return { success, failed, errors };
    }
    async findAll(options) {
        const { page, limit, projectId, status, authorName, tags } = options;
        const queryBuilder = this.artworkRepository
            .createQueryBuilder('artwork')
            .leftJoinAndSelect('artwork.project', 'project');
        if (projectId) {
            queryBuilder.andWhere('artwork.projectId = :projectId', { projectId });
        }
        if (status) {
            queryBuilder.andWhere('artwork.status = :status', { status });
        }
        if (authorName) {
            queryBuilder.andWhere('artwork.authorName LIKE :authorName', {
                authorName: `%${authorName}%`,
            });
        }
        if (tags && tags.length > 0) {
            queryBuilder.andWhere('JSON_OVERLAPS(artwork.tags, :tags)', {
                tags: JSON.stringify(tags),
            });
        }
        queryBuilder
            .orderBy('artwork.createdAt', 'DESC')
            .skip((page - 1) * limit)
            .take(limit);
        const [artworks, total] = await queryBuilder.getManyAndCount();
        return {
            data: artworks,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
        };
    }
    async findByProject(projectId, options) {
        return this.findAll({ ...options, projectId });
    }
    async findOne(id) {
        const artwork = await this.artworkRepository.findOne({
            where: { id },
            relations: ['project'],
        });
        if (!artwork) {
            throw new common_1.NotFoundException('作品不存在');
        }
        return artwork;
    }
    async update(id, updateArtworkDto, image, user) {
        const artwork = await this.findOne(id);
        await this.validateProjectAccess(artwork.projectId, user);
        if (image) {
            const imageUrl = await this.filesService.uploadImage(image, 'artworks');
            const thumbnailUrl = await this.filesService.generateThumbnail(imageUrl);
            await this.filesService.deleteFile(artwork.imageUrl);
            if (artwork.thumbnailUrl) {
                await this.filesService.deleteFile(artwork.thumbnailUrl);
            }
            updateArtworkDto.imageUrl = imageUrl;
            updateArtworkDto.thumbnailUrl = thumbnailUrl;
        }
        Object.assign(artwork, updateArtworkDto);
        return await this.artworkRepository.save(artwork);
    }
    async updateStatus(id, status, reviewComment, user) {
        const artwork = await this.findOne(id);
        await this.validateProjectAccess(artwork.projectId, user);
        artwork.status = status;
        artwork.reviewTime = new Date();
        return await this.artworkRepository.save(artwork);
    }
    async remove(id, user) {
        const artwork = await this.findOne(id);
        await this.validateProjectAccess(artwork.projectId, user);
        await this.filesService.deleteFile(artwork.imageUrl);
        if (artwork.thumbnailUrl) {
            await this.filesService.deleteFile(artwork.thumbnailUrl);
        }
        await this.artworkRepository.remove(artwork);
    }
    async getProjectStats(projectId, user) {
        await this.validateProjectAccess(projectId, user);
        const stats = await this.artworkRepository
            .createQueryBuilder('artwork')
            .select('artwork.status', 'status')
            .addSelect('COUNT(*)', 'count')
            .where('artwork.projectId = :projectId', { projectId })
            .groupBy('artwork.status')
            .getRawMany();
        const total = await this.artworkRepository.count({
            where: { projectId },
        });
        return {
            total,
            byStatus: stats.reduce((acc, item) => {
                acc[item.status] = parseInt(item.count);
                return acc;
            }, {}),
        };
    }
    async validateProjectAccess(projectId, user) {
        const project = await this.projectsService.findOne(projectId);
        if (user.role !== user_entity_1.UserRole.SUPER_ADMIN &&
            user.role !== user_entity_1.UserRole.ADMIN &&
            project.organizerId !== user.id) {
            throw new common_1.ForbiddenException('无权限访问此项目的作品');
        }
    }
    async generateArtworkNo(projectId) {
        const count = await this.artworkRepository.count({
            where: { projectId },
        });
        return `${projectId.slice(-6)}-${String(count + 1).padStart(4, '0')}`;
    }
};
exports.ArtworksService = ArtworksService;
exports.ArtworksService = ArtworksService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(artwork_entity_1.Artwork)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, projects_service_1.ProjectsService,
        files_service_1.FilesService])
], ArtworksService);
//# sourceMappingURL=artworks.service.js.map