"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FilesService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const fs = require("fs");
const path = require("path");
const sharp = require("sharp");
const uuid_1 = require("uuid");
let FilesService = class FilesService {
    configService;
    uploadPath;
    maxFileSize;
    allowedTypes;
    constructor(configService) {
        this.configService = configService;
        this.uploadPath = this.configService.get('UPLOAD_PATH', './uploads');
        this.maxFileSize = parseInt(this.configService.get('MAX_FILE_SIZE', '10485760'));
        this.allowedTypes = this.configService.get('ALLOWED_FILE_TYPES', 'jpg,jpeg,png,gif,pdf,doc,docx').split(',');
        this.ensureUploadDir();
    }
    async uploadImage(file, folder = 'images') {
        this.validateFile(file);
        const fileExtension = path.extname(file.originalname).toLowerCase();
        const fileName = `${(0, uuid_1.v4)()}${fileExtension}`;
        const folderPath = path.join(this.uploadPath, folder);
        const filePath = path.join(folderPath, fileName);
        if (!fs.existsSync(folderPath)) {
            fs.mkdirSync(folderPath, { recursive: true });
        }
        if (this.isImage(file.mimetype)) {
            await sharp(file.buffer)
                .resize(1920, 1080, {
                fit: 'inside',
                withoutEnlargement: true
            })
                .jpeg({ quality: 85 })
                .toFile(filePath);
        }
        else {
            fs.writeFileSync(filePath, file.buffer);
        }
        return this.getFileUrl(folder, fileName);
    }
    async uploadFile(file, folder = 'documents') {
        this.validateFile(file);
        const fileExtension = path.extname(file.originalname).toLowerCase();
        const fileName = `${(0, uuid_1.v4)()}${fileExtension}`;
        const folderPath = path.join(this.uploadPath, folder);
        const filePath = path.join(folderPath, fileName);
        if (!fs.existsSync(folderPath)) {
            fs.mkdirSync(folderPath, { recursive: true });
        }
        fs.writeFileSync(filePath, file.buffer);
        return this.getFileUrl(folder, fileName);
    }
    async uploadMultipleFiles(files, folder = 'batch') {
        const uploadPromises = files.map(file => this.uploadFile(file, folder));
        return Promise.all(uploadPromises);
    }
    async generateThumbnail(imageUrl, width = 300, height = 300) {
        try {
            const imagePath = this.getLocalPath(imageUrl);
            if (!fs.existsSync(imagePath)) {
                throw new Error('原图片不存在');
            }
            const fileExtension = path.extname(imagePath);
            const fileName = `thumb_${(0, uuid_1.v4)()}${fileExtension}`;
            const thumbnailPath = path.join(path.dirname(imagePath), fileName);
            await sharp(imagePath)
                .resize(width, height, {
                fit: 'cover',
                position: 'center'
            })
                .jpeg({ quality: 80 })
                .toFile(thumbnailPath);
            return this.getFileUrl(path.basename(path.dirname(imagePath)), fileName);
        }
        catch (error) {
            console.error('生成缩略图失败:', error);
            return imageUrl;
        }
    }
    async deleteFile(fileUrl) {
        try {
            const filePath = this.getLocalPath(fileUrl);
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
            }
        }
        catch (error) {
            console.error('删除文件失败:', error);
        }
    }
    async getFileInfo(fileUrl) {
        try {
            const filePath = this.getLocalPath(fileUrl);
            if (!fs.existsSync(filePath)) {
                return { exists: false };
            }
            const stats = fs.statSync(filePath);
            const fileExtension = path.extname(filePath).toLowerCase();
            return {
                exists: true,
                size: stats.size,
                mimeType: this.getMimeType(fileExtension),
                lastModified: stats.mtime,
            };
        }
        catch (error) {
            return { exists: false };
        }
    }
    validateFile(file) {
        if (!file) {
            throw new common_1.BadRequestException('文件不能为空');
        }
        if (file.size > this.maxFileSize) {
            throw new common_1.BadRequestException(`文件大小不能超过 ${this.maxFileSize / 1024 / 1024}MB`);
        }
        const fileExtension = path.extname(file.originalname).toLowerCase().slice(1);
        if (!this.allowedTypes.includes(fileExtension)) {
            throw new common_1.BadRequestException(`不支持的文件类型，允许的类型: ${this.allowedTypes.join(', ')}`);
        }
    }
    isImage(mimeType) {
        return mimeType.startsWith('image/');
    }
    ensureUploadDir() {
        if (!fs.existsSync(this.uploadPath)) {
            fs.mkdirSync(this.uploadPath, { recursive: true });
        }
    }
    getFileUrl(folder, fileName) {
        return `/uploads/${folder}/${fileName}`;
    }
    getLocalPath(fileUrl) {
        const relativePath = fileUrl.replace(/^\/uploads\//, '');
        return path.join(this.uploadPath, relativePath);
    }
    getMimeType(extension) {
        const mimeTypes = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.pdf': 'application/pdf',
            '.doc': 'application/msword',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        };
        return mimeTypes[extension] || 'application/octet-stream';
    }
};
exports.FilesService = FilesService;
exports.FilesService = FilesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], FilesService);
//# sourceMappingURL=files.service.js.map