import { User as UserEntity } from '../../users/entities/user.entity';
export declare const CurrentUser: (...dataOrPipes: (keyof UserEntity | import("@nestjs/common").PipeTransform<any, any> | import("@nestjs/common").Type<import("@nestjs/common").PipeTransform<any, any>> | undefined)[]) => ParameterDecorator;
export declare const UserId: (...dataOrPipes: unknown[]) => ParameterDecorator;
export declare const UserRole: (...dataOrPipes: unknown[]) => ParameterDecorator;
export declare const IsAdmin: (...dataOrPipes: unknown[]) => ParameterDecorator;
export declare const IsSuperAdmin: (...dataOrPipes: unknown[]) => ParameterDecorator;
export declare const IsOrganizer: (...dataOrPipes: unknown[]) => ParameterDecorator;
export declare const IsJudge: (...dataOrPipes: unknown[]) => ParameterDecorator;
export declare const IsAuthenticated: (...dataOrPipes: unknown[]) => ParameterDecorator;
export declare const SafeUser: (...dataOrPipes: unknown[]) => ParameterDecorator;
export declare const ClientIP: (...dataOrPipes: unknown[]) => ParameterDecorator;
export declare const UserAgent: (...dataOrPipes: unknown[]) => ParameterDecorator;
export declare const RequestId: (...dataOrPipes: unknown[]) => ParameterDecorator;
