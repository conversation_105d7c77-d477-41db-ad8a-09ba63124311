import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';

/**
 * 响应拦截器
 * 统一处理成功响应格式，添加请求ID和响应时间
 */
@Injectable()
export class ResponseInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    
    // 记录请求开始时间
    const startTime = Date.now();
    
    // 获取或生成请求ID
    const requestId = (request.headers['x-request-id'] as string) || uuidv4();
    
    return next.handle().pipe(
      map(data => {
        // 计算响应时间
        const responseTime = Date.now() - startTime;
        
        // 设置响应头
        response.setHeader('X-Request-ID', requestId);
        response.setHeader('X-Response-Time', `${responseTime}ms`);
        response.setHeader('Content-Type', 'application/json; charset=utf-8');
        
        // 如果返回的数据已经是标准格式，直接返回
        if (data && typeof data === 'object' && 'success' in data) {
          return data;
        }
        
        // 否则包装成标准格式
        return {
          success: true,
          code: response.statusCode,
          message: '操作成功',
          data,
          timestamp: Date.now(),
          requestId,
        };
      })
    );
  }
}

/**
 * 日志拦截器
 * 记录请求和响应日志
 */
@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    
    const { method, url, ip, headers } = request;
    const userAgent = headers['user-agent'] || '';
    const userId = (request as any).user?.id || 'anonymous';
    const requestId = headers['x-request-id'] || uuidv4();
    
    const startTime = Date.now();
    
    // 记录请求日志
    console.log(
      `[${new Date().toISOString()}] ${method} ${url} - ${ip} - ${userId} - ${requestId}`
    );
    
    return next.handle().pipe(
      map(data => {
        const responseTime = Date.now() - startTime;
        
        // 记录响应日志
        console.log(
          `[${new Date().toISOString()}] ${method} ${url} - ${response.statusCode} - ${responseTime}ms - ${requestId}`
        );
        
        return data;
      })
    );
  }
}

/**
 * 缓存拦截器
 * 为GET请求添加缓存控制头
 */
@Injectable()
export class CacheInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    
    return next.handle().pipe(
      map(data => {
        // 为GET请求设置缓存头
        if (request.method === 'GET') {
          // 根据路径设置不同的缓存策略
          if (request.url.includes('/stats') || request.url.includes('/export')) {
            // 统计和导出接口不缓存
            response.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
          } else if (request.url.includes('/artworks') || request.url.includes('/projects')) {
            // 作品和项目接口短时间缓存
            response.setHeader('Cache-Control', 'public, max-age=300'); // 5分钟
          } else {
            // 其他接口默认缓存
            response.setHeader('Cache-Control', 'public, max-age=60'); // 1分钟
          }
          
          response.setHeader('Pragma', 'cache');
          response.setHeader('Expires', new Date(Date.now() + 60000).toUTCString());
        }
        
        return data;
      })
    );
  }
}

/**
 * 安全头拦截器
 * 添加安全相关的响应头
 */
@Injectable()
export class SecurityInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const response = ctx.getResponse<Response>();
    
    return next.handle().pipe(
      map(data => {
        // 设置安全头
        response.setHeader('X-Content-Type-Options', 'nosniff');
        response.setHeader('X-Frame-Options', 'DENY');
        response.setHeader('X-XSS-Protection', '1; mode=block');
        response.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
        response.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
        
        // 移除可能泄露服务器信息的头
        response.removeHeader('X-Powered-By');
        response.removeHeader('Server');
        
        return data;
      })
    );
  }
}
