"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleManagementService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("../../users/entities/user.entity");
const user_roles_entity_1 = require("../../users/entities/user-roles.entity");
let RoleManagementService = class RoleManagementService {
    userRepository;
    globalRoleRepository;
    projectRoleRepository;
    applicationRepository;
    templateRepository;
    constructor(userRepository, globalRoleRepository, projectRoleRepository, applicationRepository, templateRepository) {
        this.userRepository = userRepository;
        this.globalRoleRepository = globalRoleRepository;
        this.projectRoleRepository = projectRoleRepository;
        this.applicationRepository = applicationRepository;
        this.templateRepository = templateRepository;
    }
    async applyForGlobalRole(userId, targetRole, applicationData) {
        const existingRole = await this.globalRoleRepository.findOne({
            where: { userId, role: targetRole, isActive: true }
        });
        if (existingRole) {
            throw new common_1.ConflictException('您已经拥有此角色');
        }
        const pendingApplication = await this.applicationRepository.findOne({
            where: {
                userId,
                targetRole,
                status: 'pending'
            }
        });
        if (pendingApplication) {
            throw new common_1.ConflictException('您已有待处理的申请');
        }
        const application = this.applicationRepository.create({
            userId,
            targetRole,
            applicationData,
            status: 'pending'
        });
        return await this.applicationRepository.save(application);
    }
    async reviewRoleApplication(applicationId, reviewerId, approved, reviewComment) {
        const application = await this.applicationRepository.findOne({
            where: { id: applicationId },
            relations: ['user']
        });
        if (!application) {
            throw new common_1.NotFoundException('申请记录不存在');
        }
        if (application.status !== 'pending') {
            throw new common_1.ConflictException('申请已被处理');
        }
        application.status = approved ? 'approved' : 'rejected';
        application.reviewerId = reviewerId;
        application.reviewComment = reviewComment;
        application.reviewedAt = new Date();
        await this.applicationRepository.save(application);
        if (approved) {
            await this.addGlobalRole(application.userId, application.targetRole, reviewerId, {
                reason: '角色申请审批通过',
                applicationId: applicationId
            });
        }
        return application;
    }
    async addGlobalRole(userId, role, grantedBy, metadata) {
        const user = await this.userRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        const existingRole = await this.globalRoleRepository.findOne({
            where: { userId, role }
        });
        if (existingRole) {
            if (existingRole.isActive) {
                throw new common_1.ConflictException('用户已拥有此角色');
            }
            else {
                existingRole.isActive = true;
                existingRole.metadata = {
                    ...existingRole.metadata,
                    ...metadata,
                    grantedBy,
                    grantedAt: new Date()
                };
                return await this.globalRoleRepository.save(existingRole);
            }
        }
        const globalRole = this.globalRoleRepository.create({
            userId,
            role,
            metadata: {
                grantedBy,
                grantedAt: new Date(),
                ...metadata
            },
            isActive: true
        });
        return await this.globalRoleRepository.save(globalRole);
    }
    async removeGlobalRole(userId, role, removedBy, reason) {
        const globalRole = await this.globalRoleRepository.findOne({
            where: { userId, role, isActive: true }
        });
        if (!globalRole) {
            throw new common_1.NotFoundException('用户没有此角色');
        }
        globalRole.isActive = false;
        globalRole.metadata = {
            ...globalRole.metadata,
            removedBy,
            removedAt: new Date(),
            reason
        };
        await this.globalRoleRepository.save(globalRole);
    }
    async getUserGlobalRoles(userId) {
        return await this.globalRoleRepository.find({
            where: { userId, isActive: true },
            order: { createdAt: 'ASC' }
        });
    }
    async getRoleApplications(filters) {
        const { status, targetRole, userId, page = 1, limit = 10 } = filters;
        const queryBuilder = this.applicationRepository
            .createQueryBuilder('app')
            .leftJoinAndSelect('app.user', 'user')
            .leftJoinAndSelect('app.reviewer', 'reviewer');
        if (status) {
            queryBuilder.andWhere('app.status = :status', { status });
        }
        if (targetRole) {
            queryBuilder.andWhere('app.targetRole = :targetRole', { targetRole });
        }
        if (userId) {
            queryBuilder.andWhere('app.userId = :userId', { userId });
        }
        queryBuilder
            .orderBy('app.appliedAt', 'DESC')
            .skip((page - 1) * limit)
            .take(limit);
        const [data, total] = await queryBuilder.getManyAndCount();
        return { data, total, page, limit };
    }
    async getPermissionTemplates(role) {
        const where = { isActive: true };
        if (role) {
            where.role = role;
        }
        return await this.templateRepository.find({
            where,
            order: { isDefault: 'DESC', createdAt: 'ASC' }
        });
    }
    async createPermissionTemplate(name, role, permissions, description, isDefault = false) {
        if (isDefault) {
            await this.templateRepository.update({ role, isDefault: true }, { isDefault: false });
        }
        const template = this.templateRepository.create({
            name,
            role,
            permissions,
            description,
            isDefault
        });
        return await this.templateRepository.save(template);
    }
    async getDefaultPermissions(role) {
        const template = await this.templateRepository.findOne({
            where: { role, isDefault: true, isActive: true }
        });
        if (template) {
            return template.permissions;
        }
        return this.getHardcodedDefaultPermissions(role);
    }
    getHardcodedDefaultPermissions(role) {
        const defaultPermissions = {
            [user_roles_entity_1.ProjectRole.PROJECT_OWNER]: {
                canManageProject: true,
                canInviteUsers: true,
                canManageArtworks: true,
                canScore: false,
                canViewScores: true,
                canExportData: true,
                canModerateContent: true,
                canManageSchedule: true,
                customPermissions: []
            },
            [user_roles_entity_1.ProjectRole.PROJECT_ADMIN]: {
                canManageProject: true,
                canInviteUsers: true,
                canManageArtworks: true,
                canScore: false,
                canViewScores: true,
                canExportData: true,
                canModerateContent: true,
                canManageSchedule: true,
                customPermissions: []
            },
            [user_roles_entity_1.ProjectRole.PROJECT_JUDGE]: {
                canManageProject: false,
                canInviteUsers: false,
                canManageArtworks: false,
                canScore: true,
                canViewScores: true,
                canExportData: false,
                canModerateContent: false,
                canManageSchedule: false,
                customPermissions: []
            },
            [user_roles_entity_1.ProjectRole.PROJECT_ARTIST]: {
                canManageProject: false,
                canInviteUsers: false,
                canManageArtworks: false,
                canScore: false,
                canViewScores: false,
                canExportData: false,
                canModerateContent: false,
                canManageSchedule: false,
                customPermissions: []
            },
            [user_roles_entity_1.ProjectRole.PROJECT_VIEWER]: {
                canManageProject: false,
                canInviteUsers: false,
                canManageArtworks: false,
                canScore: false,
                canViewScores: false,
                canExportData: false,
                canModerateContent: false,
                canManageSchedule: false,
                customPermissions: []
            },
            [user_roles_entity_1.ProjectRole.PROJECT_MODERATOR]: {
                canManageProject: false,
                canInviteUsers: false,
                canManageArtworks: true,
                canScore: false,
                canViewScores: true,
                canExportData: false,
                canModerateContent: true,
                canManageSchedule: false,
                customPermissions: []
            },
            [user_roles_entity_1.ProjectRole.PROJECT_ASSISTANT]: {
                canManageProject: false,
                canInviteUsers: false,
                canManageArtworks: true,
                canScore: false,
                canViewScores: false,
                canExportData: false,
                canModerateContent: true,
                canManageSchedule: true,
                customPermissions: []
            }
        };
        return defaultPermissions[role] || {};
    }
    async batchAssignGlobalRoles(assignments, grantedBy) {
        const results = [];
        for (const assignment of assignments) {
            try {
                const globalRole = await this.addGlobalRole(assignment.userId, assignment.role, grantedBy, assignment.metadata);
                results.push(globalRole);
            }
            catch (error) {
                console.error(`Failed to assign role ${assignment.role} to user ${assignment.userId}:`, error.message);
            }
        }
        return results;
    }
    async getRoleStatistics() {
        const globalRoleStats = await this.globalRoleRepository
            .createQueryBuilder('gr')
            .select('gr.role', 'role')
            .addSelect('COUNT(*)', 'count')
            .where('gr.isActive = :isActive', { isActive: true })
            .groupBy('gr.role')
            .getRawMany();
        const applicationStats = await this.applicationRepository
            .createQueryBuilder('app')
            .select('app.status', 'status')
            .addSelect('COUNT(*)', 'count')
            .groupBy('app.status')
            .getRawMany();
        const projectRoleStats = await this.projectRoleRepository
            .createQueryBuilder('pr')
            .select('pr.role', 'role')
            .addSelect('COUNT(*)', 'count')
            .where('pr.status = :status', { status: 'accepted' })
            .groupBy('pr.role')
            .getRawMany();
        return {
            globalRoles: globalRoleStats,
            applications: applicationStats,
            projectRoles: projectRoleStats,
            totalUsers: await this.userRepository.count(),
            totalActiveRoles: await this.globalRoleRepository.count({ where: { isActive: true } }),
            totalProjectMembers: await this.projectRoleRepository.count({ where: { status: 'accepted' } })
        };
    }
};
exports.RoleManagementService = RoleManagementService;
exports.RoleManagementService = RoleManagementService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(user_roles_entity_1.UserGlobalRole)),
    __param(2, (0, typeorm_1.InjectRepository)(user_roles_entity_1.UserProjectRole)),
    __param(3, (0, typeorm_1.InjectRepository)(user_roles_entity_1.RoleApplication)),
    __param(4, (0, typeorm_1.InjectRepository)(user_roles_entity_1.PermissionTemplate)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object, typeof (_d = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _d : Object, typeof (_e = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _e : Object])
], RoleManagementService);
//# sourceMappingURL=role-management.service.js.map