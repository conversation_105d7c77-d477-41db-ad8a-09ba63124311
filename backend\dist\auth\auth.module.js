"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthModule = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const passport_1 = require("@nestjs/passport");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const auth_service_1 = require("./auth.service");
const role_management_service_1 = require("./services/role-management.service");
const project_permission_service_1 = require("./services/project-permission.service");
const auth_controller_1 = require("./auth.controller");
const users_module_1 = require("../users/users.module");
const user_entity_1 = require("../users/entities/user.entity");
const user_roles_entity_1 = require("../users/entities/user-roles.entity");
const auth_guard_1 = require("./guards/auth.guard");
const roles_guard_1 = require("./guards/roles.guard");
const project_permission_guard_1 = require("./guards/project-permission.guard");
const jwt_strategy_1 = require("./strategies/jwt.strategy");
let AuthModule = class AuthModule {
};
exports.AuthModule = AuthModule;
exports.AuthModule = AuthModule = __decorate([
    (0, common_1.Module)({
        imports: [
            users_module_1.UsersModule,
            typeorm_1.TypeOrmModule.forFeature([
                user_entity_1.User,
                user_roles_entity_1.UserGlobalRole,
                user_roles_entity_1.UserProjectRole,
                user_roles_entity_1.RoleApplication,
                user_roles_entity_1.PermissionTemplate,
            ]),
            passport_1.PassportModule,
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    secret: configService.get('JWT_SECRET') || 'your-secret-key',
                    signOptions: {
                        expiresIn: configService.get('JWT_EXPIRES_IN', '24h'),
                    },
                }),
                inject: [config_1.ConfigService],
            }),
        ],
        controllers: [auth_controller_1.AuthController],
        providers: [
            auth_service_1.AuthService,
            role_management_service_1.RoleManagementService,
            project_permission_service_1.ProjectPermissionService,
            jwt_strategy_1.JwtStrategy,
            auth_guard_1.NoAuthGuard,
            auth_guard_1.RequiredAuthGuard,
            auth_guard_1.OptionalAuthGuard,
            auth_guard_1.JwtAuthGuard,
            auth_guard_1.SmartAuthGuard,
            roles_guard_1.RolesGuard,
            project_permission_guard_1.ProjectPermissionGuard,
            project_permission_guard_1.MultiRoleGuard,
            project_permission_guard_1.ResourceOwnerGuard,
        ],
        exports: [
            auth_service_1.AuthService,
            role_management_service_1.RoleManagementService,
            project_permission_service_1.ProjectPermissionService,
            jwt_1.JwtModule,
            auth_guard_1.NoAuthGuard,
            auth_guard_1.RequiredAuthGuard,
            auth_guard_1.OptionalAuthGuard,
            auth_guard_1.JwtAuthGuard,
            auth_guard_1.SmartAuthGuard,
            roles_guard_1.RolesGuard,
            project_permission_guard_1.ProjectPermissionGuard,
            project_permission_guard_1.MultiRoleGuard,
            project_permission_guard_1.ResourceOwnerGuard,
        ],
    })
], AuthModule);
//# sourceMappingURL=auth.module.js.map