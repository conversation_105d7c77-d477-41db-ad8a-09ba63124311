import { Repository } from 'typeorm';
import { CreateScoreDto } from './dto/create-score.dto';
import { UpdateScoreDto } from './dto/update-score.dto';
import { CreateCommentDto } from './dto/create-comment.dto';
import { Score, Comment, ScoreStatus } from './entities/score.entity';
import { User } from '../users/entities/user.entity';
import { ProjectsService } from '../projects/projects.service';
import { ArtworksService } from '../artworks/artworks.service';
export declare class ScoresService {
    private scoreRepository;
    private commentRepository;
    private projectsService;
    private artworksService;
    constructor(scoreRepository: Repository<Score>, commentRepository: Repository<Comment>, projectsService: ProjectsService, artworksService: ArtworksService);
    create(createScoreDto: CreateScoreDto, judgeId: string): Promise<Score>;
    findByProject(projectId: string, options: {
        page: number;
        limit: number;
        judgeId?: string;
        status?: ScoreStatus;
        user: User;
    }): Promise<{
        data: any;
        total: any;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    findByArtwork(artworkId: string, user: User): Promise<any>;
    findMyScores(projectId: string, judgeId: string, options: {
        page: number;
        limit: number;
    }): Promise<{
        data: any;
        total: any;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    findOne(id: string, user: User): Promise<Score>;
    update(id: string, updateScoreDto: UpdateScoreDto, user: User): Promise<Score>;
    submit(id: string, user: User): Promise<Score>;
    remove(id: string, user: User): Promise<void>;
    createComment(scoreId: string, createCommentDto: CreateCommentDto, user: User): Promise<Comment>;
    getComments(scoreId: string, user: User): Promise<Comment[]>;
    updateComment(commentId: string, updateData: Partial<CreateCommentDto>, user: User): Promise<Comment>;
    removeComment(commentId: string, user: User): Promise<void>;
    getProjectScoreStats(projectId: string, user: User): Promise<any>;
    getArtworkScoreStats(artworkId: string, user: User): Promise<any>;
    private calculateTotalScore;
    private validateProjectAccess;
}
