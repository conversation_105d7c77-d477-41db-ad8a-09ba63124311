# 书画评选系统架构图表

本目录包含了书画评选系统的各种架构图表，使用Mermaid格式绘制。

## 📊 图表列表

### 1. 多角色权限系统架构图
**文件**: `multi-role-system.mmd`
**描述**: 展示用户多角色体系、项目权限体系和权限检查流程

### 2. 角色关系图
**文件**: `role-relationships.mmd`  
**描述**: 展示6种角色的层次关系、权限继承和业务协作

### 3. 业务流程图
**文件**: `business-flow.mmd`
**描述**: 展示从项目创建到结果发布的完整业务流程

## 🖼️ 如何查看和保存图表

### 方法1: 在线查看（推荐）
1. 访问 [Mermaid Live Editor](https://mermaid.live/)
2. 复制 `.mmd` 文件中的代码
3. 粘贴到编辑器中查看
4. 点击 "Actions" → "Download PNG/SVG" 保存图片

### 方法2: VS Code查看
1. 安装 "Mermaid Preview" 插件
2. 打开 `.mmd` 文件
3. 右键选择 "Mermaid: Preview"
4. 在预览窗口中右键保存图片

### 方法3: 命令行生成
```bash
# 安装Mermaid CLI
npm install -g @mermaid-js/mermaid-cli

# 生成PNG图片
mmdc -i multi-role-system.mmd -o multi-role-system.png

# 生成SVG图片
mmdc -i multi-role-system.mmd -o multi-role-system.svg

# 生成PDF文件
mmdc -i multi-role-system.mmd -o multi-role-system.pdf
```

### 方法4: 在Markdown中使用
```markdown
# 在Markdown文件中嵌入图表

```mermaid
graph TB
    // 复制.mmd文件中的内容
```
```

## 📁 生成的图片文件

使用上述方法生成的图片文件建议保存在以下位置：
```
docs/diagrams/images/
├── multi-role-system.png
├── multi-role-system.svg
├── role-relationships.png
├── role-relationships.svg
├── business-flow.png
└── business-flow.svg
```

## 🎨 图表说明

### 多角色权限系统架构图
- **蓝色区域**: 用户角色体系
- **绿色区域**: 项目权限体系  
- **黄色区域**: 权限检查流程
- **箭头**: 表示关系和流程方向

### 角色关系图
- **红色**: 超级管理员（最高权限）
- **青色**: 系统管理员
- **蓝色**: 办展方
- **绿色**: 美工组/作者
- **黄色**: 评委
- **灰色**: 普通用户

### 业务流程图
- **蓝色**: 办展方操作
- **绿色**: 艺术家操作
- **黄色**: 评委操作
- **粉色**: 系统自动操作

## 🔧 自定义图表

如需修改图表，请编辑对应的 `.mmd` 文件，然后重新生成图片。

### 常用Mermaid语法
```mermaid
graph TB
    A[矩形节点] --> B{菱形决策}
    B -->|是| C((圆形节点))
    B -->|否| D[另一个节点]
    
    %% 样式定义
    classDef className fill:#color,stroke:#color
    class A,B className
```

### 颜色参考
- 蓝色系: `#e1f5fe`, `#01579b`
- 绿色系: `#e8f5e8`, `#1b5e20`  
- 黄色系: `#fff3e0`, `#e65100`
- 红色系: `#fce4ec`, `#880e4f`

## 📞 技术支持

如果在查看或生成图表时遇到问题，请参考：
- [Mermaid官方文档](https://mermaid-js.github.io/mermaid/)
- [Mermaid语法指南](https://mermaid-js.github.io/mermaid/#/flowchart)
- [VS Code Mermaid插件](https://marketplace.visualstudio.com/items?itemName=bierner.markdown-mermaid)
