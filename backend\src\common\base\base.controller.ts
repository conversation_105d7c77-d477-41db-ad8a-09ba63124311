import { Injectable } from '@nestjs/common';
import { Request } from 'express';
import { v4 as uuidv4 } from 'uuid';

/**
 * 统一响应格式接口
 */
export interface ApiResponse<T = any> {
  success: boolean;
  code: number;
  message: string;
  data?: T;
  timestamp: number;
  requestId: string;
}

/**
 * 分页响应格式接口
 */
export interface PaginatedResponse<T> {
  success: boolean;
  code: number;
  message: string;
  data: {
    items: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
  timestamp: number;
  requestId: string;
}

/**
 * 错误响应格式接口
 */
export interface ApiErrorResponse {
  success: false;
  code: number;
  message: string;
  error: {
    type: string;
    details?: any;
    field?: string;
  };
  timestamp: number;
  requestId: string;
}

/**
 * 分页查询参数接口
 */
export interface PaginationQuery {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'ASC' | 'DESC';
}

/**
 * 基础查询参数接口
 */
export interface BaseQuery extends PaginationQuery {
  search?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  fields?: string;
}

/**
 * 业务状态码枚举
 */
export enum BusinessCode {
  // 成功 (200-299)
  SUCCESS = 200,
  CREATED = 201,
  
  // 客户端错误 (400-499)
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  VALIDATION_ERROR = 422,
  
  // 业务错误 (1000-9999)
  USER_NOT_FOUND = 1001,
  USER_DISABLED = 1002,
  PROJECT_NOT_FOUND = 2001,
  PROJECT_STATUS_ERROR = 2002,
  ARTWORK_NOT_FOUND = 3001,
  SCORE_DUPLICATE = 4001,
  SCORE_TIME_EXPIRED = 4002,
  APPLICATION_NOT_FOUND = 5001,
  
  // 服务器错误 (500-599)
  INTERNAL_ERROR = 500,
  DATABASE_ERROR = 501,
  EXTERNAL_SERVICE_ERROR = 502
}

/**
 * 错误类型枚举
 */
export enum ErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  BUSINESS_ERROR = 'BUSINESS_ERROR',
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  EXTERNAL_ERROR = 'EXTERNAL_ERROR'
}

/**
 * 基础控制器类
 */
@Injectable()
export abstract class BaseController {
  /**
   * 获取请求ID
   */
  protected getRequestId(req?: Request): string {
    return req?.headers['x-request-id'] as string || uuidv4();
  }

  /**
   * 成功响应
   */
  protected success<T>(
    data: T,
    message: string = '操作成功',
    code: number = BusinessCode.SUCCESS,
    req?: Request
  ): ApiResponse<T> {
    return {
      success: true,
      code,
      message,
      data,
      timestamp: Date.now(),
      requestId: this.getRequestId(req)
    };
  }

  /**
   * 创建成功响应
   */
  protected created<T>(
    data: T,
    message: string = '创建成功',
    req?: Request
  ): ApiResponse<T> {
    return this.success(data, message, BusinessCode.CREATED, req);
  }

  /**
   * 分页响应
   */
  protected paginated<T>(
    items: T[],
    total: number,
    page: number,
    limit: number,
    message: string = '获取成功',
    req?: Request
  ): PaginatedResponse<T> {
    const totalPages = Math.ceil(total / limit);
    
    return {
      success: true,
      code: BusinessCode.SUCCESS,
      message,
      data: {
        items,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      },
      timestamp: Date.now(),
      requestId: this.getRequestId(req)
    };
  }

  /**
   * 错误响应
   */
  protected error(
    message: string,
    code: number = BusinessCode.BAD_REQUEST,
    errorType: ErrorType = ErrorType.BUSINESS_ERROR,
    details?: any,
    field?: string,
    req?: Request
  ): ApiErrorResponse {
    return {
      success: false,
      code,
      message,
      error: {
        type: errorType,
        details,
        field
      },
      timestamp: Date.now(),
      requestId: this.getRequestId(req)
    };
  }

  /**
   * 验证错误响应
   */
  protected validationError(
    details: any,
    message: string = '参数验证失败',
    req?: Request
  ): ApiErrorResponse {
    return this.error(
      message,
      BusinessCode.VALIDATION_ERROR,
      ErrorType.VALIDATION_ERROR,
      details,
      undefined,
      req
    );
  }

  /**
   * 未找到错误响应
   */
  protected notFound(
    message: string = '资源不存在',
    req?: Request
  ): ApiErrorResponse {
    return this.error(
      message,
      BusinessCode.NOT_FOUND,
      ErrorType.BUSINESS_ERROR,
      undefined,
      undefined,
      req
    );
  }

  /**
   * 权限不足错误响应
   */
  protected forbidden(
    message: string = '权限不足',
    req?: Request
  ): ApiErrorResponse {
    return this.error(
      message,
      BusinessCode.FORBIDDEN,
      ErrorType.AUTHORIZATION_ERROR,
      undefined,
      undefined,
      req
    );
  }

  /**
   * 冲突错误响应
   */
  protected conflict(
    message: string = '资源冲突',
    req?: Request
  ): ApiErrorResponse {
    return this.error(
      message,
      BusinessCode.CONFLICT,
      ErrorType.BUSINESS_ERROR,
      undefined,
      undefined,
      req
    );
  }

  /**
   * 解析分页参数
   */
  protected parsePagination(query: any): { page: number; limit: number } {
    const page = Math.max(1, parseInt(query.page) || 1);
    const limit = Math.min(100, Math.max(1, parseInt(query.limit) || 10));
    
    return { page, limit };
  }

  /**
   * 解析排序参数
   */
  protected parseSort(query: any): { sort?: string; order: 'ASC' | 'DESC' } {
    const sort = query.sort;
    const order = query.order?.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
    
    return { sort, order };
  }

  /**
   * 解析日期范围参数
   */
  protected parseDateRange(query: any): { startDate?: Date; endDate?: Date } {
    const startDate = query.startDate ? new Date(query.startDate) : undefined;
    const endDate = query.endDate ? new Date(query.endDate) : undefined;
    
    return { startDate, endDate };
  }

  /**
   * 解析字段选择参数
   */
  protected parseFields(query: any): string[] | undefined {
    return query.fields ? query.fields.split(',').map((f: string) => f.trim()) : undefined;
  }
}
