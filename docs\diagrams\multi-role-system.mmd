graph TB
    %% 用户和角色关系
    subgraph "用户角色体系"
        U[用户 User]
        
        subgraph "全局角色 (可多选)"
            GR1[Super Admin]
            GR2[Admin] 
            GR3[Organizer]
            GR4[Artist]
            GR5[Judge]
            GR6[User]
        end
        
        U -.->|拥有多个| GR3
        U -.->|拥有多个| GR4
        U -.->|拥有多个| GR5
    end

    %% 项目权限体系
    subgraph "项目权限体系"
        P[项目 Project]
        
        subgraph "项目角色"
            PR1[Project Owner]
            PR2[Project Admin]
            PR3[Project Judge]
            PR4[Project Artist]
            PR5[Project Viewer]
            PR6[Project Moderator]
        end
        
        subgraph "项目权限"
            PP1[管理项目]
            PP2[邀请用户]
            PP3[管理作品]
            PP4[评分权限]
            PP5[查看评分]
            PP6[导出数据]
            PP7[内容审核]
            PP8[时间管理]
        end
        
        PR1 --> PP1
        PR1 --> PP2
        PR1 --> PP3
        PR1 --> PP5
        PR1 --> PP6
        PR1 --> PP7
        PR1 --> PP8
        
        PR2 --> PP1
        PR2 --> PP2
        PR2 --> PP3
        PR2 --> PP7
        
        PR3 --> PP4
        PR3 --> PP5
        
        PR6 --> PP3
        PR6 --> PP7
    end

    %% 权限检查流程
    subgraph "权限检查流程"
        PC1[请求访问]
        PC2{超级管理员?}
        PC3{系统管理员?}
        PC4{项目所有者?}
        PC5{项目角色权限?}
        PC6{全局角色权限?}
        PC7[允许访问]
        PC8[拒绝访问]
        
        PC1 --> PC2
        PC2 -->|是| PC7
        PC2 -->|否| PC3
        PC3 -->|是| PC7
        PC3 -->|否| PC4
        PC4 -->|是| PC7
        PC4 -->|否| PC5
        PC5 -->|有权限| PC7
        PC5 -->|无权限| PC6
        PC6 -->|有权限| PC7
        PC6 -->|无权限| PC8
    end

    %% 用户项目关系
    U -->|创建| P
    U -->|被邀请加入| P
    P -->|分配角色| PR1
    P -->|分配角色| PR2
    P -->|分配角色| PR3

    %% 样式定义
    classDef user fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef globalRole fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef project fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef projectRole fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef permission fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef process fill:#f1f8e9,stroke:#33691e,stroke-width:2px

    class U user
    class GR1,GR2,GR3,GR4,GR5,GR6 globalRole
    class P project
    class PR1,PR2,PR3,PR4,PR5,PR6 projectRole
    class PP1,PP2,PP3,PP4,PP5,PP6,PP7,PP8 permission
    class PC1,PC2,PC3,PC4,PC5,PC6,PC7,PC8 process
