"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RequestId = exports.UserAgent = exports.ClientIP = exports.SafeUser = exports.IsAuthenticated = exports.IsJudge = exports.IsOrganizer = exports.IsSuperAdmin = exports.IsAdmin = exports.UserRole = exports.UserId = exports.CurrentUser = void 0;
const common_1 = require("@nestjs/common");
exports.CurrentUser = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    if (!user) {
        return null;
    }
    return data ? user[data] : user;
});
exports.UserId = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    return user ? user.id : null;
});
exports.UserRole = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    return user ? user.role : null;
});
exports.IsAdmin = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    return user && (user.role === 'admin' || user.role === 'super_admin');
});
exports.IsSuperAdmin = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    return user && user.role === 'super_admin';
});
exports.IsOrganizer = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    return user && user.role === 'organizer';
});
exports.IsJudge = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    return user && user.role === 'judge';
});
exports.IsAuthenticated = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    return !!request.user;
});
exports.SafeUser = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    if (!user) {
        return null;
    }
    const { password, refreshToken, ...safeUser } = user;
    return safeUser;
});
exports.ClientIP = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    return request.ip ||
        request.connection.remoteAddress ||
        request.socket.remoteAddress ||
        (request.connection.socket ? request.connection.socket.remoteAddress : null) ||
        'unknown';
});
exports.UserAgent = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    return request.headers['user-agent'] || 'unknown';
});
exports.RequestId = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    return request.headers['x-request-id'] || 'unknown';
});
//# sourceMappingURL=user.decorator.js.map