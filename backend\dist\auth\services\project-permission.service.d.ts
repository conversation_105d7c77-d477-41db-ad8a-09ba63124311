import { Repository } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { UserProjectRole, ProjectRole } from '../../users/entities/user-roles.entity';
import { RoleManagementService } from './role-management.service';
export declare class ProjectPermissionService {
    private userRepository;
    private projectRoleRepository;
    private roleManagementService;
    constructor(userRepository: Repository<User>, projectRoleRepository: Repository<UserProjectRole>, roleManagementService: RoleManagementService);
    inviteUserToProject(projectId: string, userId: string, role: ProjectRole, permissions: any, invitedBy: string, metadata?: any): Promise<UserProjectRole>;
    acceptProjectInvitation(userId: string, projectId: string, role?: ProjectRole): Promise<void>;
    declineProjectInvitation(userId: string, projectId: string, role?: ProjectRole): Promise<void>;
    updateProjectRolePermissions(userId: string, projectId: string, permissions: any, updatedBy: string): Promise<void>;
    removeProjectMember(userId: string, projectId: string, removedBy: string, reason?: string): Promise<void>;
    getUserProjectRoles(userId: string, projectId: string): Promise<UserProjectRole[]>;
    getProjectMembers(projectId: string, filters?: {
        role?: ProjectRole;
        status?: string;
        page?: number;
        limit?: number;
    }): Promise<{
        data: UserProjectRole[];
        total: number;
        page: number;
        limit: number;
    }>;
    hasProjectPermission(userId: string, projectId: string, permission: string): Promise<boolean>;
    private checkPermission;
    mergePermissions(projectRoles: UserProjectRole[]): any;
    batchInviteUsers(projectId: string, invitations: Array<{
        userId: string;
        role: ProjectRole;
        permissions?: any;
        message?: string;
    }>, invitedBy: string): Promise<UserProjectRole[]>;
    getProjectPermissionStats(projectId: string): Promise<any>;
}
