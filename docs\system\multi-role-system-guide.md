# 多角色权限系统使用指南

## 🎯 系统概述

### **双重权限体系**
1. **全局角色** - 用户在整个平台的角色（可多选）
2. **项目角色** - 用户在特定项目中的角色和权限（可自定义）

### **核心特性**
- ✅ **多角色支持** - 用户可同时拥有多个全局角色
- ✅ **项目级权限** - 每个项目独立的权限配置
- ✅ **灵活权限分配** - 项目创建者可自定义成员权限
- ✅ **权限继承** - 全局管理员拥有所有项目权限
- ✅ **临时权限** - 支持项目内临时角色分配

## 🏗️ 角色体系架构

### **全局角色（可多选）**
```typescript
enum GlobalRole {
  SUPER_ADMIN = 'super_admin',    // 超级管理员
  ADMIN = 'admin',                // 系统管理员
  ORGANIZER = 'organizer',        // 办展方
  ARTIST = 'artist',              // 美工组/作者
  JUDGE = 'judge',                // 评委
  USER = 'user'                   // 普通用户
}
```

### **项目角色（项目内分配）**
```typescript
enum ProjectRole {
  PROJECT_OWNER = 'project_owner',         // 项目所有者
  PROJECT_ADMIN = 'project_admin',         // 项目管理员
  PROJECT_JUDGE = 'project_judge',         // 项目评委
  PROJECT_ARTIST = 'project_artist',       // 项目参与者
  PROJECT_VIEWER = 'project_viewer',       // 项目观察者
  PROJECT_MODERATOR = 'project_moderator', // 项目协调员
  PROJECT_ASSISTANT = 'project_assistant'  // 项目助理
}
```

## 🔧 使用示例

### **1. 多角色用户示例**

```typescript
// 用户可以同时拥有多个全局角色
const user = {
  id: 'user-123',
  username: 'zhang_san',
  globalRoles: ['artist', 'judge'],  // 既是艺术家又是评委
  
  // 在不同项目中有不同角色
  projectRoles: [
    {
      projectId: 'project-1',
      role: 'project_artist',     // 在项目1中是参与者
      permissions: { canScore: false }
    },
    {
      projectId: 'project-2', 
      role: 'project_judge',      // 在项目2中是评委
      permissions: { canScore: true }
    }
  ]
}
```

### **2. 项目权限配置示例**

```typescript
// 项目创建者邀请用户并自定义权限
@ProjectPermission('invite')
@Post('projects/:projectId/invite')
async inviteUser(@Body() inviteData: {
  userId: string;
  role: ProjectRole;
  permissions: {
    canManageProject: boolean;
    canInviteUsers: boolean;
    canManageArtworks: boolean;
    canScore: boolean;
    canViewScores: boolean;
    canExportData: boolean;
    canModerateContent: boolean;
    canManageSchedule: boolean;
    customPermissions: string[];
  };
}) {
  // 邀请逻辑
}
```

### **3. 权限检查示例**

```typescript
// 检查项目特定权限
@ProjectPermission('export')
@Get('projects/:projectId/export')
async exportData(@ProjectId() projectId: string) {
  // 只有有导出权限的用户可以访问
}

// 检查项目角色
@ProjectRoleAuth([ProjectRole.PROJECT_OWNER, ProjectRole.PROJECT_ADMIN])
@Patch('projects/:projectId/settings')
async updateSettings(@ProjectId() projectId: string) {
  // 只有项目所有者和管理员可以修改设置
}

// 条件权限逻辑
@ProjectMember()
@Get('projects/:projectId/data')
async getData(
  @IsProjectOwner() isOwner: boolean,
  @HasProjectPermission('view_scores') canViewScores: boolean
) {
  if (isOwner) {
    return this.getOwnerData();
  } else if (canViewScores) {
    return this.getScoresData();
  } else {
    return this.getBasicData();
  }
}
```

## 📋 典型业务场景

### **场景1: 全国书法大赛**

#### 角色分配
```typescript
// 中国书法协会（项目创建者）
const organizer = {
  globalRoles: ['organizer'],
  projectRole: 'project_owner',
  permissions: {
    canManageProject: true,
    canInviteUsers: true,
    canManageArtworks: true,
    canViewScores: true,
    canExportData: true,
    canModerateContent: true,
    canManageSchedule: true
  }
};

// 知名书法家（受邀评委）
const expertJudge = {
  globalRoles: ['artist', 'judge'],  // 既是艺术家又是评委
  projectRole: 'project_judge',
  permissions: {
    canScore: true,
    canViewScores: true,
    canModerateContent: false  // 不能审核内容
  }
};

// 参赛书法家
const participant = {
  globalRoles: ['artist'],
  projectRole: 'project_artist',
  permissions: {
    canScore: false,
    canViewScores: false  // 不能查看其他人评分
  }
};

// 协办方工作人员
const assistant = {
  globalRoles: ['user'],
  projectRole: 'project_assistant',
  permissions: {
    canManageArtworks: true,   // 可以管理作品
    canModerateContent: true,  // 可以审核内容
    canScore: false,
    canViewScores: false
  }
};
```

### **场景2: 高校学生比赛**

#### 灵活权限配置
```typescript
// 美术学院老师（项目管理员）
const teacher = {
  globalRoles: ['organizer', 'judge'],
  projectRole: 'project_admin',
  permissions: {
    canManageProject: true,
    canInviteUsers: true,
    canScore: true,           // 既管理又评分
    canViewScores: true,
    canExportData: true
  }
};

// 外聘专家（临时评委）
const guestJudge = {
  globalRoles: ['user'],      // 全局只是普通用户
  projectRole: 'project_judge',
  permissions: {
    canScore: true,
    canViewScores: true
  },
  expiresAt: '2024-06-30'     // 临时权限，有过期时间
};

// 学生（参赛者）
const student = {
  globalRoles: ['artist'],
  projectRole: 'project_artist',
  permissions: {
    canScore: false,
    canViewScores: false,
    customPermissions: ['view_comments']  // 可以查看评语
  }
};
```

## 🎨 权限装饰器使用

### **项目权限装饰器**

```typescript
// 1. 检查特定权限
@ProjectPermission('manage')
@Patch(':projectId')
async updateProject() {
  // 需要项目管理权限
}

// 2. 检查项目角色
@ProjectRoleAuth([ProjectRole.PROJECT_OWNER])
@Delete(':projectId')
async deleteProject() {
  // 只有项目所有者可以删除
}

// 3. 项目成员检查
@ProjectMember()
@Get(':projectId/info')
async getProjectInfo() {
  // 项目成员都可以查看
}

// 4. 便捷角色装饰器
@ProjectOwner()        // 项目所有者
@ProjectManager()      // 项目管理者（所有者+管理员）
@ProjectJudge()        // 项目评委
```

### **权限信息获取装饰器**

```typescript
@ProjectMember()
@Get(':projectId/my-info')
async getMyInfo(
  @ProjectId() projectId: string,                    // 项目ID
  @CurrentProjectRole() roles: ProjectRole[],       // 我的项目角色
  @CurrentProjectPermissions() permissions: any,    // 我的项目权限
  @IsProjectOwner() isOwner: boolean,               // 是否项目所有者
  @IsProjectManager() isManager: boolean,           // 是否项目管理者
  @HasProjectPermission('export') canExport: boolean // 是否有导出权限
) {
  return {
    projectId,
    myRoles: roles,
    myPermissions: permissions,
    isOwner,
    isManager,
    canExport
  };
}
```

## 🔄 权限管理流程

### **1. 用户获得全局角色**

```typescript
// 用户申请成为评委
await permissionService.addGlobalRole(
  userId,
  GlobalRole.JUDGE,
  adminId,
  {
    reason: '专业书法家，具有20年经验',
    certifications: ['中国书法家协会会员'],
    expiresAt: null  // 永久有效
  }
);
```

### **2. 项目创建者邀请用户**

```typescript
// 邀请用户加入项目并分配角色
await permissionService.inviteUserToProject(
  projectId,
  userId,
  ProjectRole.PROJECT_JUDGE,
  {
    canScore: true,
    canViewScores: true,
    canExportData: false,
    customPermissions: ['view_detailed_comments']
  },
  inviterId,
  {
    role_description: '书法专业评委',
    responsibilities: ['评审书法作品', '提供专业点评'],
    message: '诚邀您担任本次比赛的评委'
  }
);
```

### **3. 用户接受邀请**

```typescript
// 用户接受项目邀请
await permissionService.acceptProjectInvitation(userId, projectId);
```

### **4. 动态权限调整**

```typescript
// 项目管理者调整成员权限
await permissionService.updateProjectRolePermissions(
  userId,
  projectId,
  {
    canScore: true,
    canViewScores: true,
    canExportData: true,  // 新增导出权限
    customPermissions: ['moderate_comments']  // 新增评语审核权限
  }
);
```

## 📊 权限检查逻辑

### **权限检查优先级**
```
1. 超级管理员 → 拥有所有权限
2. 系统管理员 → 拥有大部分权限
3. 项目所有者 → 拥有项目内所有权限
4. 项目角色权限 → 根据具体配置检查
5. 全局角色权限 → 基础权限检查
```

### **权限合并规则**
```typescript
// 用户在项目中有多个角色时，权限使用 OR 逻辑合并
const mergedPermissions = {
  canManageProject: role1.canManageProject || role2.canManageProject,
  canScore: role1.canScore || role2.canScore,
  // ... 其他权限
};
```

## 🧪 测试示例

```typescript
describe('Multi-Role Permission System', () => {
  test('user with multiple global roles', async () => {
    // 用户同时是艺术家和评委
    await permissionService.addGlobalRole(userId, GlobalRole.ARTIST, adminId);
    await permissionService.addGlobalRole(userId, GlobalRole.JUDGE, adminId);
    
    const user = await userService.findOne(userId);
    expect(user.hasGlobalRole('artist')).toBe(true);
    expect(user.hasGlobalRole('judge')).toBe(true);
  });

  test('project permission inheritance', async () => {
    // 项目所有者应该拥有所有项目权限
    const hasPermission = await permissionService.hasProjectPermission(
      ownerId, projectId, 'any_permission'
    );
    expect(hasPermission).toBe(true);
  });

  test('custom project permissions', async () => {
    // 自定义项目权限应该正确工作
    await permissionService.inviteUserToProject(
      projectId, userId, ProjectRole.PROJECT_JUDGE,
      { canScore: true, customPermissions: ['special_feature'] },
      ownerId
    );
    
    const hasCustomPermission = await permissionService.hasProjectPermission(
      userId, projectId, 'special_feature'
    );
    expect(hasCustomPermission).toBe(true);
  });
});
```

这个多角色权限系统提供了极大的灵活性，既满足了复杂的业务需求，又保持了系统的安全性和可维护性。
