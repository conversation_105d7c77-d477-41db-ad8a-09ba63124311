import { SetMetadata, UseGuards, applyDecorators, createParamDecorator, ExecutionContext } from '@nestjs/common';
import { ApiBearerAuth, ApiUnauthorizedResponse, ApiForbiddenResponse } from '@nestjs/swagger';
import { RequiredAuthGuard } from '../guards/auth.guard';
import { ProjectPermissionGuard } from '../guards/project-permission.guard';
import { ProjectRole } from '../../users/entities/user-roles.entity';

/**
 * 项目权限装饰器
 * 检查用户在特定项目中的权限
 * 
 * @param permission - 需要的权限
 * 
 * @example
 * ```typescript
 * @ProjectPermission('manage')
 * @Patch(':projectId')
 * async updateProject(@Param('projectId') projectId: string) {
 *   // 只有项目管理者可以访问
 * }
 * ```
 */
export const ProjectPermission = (permission: string) => {
  return applyDecorators(
    SetMetadata('project_permission', permission),
    UseGuards(RequiredAuthGuard, ProjectPermissionGuard),
    ApiBearerAuth(),
    ApiUnauthorizedResponse({ description: '未授权访问' }),
    ApiForbiddenResponse({ description: '项目权限不足' }),
  );
};

/**
 * 项目角色装饰器
 * 检查用户在项目中是否有指定角色
 * 
 * @param roles - 允许的项目角色列表
 * 
 * @example
 * ```typescript
 * @ProjectRole([ProjectRole.PROJECT_OWNER, ProjectRole.PROJECT_ADMIN])
 * @Post(':projectId/invite')
 * async inviteUser(@Param('projectId') projectId: string) {
 *   // 只有项目所有者和管理员可以邀请用户
 * }
 * ```
 */
export const ProjectRoleAuth = (roles: ProjectRole[]) => {
  return applyDecorators(
    SetMetadata('project_roles', roles),
    UseGuards(RequiredAuthGuard, ProjectPermissionGuard),
    ApiBearerAuth(),
    ApiUnauthorizedResponse({ description: '未授权访问' }),
    ApiForbiddenResponse({ description: '项目角色权限不足' }),
  );
};

/**
 * 项目所有者装饰器
 * 只允许项目所有者访问
 * 
 * @example
 * ```typescript
 * @ProjectOwner()
 * @Delete(':projectId')
 * async deleteProject(@Param('projectId') projectId: string) {
 *   // 只有项目所有者可以删除项目
 * }
 * ```
 */
export const ProjectOwner = () => {
  return ProjectRoleAuth([ProjectRole.PROJECT_OWNER]);
};

/**
 * 项目管理者装饰器
 * 允许项目所有者和管理员访问
 * 
 * @example
 * ```typescript
 * @ProjectManager()
 * @Patch(':projectId/settings')
 * async updateSettings(@Param('projectId') projectId: string) {
 *   // 项目所有者和管理员可以修改设置
 * }
 * ```
 */
export const ProjectManager = () => {
  return ProjectRoleAuth([ProjectRole.PROJECT_OWNER, ProjectRole.PROJECT_ADMIN]);
};

/**
 * 项目评委装饰器
 * 允许项目评委访问
 * 
 * @example
 * ```typescript
 * @ProjectJudge()
 * @Post(':projectId/scores')
 * async createScore(@Param('projectId') projectId: string) {
 *   // 项目评委可以评分
 * }
 * ```
 */
export const ProjectJudge = () => {
  return ProjectRoleAuth([
    ProjectRole.PROJECT_JUDGE,
    ProjectRole.PROJECT_OWNER,
    ProjectRole.PROJECT_ADMIN
  ]);
};

/**
 * 项目成员装饰器
 * 允许项目的任何成员访问
 * 
 * @example
 * ```typescript
 * @ProjectMember()
 * @Get(':projectId/info')
 * async getProjectInfo(@Param('projectId') projectId: string) {
 *   // 项目成员可以查看项目信息
 * }
 * ```
 */
export const ProjectMember = () => {
  return applyDecorators(
    SetMetadata('project_member', true),
    UseGuards(RequiredAuthGuard, ProjectPermissionGuard),
    ApiBearerAuth(),
    ApiUnauthorizedResponse({ description: '未授权访问' }),
    ApiForbiddenResponse({ description: '非项目成员' }),
  );
};

/**
 * 项目ID参数装饰器
 * 从路径参数中提取项目ID
 * 
 * @example
 * ```typescript
 * @Get(':projectId/members')
 * async getMembers(@ProjectId() projectId: string) {
 *   return this.service.getMembers(projectId);
 * }
 * ```
 */
export const ProjectId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest();
    return request.params.projectId || request.params.id;
  },
);

/**
 * 当前用户项目角色装饰器
 * 获取当前用户在项目中的角色
 * 
 * @example
 * ```typescript
 * @Get(':projectId/my-role')
 * async getMyRole(@CurrentProjectRole() roles: ProjectRole[]) {
 *   return { roles };
 * }
 * ```
 */
export const CurrentProjectRole = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): ProjectRole[] => {
    const request = ctx.switchToHttp().getRequest();
    return request.projectRoles || [];
  },
);

/**
 * 当前用户项目权限装饰器
 * 获取当前用户在项目中的权限
 * 
 * @example
 * ```typescript
 * @Get(':projectId/my-permissions')
 * async getMyPermissions(@CurrentProjectPermissions() permissions: any) {
 *   return { permissions };
 * }
 * ```
 */
export const CurrentProjectPermissions = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): any => {
    const request = ctx.switchToHttp().getRequest();
    return request.projectPermissions || {};
  },
);

/**
 * 检查项目权限装饰器
 * 检查当前用户是否有特定项目权限
 * 
 * @example
 * ```typescript
 * @Get(':projectId/sensitive-data')
 * async getSensitiveData(
 *   @HasProjectPermission('export') canExport: boolean
 * ) {
 *   if (!canExport) {
 *     throw new ForbiddenException('无导出权限');
 *   }
 *   return this.service.getSensitiveData();
 * }
 * ```
 */
export const HasProjectPermission = (permission: string) => {
  return createParamDecorator(
    (data: unknown, ctx: ExecutionContext): boolean => {
      const request = ctx.switchToHttp().getRequest();
      const permissions = request.projectPermissions || {};
      
      switch (permission) {
        case 'manage':
          return permissions.canManageProject;
        case 'invite':
          return permissions.canInviteUsers;
        case 'artwork_manage':
          return permissions.canManageArtworks;
        case 'score':
          return permissions.canScore;
        case 'view_scores':
          return permissions.canViewScores;
        case 'export':
          return permissions.canExportData;
        case 'moderate':
          return permissions.canModerateContent;
        case 'schedule':
          return permissions.canManageSchedule;
        default:
          return permissions.customPermissions?.includes(permission) || false;
      }
    },
  )();
};

/**
 * 项目所有者检查装饰器
 * 检查当前用户是否是项目所有者
 * 
 * @example
 * ```typescript
 * @Get(':projectId/owner-data')
 * async getOwnerData(@IsProjectOwner() isOwner: boolean) {
 *   if (!isOwner) {
 *     throw new ForbiddenException('只有项目所有者可以访问');
 *   }
 *   return this.service.getOwnerData();
 * }
 * ```
 */
export const IsProjectOwner = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): boolean => {
    const request = ctx.switchToHttp().getRequest();
    const roles = request.projectRoles || [];
    return roles.includes(ProjectRole.PROJECT_OWNER);
  },
);

/**
 * 项目管理者检查装饰器
 * 检查当前用户是否是项目管理者（所有者或管理员）
 * 
 * @example
 * ```typescript
 * @Get(':projectId/admin-data')
 * async getAdminData(@IsProjectManager() isManager: boolean) {
 *   if (!isManager) {
 *     throw new ForbiddenException('需要管理权限');
 *   }
 *   return this.service.getAdminData();
 * }
 * ```
 */
export const IsProjectManager = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): boolean => {
    const request = ctx.switchToHttp().getRequest();
    const roles = request.projectRoles || [];
    return roles.includes(ProjectRole.PROJECT_OWNER) || 
           roles.includes(ProjectRole.PROJECT_ADMIN);
  },
);
