import { BaseController } from '../../common/base/base.controller';
import { RoleManagementService } from '../../auth/services/role-management.service';
import { ReviewRoleApplicationDto, AssignGlobalRoleDto, BatchAssignRolesDto, CreatePermissionTemplateDto, QueryRoleApplicationsDto } from '../../users/dto/role-management.dto';
import { GlobalRole } from '../../users/entities/user-roles.entity';
export declare class AdminRoleManagementController extends BaseController {
    private readonly roleManagementService;
    constructor(roleManagementService: RoleManagementService);
    getRoleApplications(query: QueryRoleApplicationsDto, req: any): Promise<import("../../common/base/base.controller").PaginatedResponse<import("../../users/entities/user-roles.entity").RoleApplication>>;
    reviewRoleApplication(applicationId: string, reviewDto: ReviewRoleApplicationDto, reviewerId: string, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        application: import("../../users/entities/user-roles.entity").RoleApplication;
        action: string;
    }> | import("../../common/base/base.controller").ApiErrorResponse>;
    assignGlobalRole(assignDto: AssignGlobalRoleDto, grantedBy: string, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<import("../../users/entities/user-roles.entity").UserGlobalRole>>;
    batchAssignRoles(batchDto: BatchAssignRolesDto, grantedBy: string, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        successful: number;
        total: number;
        results: import("../../users/entities/user-roles.entity").UserGlobalRole[];
    }>>;
    removeGlobalRole(userId: string, role: GlobalRole, reason: string, removedBy: string, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<null>>;
    getUserRoles(userId: string, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        userId: string;
        roles: import("../../users/entities/user-roles.entity").UserGlobalRole[];
        totalRoles: number;
    }>>;
    getPermissionTemplates(role: string, req: any): Promise<import("../../common/base/base.controller").ApiResponse<import("../../users/entities/user-roles.entity").PermissionTemplate[]>>;
    createPermissionTemplate(templateDto: CreatePermissionTemplateDto, req: any): Promise<import("../../common/base/base.controller").ApiResponse<import("../../users/entities/user-roles.entity").PermissionTemplate>>;
    getRoleStatistics(req: any): Promise<import("../../common/base/base.controller").ApiResponse<any>>;
    getUsersRoleOverview(query: {
        page?: number;
        limit?: number;
        role?: GlobalRole;
        search?: string;
    }, req: any): Promise<import("../../common/base/base.controller").PaginatedResponse<never>>;
    exportRoleData(format: "excel" | "csv" | undefined, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        format: "excel" | "csv";
        downloadUrl: string;
        generatedAt: Date;
    }>>;
}
