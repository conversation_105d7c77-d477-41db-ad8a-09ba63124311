"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MpScoresController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const swagger_1 = require("@nestjs/swagger");
const base_controller_1 = require("../../common/base/base.controller");
const scores_service_1 = require("../../scores/scores.service");
const create_score_dto_1 = require("../../scores/dto/create-score.dto");
const update_score_dto_1 = require("../../scores/dto/update-score.dto");
const create_comment_dto_1 = require("../../scores/dto/create-comment.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../../auth/guards/roles.guard");
const roles_decorator_1 = require("../../auth/decorators/roles.decorator");
const user_entity_1 = require("../../users/entities/user.entity");
let MpScoresController = class MpScoresController extends base_controller_1.BaseController {
    scoresService;
    constructor(scoresService) {
        super();
        this.scoresService = scoresService;
    }
    async create(createScoreDto, req) {
        try {
            const score = await this.scoresService.create(createScoreDto, req.user.id);
            return this.created(score, '评分创建成功', req);
        }
        catch (error) {
            if (error.message.includes('已经评分')) {
                return this.conflict('您已经对此作品进行过评分', req);
            }
            if (error.message.includes('验证失败')) {
                return this.validationError(error.details, error.message, req);
            }
            if (error.message.includes('时间已过')) {
                return this.error('评分时间已过，无法进行评分', this.BusinessCode.SCORE_TIME_EXPIRED, 'BUSINESS_ERROR', undefined, undefined, req);
            }
            throw error;
        }
    }
    async findMyScores(projectId, query, req) {
        const { page, limit } = this.parsePagination(query);
        const result = await this.scoresService.findMyScores(projectId, req.user.id, {
            page,
            limit,
            status: query.status
        });
        return this.paginated(result.data, result.total, result.page, result.limit, '我的评分列表获取成功', req);
    }
    async findOne(id, req) {
        try {
            const score = await this.scoresService.findOne(id, req.user);
            return this.success(score, '评分详情获取成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('评分记录不存在', req);
            }
            if (error.message.includes('权限')) {
                return this.forbidden(error.message, req);
            }
            throw error;
        }
    }
    async update(id, updateScoreDto, req) {
        try {
            const score = await this.scoresService.update(id, updateScoreDto, req.user);
            return this.success(score, '评分更新成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('评分记录不存在', req);
            }
            if (error.message.includes('权限')) {
                return this.forbidden('只能修改自己的评分', req);
            }
            if (error.message.includes('已提交')) {
                return this.error('评分已提交，无法修改', 400, 'BUSINESS_ERROR', undefined, undefined, req);
            }
            throw error;
        }
    }
    async submit(id, req) {
        try {
            const score = await this.scoresService.submit(id, req.user);
            return this.success(score, '评分提交成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('评分记录不存在', req);
            }
            if (error.message.includes('权限')) {
                return this.forbidden('只能提交自己的评分', req);
            }
            if (error.message.includes('不完整')) {
                return this.error('评分信息不完整，无法提交', 400, 'BUSINESS_ERROR', undefined, undefined, req);
            }
            throw error;
        }
    }
    async remove(id, req) {
        try {
            await this.scoresService.remove(id, req.user);
            return this.success(null, '评分删除成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('评分记录不存在', req);
            }
            if (error.message.includes('权限')) {
                return this.forbidden('只能删除自己的评分', req);
            }
            throw error;
        }
    }
    async createComment(scoreId, createCommentDto, audio, req) {
        try {
            const comment = await this.scoresService.createComment(scoreId, createCommentDto, req.user, audio);
            return this.created(comment, '评语添加成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('评分记录不存在', req);
            }
            if (error.message.includes('权限')) {
                return this.forbidden('只能为自己的评分添加评语', req);
            }
            throw error;
        }
    }
    async getComments(scoreId, req) {
        try {
            const comments = await this.scoresService.getComments(scoreId, req.user);
            return this.success(comments, '评语列表获取成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('评分记录不存在', req);
            }
            if (error.message.includes('权限')) {
                return this.forbidden(error.message, req);
            }
            throw error;
        }
    }
    async updateComment(commentId, updateData, audio, req) {
        try {
            const comment = await this.scoresService.updateComment(commentId, updateData, req.user, audio);
            return this.success(comment, '评语更新成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('评语不存在', req);
            }
            if (error.message.includes('权限')) {
                return this.forbidden('只能修改自己的评语', req);
            }
            throw error;
        }
    }
    async removeComment(commentId, req) {
        try {
            await this.scoresService.removeComment(commentId, req.user);
            return this.success(null, '评语删除成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('评语不存在', req);
            }
            if (error.message.includes('权限')) {
                return this.forbidden('只能删除自己的评语', req);
            }
            throw error;
        }
    }
    async getScoreProgress(projectId, req) {
        try {
            const progress = await this.scoresService.getScoreProgress(projectId, req.user.id);
            return this.success(progress, '评分进度获取成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('项目不存在', req);
            }
            throw error;
        }
    }
    async batchSave(scores, req) {
        try {
            const result = await this.scoresService.batchSave(scores, req.user.id);
            return this.created(result, '批量保存成功', req);
        }
        catch (error) {
            if (error.message.includes('验证失败')) {
                return this.validationError(error.details, error.message, req);
            }
            throw error;
        }
    }
};
exports.MpScoresController = MpScoresController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建评分' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '评分创建成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数验证失败' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '重复评分' }),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.JUDGE),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_score_dto_1.CreateScoreDto, Object]),
    __metadata("design:returntype", Promise)
], MpScoresController.prototype, "create", null);
__decorate([
    (0, common_1.Get)('my/:projectId'),
    (0, swagger_1.ApiOperation)({ summary: '获取我的评分列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.JUDGE),
    __param(0, (0, common_1.Param)('projectId')),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], MpScoresController.prototype, "findMyScores", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取评分详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '评分不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MpScoresController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新评分' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '评分不存在' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: '权限不足' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '评分已提交，无法修改' }),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.JUDGE),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_score_dto_1.UpdateScoreDto, Object]),
    __metadata("design:returntype", Promise)
], MpScoresController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/submit'),
    (0, swagger_1.ApiOperation)({ summary: '提交评分' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '提交成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '评分信息不完整' }),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.JUDGE),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MpScoresController.prototype, "submit", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除评分' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '评分不存在' }),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.JUDGE),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MpScoresController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':scoreId/comments'),
    (0, swagger_1.ApiOperation)({ summary: '添加评语' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '评语添加成功' }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('audio')),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.JUDGE),
    __param(0, (0, common_1.Param)('scoreId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.UploadedFile)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, create_comment_dto_1.CreateCommentDto, typeof (_b = typeof Express !== "undefined" && (_a = Express.Multer) !== void 0 && _a.File) === "function" ? _b : Object, Object]),
    __metadata("design:returntype", Promise)
], MpScoresController.prototype, "createComment", null);
__decorate([
    (0, common_1.Get)(':scoreId/comments'),
    (0, swagger_1.ApiOperation)({ summary: '获取评语列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Param)('scoreId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MpScoresController.prototype, "getComments", null);
__decorate([
    (0, common_1.Patch)('comments/:commentId'),
    (0, swagger_1.ApiOperation)({ summary: '更新评语' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('audio')),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.JUDGE),
    __param(0, (0, common_1.Param)('commentId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.UploadedFile)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, typeof (_d = typeof Express !== "undefined" && (_c = Express.Multer) !== void 0 && _c.File) === "function" ? _d : Object, Object]),
    __metadata("design:returntype", Promise)
], MpScoresController.prototype, "updateComment", null);
__decorate([
    (0, common_1.Delete)('comments/:commentId'),
    (0, swagger_1.ApiOperation)({ summary: '删除评语' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.JUDGE),
    __param(0, (0, common_1.Param)('commentId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MpScoresController.prototype, "removeComment", null);
__decorate([
    (0, common_1.Get)('progress/:projectId'),
    (0, swagger_1.ApiOperation)({ summary: '获取评分进度' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.JUDGE),
    __param(0, (0, common_1.Param)('projectId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MpScoresController.prototype, "getScoreProgress", null);
__decorate([
    (0, common_1.Post)('batch'),
    (0, swagger_1.ApiOperation)({ summary: '批量保存评分' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '批量保存成功' }),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.JUDGE),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, Object]),
    __metadata("design:returntype", Promise)
], MpScoresController.prototype, "batchSave", null);
exports.MpScoresController = MpScoresController = __decorate([
    (0, swagger_1.ApiTags)('小程序端-评分管理'),
    (0, common_1.Controller)('mp/scores'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.JUDGE, user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.SUPER_ADMIN),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [scores_service_1.ScoresService])
], MpScoresController);
//# sourceMappingURL=mp-scores.controller.js.map