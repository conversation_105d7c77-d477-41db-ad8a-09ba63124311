import { ApplicationsService } from './applications.service';
import { CreateApplicationDto } from './dto/create-application.dto';
import { UpdateApplicationDto } from './dto/update-application.dto';
import { ReviewApplicationDto } from './dto/review-application.dto';
import { ApplicationStatus } from './entities/organizer-application.entity';
export declare class ApplicationsController {
    private readonly applicationsService;
    constructor(applicationsService: ApplicationsService);
    create(createApplicationDto: CreateApplicationDto, req: any): Promise<import("./entities/organizer-application.entity").OrganizerApplication>;
    findAll(page?: number, limit?: number, status?: ApplicationStatus, organizationType?: string): Promise<{
        data: any;
        total: any;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    findMyApplications(req: any): Promise<import("./entities/organizer-application.entity").OrganizerApplication[]>;
    findOne(id: string, req: any): Promise<import("./entities/organizer-application.entity").OrganizerApplication>;
    update(id: string, updateApplicationDto: UpdateApplicationDto, req: any): Promise<import("./entities/organizer-application.entity").OrganizerApplication>;
    review(id: string, reviewApplicationDto: ReviewApplicationDto, req: any): Promise<import("./entities/organizer-application.entity").OrganizerApplication>;
    remove(id: string, req: any): Promise<void>;
    getStats(): Promise<{
        total: any;
        byStatus: any;
        recent: any;
    }>;
}
