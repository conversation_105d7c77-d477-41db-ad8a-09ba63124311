import { BaseController } from '../../common/base/base.controller';
import { User } from '../../users/entities/user.entity';
import { RoleManagementService } from '../../auth/services/role-management.service';
import { ProjectPermissionService } from '../../auth/services/project-permission.service';
import { ApplyGlobalRoleDto } from '../../users/dto/role-management.dto';
export declare class CommonRoleApplicationsController extends BaseController {
    private readonly roleManagementService;
    private readonly projectPermissionService;
    constructor(roleManagementService: RoleManagementService, projectPermissionService: ProjectPermissionService);
    applyForGlobalRole(applyDto: ApplyGlobalRoleDto, userId: string, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        application: import("../../users/entities/user-roles.entity").RoleApplication;
        targetRole: import("../../users/entities/user-roles.entity").GlobalRole;
        status: string;
        message: string;
    }> | import("../../common/base/base.controller").ApiErrorResponse>;
    getMyApplications(userId: string, query: {
        page?: number;
        limit?: number;
        status?: string;
    }, req: any): Promise<import("../../common/base/base.controller").PaginatedResponse<import("../../users/entities/user-roles.entity").RoleApplication>>;
    getMyGlobalRoles(userId: string, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        userId: string;
        roles: {
            id: string;
            role: import("../../users/entities/user-roles.entity").GlobalRole;
            isActive: boolean;
            metadata: {
                grantedBy?: string;
                grantedAt?: Date;
                expiresAt?: Date;
                reason?: string;
                certifications?: string[];
            };
            createdAt: Date;
        }[];
        totalRoles: number;
        activeRoles: number;
    }>>;
    acceptProjectInvitation(projectId: string, userId: string, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<{
        projectId: string;
        userId: string;
        status: string;
        message: string;
    }>>;
    declineProjectInvitation(projectId: string, userId: string, req: any): Promise<import("../../common/base/base.controller").ApiErrorResponse | import("../../common/base/base.controller").ApiResponse<{
        projectId: string;
        userId: string;
        status: string;
    }>>;
    getMyProjectInvitations(userId: string, query: {
        status?: string;
        page?: number;
        limit?: number;
    }, req: any): Promise<import("../../common/base/base.controller").PaginatedResponse<never>>;
    getMyProjects(userId: string, query: {
        role?: string;
        status?: string;
        page?: number;
        limit?: number;
    }, req: any): Promise<import("../../common/base/base.controller").PaginatedResponse<never>>;
    getRoleApplicationGuide(user: User | null, req: any): Promise<import("../../common/base/base.controller").ApiResponse<{
        roles: {
            role: string;
            name: string;
            description: string;
            requirements: string[];
            applicationFields: string[];
        }[];
        currentUser: {
            id: string;
            username: string;
            currentRoles: string[];
            canApplyFor: string[];
        } | null;
        applicationProcess: string[];
    }>>;
    private getAvailableRoles;
}
