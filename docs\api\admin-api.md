# 系统管理端 API 接口规范

## 概述
系统管理端供超级管理员和管理员使用，包含用户管理、申请审批、系统配置、数据统计等功能。

## 认证接口

### 管理员登录
```http
POST /auth/login
```

**请求体**:
```json
{
  "username": "<EMAIL>",
  "password": "admin123"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
    "user": {
      "id": "uuid",
      "username": "<EMAIL>",
      "nickname": "系统管理员",
      "role": "super_admin",
      "avatarUrl": "https://..."
    }
  }
}
```

## 用户管理接口

### 获取用户列表
```http
GET /users?page=1&limit=10&role=organizer&status=1&search=张
Authorization: Bearer {token}
```

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `role`: 用户角色筛选
- `status`: 用户状态筛选 (1-正常, 0-禁用)
- `search`: 搜索关键词（用户名、昵称、真实姓名）

**响应**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "user_uuid",
        "username": "organizer001",
        "nickname": "中国书法协会",
        "realName": "张主任",
        "email": "<EMAIL>",
        "phone": "138****8888",
        "role": "organizer",
        "loginType": "website",
        "status": 1,
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-15T10:30:00Z"
      }
    ],
    "total": 150,
    "page": 1,
    "limit": 10,
    "totalPages": 15
  }
}
```

### 获取用户详情
```http
GET /users/{userId}
Authorization: Bearer {token}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "user_uuid",
    "username": "organizer001",
    "nickname": "中国书法协会",
    "realName": "张主任",
    "email": "<EMAIL>",
    "phone": "138****8888",
    "avatarUrl": "https://...",
    "role": "organizer",
    "loginType": "website",
    "status": 1,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

### 创建用户
```http
POST /users
Authorization: Bearer {token}
```

**请求体**:
```json
{
  "username": "newuser001",
  "email": "<EMAIL>",
  "password": "password123",
  "nickname": "新用户",
  "realName": "李用户",
  "phone": "139****9999",
  "role": "organizer",
  "loginType": "website"
}
```

### 更新用户信息
```http
PATCH /users/{userId}
Authorization: Bearer {token}
```

**请求体**:
```json
{
  "nickname": "更新后的昵称",
  "realName": "更新后的真实姓名",
  "email": "<EMAIL>",
  "phone": "138****0000"
}
```

### 更新用户状态
```http
PATCH /users/{userId}/status
Authorization: Bearer {token}
```

**请求体**:
```json
{
  "status": 0  // 1-正常, 0-禁用
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "user_uuid",
    "status": 0,
    "updatedAt": "2024-01-15T10:30:00Z"
  },
  "message": "用户状态更新成功"
}
```

### 更新用户角色
```http
PATCH /users/{userId}/role
Authorization: Bearer {token}
```

**请求体**:
```json
{
  "role": "admin"  // super_admin, admin, organizer, artist, judge, user
}
```

**权限**: 仅 super_admin

### 删除用户
```http
DELETE /users/{userId}
Authorization: Bearer {token}
```

**权限**: 仅 super_admin

## 申请管理接口

### 获取申请列表
```http
GET /applications?page=1&limit=10&status=pending&type=company&search=书法
Authorization: Bearer {token}
```

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `status`: 申请状态 (pending, reviewing, approved, rejected)
- `type`: 机构类型 (government, association, company, school, individual)
- `search`: 搜索关键词（机构名称、联系人）

**响应**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "application_uuid",
        "user": {
          "id": "user_uuid",
          "username": "applicant001",
          "nickname": "申请人"
        },
        "organizationName": "中华书法文化协会",
        "organizationType": "association",
        "contactPerson": "李主任",
        "contactPhone": "138****8888",
        "contactEmail": "<EMAIL>",
        "status": "pending",
        "appliedAt": "2024-01-10T09:00:00Z"
      }
    ],
    "total": 50,
    "page": 1,
    "limit": 10,
    "totalPages": 5
  }
}
```

### 获取申请详情
```http
GET /applications/{applicationId}
Authorization: Bearer {token}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "application_uuid",
    "user": {
      "id": "user_uuid",
      "username": "applicant001",
      "nickname": "申请人",
      "email": "<EMAIL>"
    },
    "organizationName": "中华书法文化协会",
    "organizationType": "association",
    "legalPerson": "王会长",
    "contactPerson": "李主任",
    "contactPhone": "138****8888",
    "contactEmail": "<EMAIL>",
    "address": "北京市朝阳区文化街123号",
    "businessLicense": "https://...",
    "qualificationDocs": ["https://...", "https://..."],
    "introduction": "协会成立于1990年，致力于传承和发扬中华书法文化...",
    "experience": "曾成功举办多届全国性书法大赛...",
    "applicationReason": "希望通过平台举办更多优质的书法活动...",
    "status": "pending",
    "appliedAt": "2024-01-10T09:00:00Z"
  }
}
```

### 审批申请
```http
PATCH /applications/{applicationId}/review
Authorization: Bearer {token}
```

**请求体**:
```json
{
  "status": "approved",  // approved, rejected
  "reviewComment": "申请材料齐全，机构资质符合要求，同意通过申请。"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "application_uuid",
    "status": "approved",
    "reviewComment": "申请材料齐全，机构资质符合要求，同意通过申请。",
    "reviewerId": "admin_uuid",
    "reviewedAt": "2024-01-15T14:30:00Z"
  },
  "message": "申请审批成功"
}
```

### 获取申请统计
```http
GET /applications/stats/overview
Authorization: Bearer {token}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "total": 100,
    "byStatus": {
      "pending": 20,
      "reviewing": 5,
      "approved": 60,
      "rejected": 15
    },
    "recent": [
      {
        "id": "application_uuid",
        "organizationName": "新申请机构",
        "contactPerson": "联系人",
        "status": "pending",
        "appliedAt": "2024-01-15T10:00:00Z"
      }
    ]
  }
}
```

## 项目管理接口

### 获取所有项目列表
```http
GET /projects?page=1&limit=10&status=collecting&organizer={organizerId}&search=书法
Authorization: Bearer {token}
```

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `status`: 项目状态
- `organizer`: 办展方ID
- `search`: 搜索关键词（项目名称、描述）

### 获取项目详情
```http
GET /projects/{projectId}
Authorization: Bearer {token}
```

### 更新项目状态
```http
PATCH /projects/{projectId}/status
Authorization: Bearer {token}
```

**请求体**:
```json
{
  "status": "finished"
}
```

### 删除项目
```http
DELETE /projects/{projectId}
Authorization: Bearer {token}
```

**权限**: 仅 super_admin

## 系统配置接口

### 获取系统设置列表
```http
GET /system/settings?page=1&limit=10&public=true
Authorization: Bearer {token}
```

### 获取系统设置详情
```http
GET /system/settings/{key}
Authorization: Bearer {token}
```

### 更新系统设置
```http
PATCH /system/settings/{key}
Authorization: Bearer {token}
```

**请求体**:
```json
{
  "value": "new_value",
  "description": "设置描述"
}
```

### 创建系统设置
```http
POST /system/settings
Authorization: Bearer {token}
```

**请求体**:
```json
{
  "key": "max_file_size",
  "value": "10485760",
  "type": "number",
  "description": "最大文件上传大小（字节）",
  "isPublic": false
}
```

## 数据统计接口

### 获取系统概览统计
```http
GET /stats/overview
Authorization: Bearer {token}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "users": {
      "total": 1000,
      "organizers": 50,
      "judges": 200,
      "activeToday": 150
    },
    "projects": {
      "total": 100,
      "active": 20,
      "finished": 70
    },
    "artworks": {
      "total": 5000,
      "thisMonth": 500
    },
    "scores": {
      "total": 15000,
      "thisMonth": 2000
    }
  }
}
```

### 获取用户统计
```http
GET /stats/users?period=month&startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer {token}
```

### 获取项目统计
```http
GET /stats/projects?period=month&startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer {token}
```

### 获取作品统计
```http
GET /stats/artworks?period=month&startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer {token}
```

## 系统日志接口

### 获取操作日志
```http
GET /logs/operations?page=1&limit=10&userId={userId}&action=create&startDate=2024-01-01
Authorization: Bearer {token}
```

### 获取错误日志
```http
GET /logs/errors?page=1&limit=10&level=error&startDate=2024-01-01
Authorization: Bearer {token}
```

## 数据导出接口

### 导出用户数据
```http
GET /export/users?format=excel&role=organizer&status=1
Authorization: Bearer {token}
```

### 导出项目数据
```http
GET /export/projects?format=excel&status=finished&startDate=2024-01-01
Authorization: Bearer {token}
```

### 导出申请数据
```http
GET /export/applications?format=excel&status=approved&startDate=2024-01-01
Authorization: Bearer {token}
```

## 系统维护接口

### 数据库备份
```http
POST /system/backup
Authorization: Bearer {token}
```

**权限**: 仅 super_admin

### 清理临时文件
```http
POST /system/cleanup
Authorization: Bearer {token}
```

**权限**: 仅 super_admin

### 系统健康检查
```http
GET /system/health
Authorization: Bearer {token}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| USER_001 | 用户不存在 |
| USER_002 | 用户名已存在 |
| USER_003 | 邮箱已存在 |
| APPLICATION_001 | 申请不存在 |
| APPLICATION_002 | 申请状态不允许操作 |
| SYSTEM_001 | 系统设置不存在 |
| SYSTEM_002 | 系统维护中 |
| AUTH_001 | 登录失败 |
| AUTH_002 | Token已过期 |
| AUTH_003 | 权限不足 |
| EXPORT_001 | 导出失败 |
| BACKUP_001 | 备份失败 |
