"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryProjectMembersDto = exports.QueryRoleApplicationsDto = exports.CreatePermissionTemplateDto = exports.RemoveProjectMemberDto = exports.UpdateProjectPermissionsDto = exports.BatchInviteUsersDto = exports.InviteUserToProjectDto = exports.BatchAssignRolesDto = exports.AssignGlobalRoleDto = exports.ReviewRoleApplicationDto = exports.ApplyGlobalRoleDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const user_roles_entity_1 = require("../entities/user-roles.entity");
class ApplyGlobalRoleDto {
    targetRole;
    applicationData;
}
exports.ApplyGlobalRoleDto = ApplyGlobalRoleDto;
__decorate([
    (0, swagger_1.ApiProperty)({ enum: user_roles_entity_1.GlobalRole, description: '申请的角色' }),
    (0, class_validator_1.IsEnum)(user_roles_entity_1.GlobalRole),
    __metadata("design:type", String)
], ApplyGlobalRoleDto.prototype, "targetRole", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '申请材料和信息' }),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ApplyGlobalRoleDto.prototype, "applicationData", void 0);
class ReviewRoleApplicationDto {
    approved;
    reviewComment;
}
exports.ReviewRoleApplicationDto = ReviewRoleApplicationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否批准' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ReviewRoleApplicationDto.prototype, "approved", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '审核意见' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ReviewRoleApplicationDto.prototype, "reviewComment", void 0);
class AssignGlobalRoleDto {
    userId;
    role;
    metadata;
}
exports.AssignGlobalRoleDto = AssignGlobalRoleDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], AssignGlobalRoleDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: user_roles_entity_1.GlobalRole, description: '角色' }),
    (0, class_validator_1.IsEnum)(user_roles_entity_1.GlobalRole),
    __metadata("design:type", String)
], AssignGlobalRoleDto.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '角色元数据' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], AssignGlobalRoleDto.prototype, "metadata", void 0);
class BatchAssignRolesDto {
    assignments;
}
exports.BatchAssignRolesDto = BatchAssignRolesDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: [AssignGlobalRoleDto], description: '角色分配列表' }),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], BatchAssignRolesDto.prototype, "assignments", void 0);
class InviteUserToProjectDto {
    userId;
    role;
    permissions;
    message;
    roleDescription;
    responsibilities;
}
exports.InviteUserToProjectDto = InviteUserToProjectDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], InviteUserToProjectDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: user_roles_entity_1.ProjectRole, description: '项目角色' }),
    (0, class_validator_1.IsEnum)(user_roles_entity_1.ProjectRole),
    __metadata("design:type", String)
], InviteUserToProjectDto.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '自定义权限配置' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], InviteUserToProjectDto.prototype, "permissions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '邀请消息' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], InviteUserToProjectDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '角色描述' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], InviteUserToProjectDto.prototype, "roleDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '职责列表' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], InviteUserToProjectDto.prototype, "responsibilities", void 0);
class BatchInviteUsersDto {
    invitations;
}
exports.BatchInviteUsersDto = BatchInviteUsersDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: [InviteUserToProjectDto], description: '邀请列表' }),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], BatchInviteUsersDto.prototype, "invitations", void 0);
class UpdateProjectPermissionsDto {
    permissions;
}
exports.UpdateProjectPermissionsDto = UpdateProjectPermissionsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '权限配置' }),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], UpdateProjectPermissionsDto.prototype, "permissions", void 0);
class RemoveProjectMemberDto {
    reason;
}
exports.RemoveProjectMemberDto = RemoveProjectMemberDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '移除原因' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RemoveProjectMemberDto.prototype, "reason", void 0);
class CreatePermissionTemplateDto {
    name;
    role;
    permissions;
    description;
    isDefault;
}
exports.CreatePermissionTemplateDto = CreatePermissionTemplateDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '模板名称' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePermissionTemplateDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: user_roles_entity_1.ProjectRole, description: '适用角色' }),
    (0, class_validator_1.IsEnum)(user_roles_entity_1.ProjectRole),
    __metadata("design:type", String)
], CreatePermissionTemplateDto.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '权限配置' }),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreatePermissionTemplateDto.prototype, "permissions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '模板描述' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePermissionTemplateDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '是否为默认模板' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreatePermissionTemplateDto.prototype, "isDefault", void 0);
class QueryRoleApplicationsDto {
    status;
    targetRole;
    userId;
    page;
    limit;
}
exports.QueryRoleApplicationsDto = QueryRoleApplicationsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ enum: ['pending', 'reviewing', 'approved', 'rejected'], description: '申请状态' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['pending', 'reviewing', 'approved', 'rejected']),
    __metadata("design:type", String)
], QueryRoleApplicationsDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ enum: user_roles_entity_1.GlobalRole, description: '目标角色' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(user_roles_entity_1.GlobalRole),
    __metadata("design:type", String)
], QueryRoleApplicationsDto.prototype, "targetRole", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '用户ID' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], QueryRoleApplicationsDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '页码', default: 1 }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], QueryRoleApplicationsDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '每页数量', default: 10 }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], QueryRoleApplicationsDto.prototype, "limit", void 0);
class QueryProjectMembersDto {
    role;
    status;
    page;
    limit;
}
exports.QueryProjectMembersDto = QueryProjectMembersDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ enum: user_roles_entity_1.ProjectRole, description: '项目角色' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(user_roles_entity_1.ProjectRole),
    __metadata("design:type", String)
], QueryProjectMembersDto.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ enum: ['pending', 'accepted', 'declined', 'removed'], description: '状态' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['pending', 'accepted', 'declined', 'removed']),
    __metadata("design:type", String)
], QueryProjectMembersDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '页码', default: 1 }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], QueryProjectMembersDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '每页数量', default: 50 }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], QueryProjectMembersDto.prototype, "limit", void 0);
//# sourceMappingURL=role-management.dto.js.map