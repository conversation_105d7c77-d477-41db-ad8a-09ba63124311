# 小程序端 API 接口规范

## 概述
小程序端主要供评委和普通用户使用，包含项目浏览、作品查看、评分功能等。

## 认证接口

### 微信登录
```http
POST /auth/wechat-login
```

**请求体**:
```json
{
  "code": "wx_login_code",
  "userInfo": {
    "nickname": "张评委",
    "avatarUrl": "https://wx.qlogo.cn/..."
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
    "user": {
      "id": "uuid",
      "nickname": "张评委",
      "role": "judge",
      "avatarUrl": "https://wx.qlogo.cn/..."
    }
  }
}
```

### 获取用户信息
```http
GET /auth/profile
Authorization: Bearer {token}
```

### 刷新Token
```http
POST /auth/refresh
Authorization: Bearer {token}
```

## 项目相关接口

### 获取公开项目列表
```http
GET /projects?public=true&status=judging&page=1&limit=10
```

**响应**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "project_uuid",
        "name": "2024年春季书法大赛",
        "description": "面向全国的书法作品征集...",
        "coverImage": "https://...",
        "status": "judging",
        "collectStartTime": "2024-01-01T00:00:00Z",
        "collectEndTime": "2024-01-31T23:59:59Z",
        "judgeStartTime": "2024-02-01T00:00:00Z",
        "judgeEndTime": "2024-02-15T23:59:59Z",
        "organizer": {
          "id": "organizer_uuid",
          "organizationName": "中国书法协会"
        }
      }
    ],
    "total": 50,
    "page": 1,
    "limit": 10,
    "totalPages": 5
  }
}
```

### 获取项目详情
```http
GET /projects/{projectId}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "project_uuid",
    "name": "2024年春季书法大赛",
    "description": "详细描述...",
    "coverImage": "https://...",
    "status": "judging",
    "qrCode": "https://...",
    "collectStartTime": "2024-01-01T00:00:00Z",
    "collectEndTime": "2024-01-31T23:59:59Z",
    "judgeStartTime": "2024-02-01T00:00:00Z",
    "judgeEndTime": "2024-02-15T23:59:59Z",
    "displayStartTime": "2024-02-16T00:00:00Z",
    "displayEndTime": "2024-03-16T23:59:59Z",
    "organizer": {
      "id": "organizer_uuid",
      "organizationName": "中国书法协会",
      "contactPerson": "李主任",
      "contactPhone": "138****8888"
    }
  }
}
```

## 作品相关接口

### 获取项目作品列表
```http
GET /artworks/project/{projectId}?page=1&limit=20&status=approved
```

**响应**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "artwork_uuid",
        "artworkNo": "A001-0001",
        "title": "临王羲之兰亭序",
        "description": "临摹王羲之兰亭序...",
        "size": "138cm×69cm",
        "technique": "行书",
        "creationYear": 2024,
        "imageUrl": "https://...",
        "thumbnailUrl": "https://...",
        "authorName": "王书法",
        "tags": ["行书", "临帖", "王羲之"],
        "status": "approved",
        "uploadTime": "2024-01-15T10:30:00Z"
      }
    ],
    "total": 200,
    "page": 1,
    "limit": 20,
    "totalPages": 10
  }
}
```

### 获取作品详情
```http
GET /artworks/{artworkId}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "artwork_uuid",
    "artworkNo": "A001-0001",
    "title": "临王羲之兰亭序",
    "description": "临摹王羲之兰亭序，注重笔法传承...",
    "size": "138cm×69cm",
    "technique": "行书",
    "creationYear": 2024,
    "imageUrl": "https://...",
    "thumbnailUrl": "https://...",
    "authorName": "王书法",
    "authorPhone": "139****9999",
    "authorAddress": "北京市朝阳区...",
    "authorBio": "中国书法家协会会员...",
    "tags": ["行书", "临帖", "王羲之"],
    "status": "approved",
    "uploadTime": "2024-01-15T10:30:00Z",
    "project": {
      "id": "project_uuid",
      "name": "2024年春季书法大赛"
    }
  }
}
```

## 评分相关接口

### 创建评分
```http
POST /scores
Authorization: Bearer {token}
```

**请求体**:
```json
{
  "projectId": "project_uuid",
  "artworkId": "artwork_uuid",
  "objectivePass": true,
  "subjectiveScores": {
    "临帖传承": 25,
    "结体结构": 28,
    "艺术创意": 35,
    "整体效果": 30
  },
  "totalScore": 118
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "score_uuid",
    "projectId": "project_uuid",
    "artworkId": "artwork_uuid",
    "judgeId": "judge_uuid",
    "objectivePass": 1,
    "subjectiveScores": {
      "临帖传承": 25,
      "结体结构": 28,
      "艺术创意": 35,
      "整体效果": 30
    },
    "totalScore": 118,
    "status": "draft",
    "scoredAt": "2024-02-05T14:30:00Z"
  }
}
```

### 获取我的评分列表
```http
GET /scores/my/{projectId}?page=1&limit=10
Authorization: Bearer {token}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "score_uuid",
        "artwork": {
          "id": "artwork_uuid",
          "artworkNo": "A001-0001",
          "title": "临王羲之兰亭序",
          "thumbnailUrl": "https://...",
          "authorName": "王书法"
        },
        "objectivePass": 1,
        "totalScore": 118,
        "status": "submitted",
        "scoredAt": "2024-02-05T14:30:00Z"
      }
    ],
    "total": 25,
    "page": 1,
    "limit": 10,
    "totalPages": 3
  }
}
```

### 获取评分详情
```http
GET /scores/{scoreId}
Authorization: Bearer {token}
```

### 更新评分
```http
PATCH /scores/{scoreId}
Authorization: Bearer {token}
```

**请求体**:
```json
{
  "objectivePass": true,
  "subjectiveScores": {
    "临帖传承": 26,
    "结体结构": 29,
    "艺术创意": 36,
    "整体效果": 31
  }
}
```

### 提交评分
```http
PATCH /scores/{scoreId}/submit
Authorization: Bearer {token}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "score_uuid",
    "status": "submitted",
    "totalScore": 122
  },
  "message": "评分提交成功"
}
```

## 评语相关接口

### 添加评语
```http
POST /scores/{scoreId}/comments
Authorization: Bearer {token}
```

**请求体**:
```json
{
  "content": "整体笔法娴熟，结体端正，但在某些细节处理上还可以更加精细。",
  "audioUrl": "https://...",
  "annotations": [
    {
      "x": 150,
      "y": 200,
      "width": 80,
      "height": 40,
      "note": "此处笔画可以更加流畅"
    }
  ],
  "isPublic": true
}
```

### 获取评语列表
```http
GET /scores/{scoreId}/comments
Authorization: Bearer {token}
```

### 更新评语
```http
PATCH /scores/comments/{commentId}
Authorization: Bearer {token}
```

### 删除评语
```http
DELETE /scores/comments/{commentId}
Authorization: Bearer {token}
```

## 文件上传接口

### 上传语音评语
```http
POST /files/upload/document?folder=audio
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

**请求体**:
```
file: File (音频文件)
```

**响应**:
```json
{
  "success": true,
  "data": {
    "url": "/uploads/audio/uuid.mp3",
    "originalName": "voice_comment.mp3",
    "size": 1024000
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| AUTH_001 | 微信登录失败 |
| AUTH_002 | Token已过期 |
| AUTH_003 | 权限不足 |
| SCORE_001 | 重复评分 |
| SCORE_002 | 评分时间已过 |
| SCORE_003 | 评分信息不完整 |
| PROJECT_001 | 项目不存在 |
| PROJECT_002 | 项目未开始评分 |
| ARTWORK_001 | 作品不存在 |
| FILE_001 | 文件上传失败 |
| FILE_002 | 文件格式不支持 |
