import { Project } from '../../projects/entities/project.entity';
export declare enum ArtworkStatus {
    UPLOADED = "uploaded",
    REVIEWING = "reviewing",
    APPROVED = "approved",
    REJECTED = "rejected"
}
export declare class Artwork {
    id: string;
    projectId: string;
    artworkNo: string;
    title: string;
    description: string;
    size: string;
    technique: string;
    creationYear: number;
    imageUrl: string;
    thumbnailUrl: string;
    authorName: string;
    authorPhone: string;
    authorAddress: string;
    authorBio: string;
    tags: string[];
    status: ArtworkStatus;
    uploadTime: Date;
    reviewTime: Date;
    createdAt: Date;
    updatedAt: Date;
    project: Project;
}
