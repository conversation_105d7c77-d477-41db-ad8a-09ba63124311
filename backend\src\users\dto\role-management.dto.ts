import { IsEnum, IsString, IsOptional, IsBoolean, IsObject, IsArray, IsUUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { GlobalRole, ProjectRole } from '../entities/user-roles.entity';

/**
 * 申请全局角色DTO
 */
export class ApplyGlobalRoleDto {
  @ApiProperty({ enum: GlobalRole, description: '申请的角色' })
  @IsEnum(GlobalRole)
  targetRole: GlobalRole;

  @ApiProperty({ description: '申请材料和信息' })
  @IsObject()
  applicationData: {
    organizationName?: string;
    organizationType?: string;
    contactPerson?: string;
    contactPhone?: string;
    qualifications?: string[];
    experience?: string;
    reason?: string;
    attachments?: string[];
  };
}

/**
 * 审批角色申请DTO
 */
export class ReviewRoleApplicationDto {
  @ApiProperty({ description: '是否批准' })
  @IsBoolean()
  approved: boolean;

  @ApiPropertyOptional({ description: '审核意见' })
  @IsOptional()
  @IsString()
  reviewComment?: string;
}

/**
 * 分配全局角色DTO
 */
export class AssignGlobalRoleDto {
  @ApiProperty({ description: '用户ID' })
  @IsUUID()
  userId: string;

  @ApiProperty({ enum: GlobalRole, description: '角色' })
  @IsEnum(GlobalRole)
  role: GlobalRole;

  @ApiPropertyOptional({ description: '角色元数据' })
  @IsOptional()
  @IsObject()
  metadata?: {
    reason?: string;
    expiresAt?: Date;
    certifications?: string[];
  };
}

/**
 * 批量分配角色DTO
 */
export class BatchAssignRolesDto {
  @ApiProperty({ type: [AssignGlobalRoleDto], description: '角色分配列表' })
  @IsArray()
  assignments: AssignGlobalRoleDto[];
}

/**
 * 邀请用户加入项目DTO
 */
export class InviteUserToProjectDto {
  @ApiProperty({ description: '用户ID' })
  @IsUUID()
  userId: string;

  @ApiProperty({ enum: ProjectRole, description: '项目角色' })
  @IsEnum(ProjectRole)
  role: ProjectRole;

  @ApiPropertyOptional({ description: '自定义权限配置' })
  @IsOptional()
  @IsObject()
  permissions?: {
    canManageProject?: boolean;
    canInviteUsers?: boolean;
    canManageArtworks?: boolean;
    canScore?: boolean;
    canViewScores?: boolean;
    canExportData?: boolean;
    canModerateContent?: boolean;
    canManageSchedule?: boolean;
    customPermissions?: string[];
  };

  @ApiPropertyOptional({ description: '邀请消息' })
  @IsOptional()
  @IsString()
  message?: string;

  @ApiPropertyOptional({ description: '角色描述' })
  @IsOptional()
  @IsString()
  roleDescription?: string;

  @ApiPropertyOptional({ description: '职责列表' })
  @IsOptional()
  @IsArray()
  responsibilities?: string[];
}

/**
 * 批量邀请用户DTO
 */
export class BatchInviteUsersDto {
  @ApiProperty({ type: [InviteUserToProjectDto], description: '邀请列表' })
  @IsArray()
  invitations: InviteUserToProjectDto[];
}

/**
 * 更新项目权限DTO
 */
export class UpdateProjectPermissionsDto {
  @ApiProperty({ description: '权限配置' })
  @IsObject()
  permissions: {
    canManageProject?: boolean;
    canInviteUsers?: boolean;
    canManageArtworks?: boolean;
    canScore?: boolean;
    canViewScores?: boolean;
    canExportData?: boolean;
    canModerateContent?: boolean;
    canManageSchedule?: boolean;
    customPermissions?: string[];
  };
}

/**
 * 移除项目成员DTO
 */
export class RemoveProjectMemberDto {
  @ApiPropertyOptional({ description: '移除原因' })
  @IsOptional()
  @IsString()
  reason?: string;
}

/**
 * 创建权限模板DTO
 */
export class CreatePermissionTemplateDto {
  @ApiProperty({ description: '模板名称' })
  @IsString()
  name: string;

  @ApiProperty({ enum: ProjectRole, description: '适用角色' })
  @IsEnum(ProjectRole)
  role: ProjectRole;

  @ApiProperty({ description: '权限配置' })
  @IsObject()
  permissions: {
    canManageProject?: boolean;
    canInviteUsers?: boolean;
    canManageArtworks?: boolean;
    canScore?: boolean;
    canViewScores?: boolean;
    canExportData?: boolean;
    canModerateContent?: boolean;
    canManageSchedule?: boolean;
    customPermissions?: string[];
  };

  @ApiPropertyOptional({ description: '模板描述' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: '是否为默认模板' })
  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;
}

/**
 * 查询角色申请DTO
 */
export class QueryRoleApplicationsDto {
  @ApiPropertyOptional({ enum: ['pending', 'reviewing', 'approved', 'rejected'], description: '申请状态' })
  @IsOptional()
  @IsEnum(['pending', 'reviewing', 'approved', 'rejected'])
  status?: string;

  @ApiPropertyOptional({ enum: GlobalRole, description: '目标角色' })
  @IsOptional()
  @IsEnum(GlobalRole)
  targetRole?: GlobalRole;

  @ApiPropertyOptional({ description: '用户ID' })
  @IsOptional()
  @IsUUID()
  userId?: string;

  @ApiPropertyOptional({ description: '页码', default: 1 })
  @IsOptional()
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', default: 10 })
  @IsOptional()
  limit?: number;
}

/**
 * 查询项目成员DTO
 */
export class QueryProjectMembersDto {
  @ApiPropertyOptional({ enum: ProjectRole, description: '项目角色' })
  @IsOptional()
  @IsEnum(ProjectRole)
  role?: ProjectRole;

  @ApiPropertyOptional({ enum: ['pending', 'accepted', 'declined', 'removed'], description: '状态' })
  @IsOptional()
  @IsEnum(['pending', 'accepted', 'declined', 'removed'])
  status?: string;

  @ApiPropertyOptional({ description: '页码', default: 1 })
  @IsOptional()
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', default: 50 })
  @IsOptional()
  limit?: number;
}
