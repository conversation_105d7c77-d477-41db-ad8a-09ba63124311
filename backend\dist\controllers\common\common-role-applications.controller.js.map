{"version": 3, "file": "common-role-applications.controller.js", "sourceRoot": "", "sources": ["../../../src/controllers/common/common-role-applications.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CASwB;AACxB,6CAAqE;AACrE,uEAAmE;AACnE,yEAAkF;AAClF,yEAA2E;AAE3E,yFAAoF;AACpF,+FAA0F;AAC1F,6EAG6C;AAQtC,IAAM,gCAAgC,GAAtC,MAAM,gCAAiC,SAAQ,gCAAc;IAE/C;IACA;IAFnB,YACmB,qBAA4C,EAC5C,wBAAkD;QAEnE,KAAK,EAAE,CAAC;QAHS,0BAAqB,GAArB,qBAAqB,CAAuB;QAC5C,6BAAwB,GAAxB,wBAAwB,CAA0B;IAGrE,CAAC;IAUK,AAAN,KAAK,CAAC,kBAAkB,CACd,QAA4B,EAC1B,MAAc,EACb,GAAG;QAEd,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CACrE,MAAM,EACN,QAAQ,CAAC,UAAU,EACnB,QAAQ,CAAC,eAAe,CACzB,CAAC;YAEF,OAAO,IAAI,CAAC,OAAO,CACjB;gBACE,WAAW;gBACX,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,MAAM,EAAE,SAAS;gBACjB,OAAO,EAAE,kBAAkB;aAC5B,EACD,UAAU,EACV,GAAG,CACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBACtC,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YACxC,CAAC;YACD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACrC,OAAO,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,GAAG,CAAC,CAAC;YACjD,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IASK,AAAN,KAAK,CAAC,iBAAiB,CACX,MAAc,EACf,KAAyD,EACvD,GAAG;QAEd,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,CAAC;gBAClE,MAAM;gBACN,GAAG,KAAK;aACT,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC,SAAS,CACnB,MAAM,CAAC,IAAI,EACX,MAAM,CAAC,KAAK,EACZ,MAAM,CAAC,IAAI,EACX,MAAM,CAAC,KAAK,EACZ,YAAY,EACZ,GAAG,CACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IASK,AAAN,KAAK,CAAC,gBAAgB,CAAW,MAAc,EAAa,GAAG;QAC7D,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAE1E,OAAO,IAAI,CAAC,OAAO,CACjB;gBACE,MAAM;gBACN,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACxB,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,SAAS,EAAE,IAAI,CAAC,SAAS;iBAC1B,CAAC,CAAC;gBACH,UAAU,EAAE,KAAK,CAAC,MAAM;gBACxB,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM;aAClD,EACD,YAAY,EACZ,GAAG,CACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAUK,AAAN,KAAK,CAAC,uBAAuB,CACP,SAAiB,EAC3B,MAAc,EACb,GAAG;QAEd,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,wBAAwB,CAAC,uBAAuB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAE/E,OAAO,IAAI,CAAC,OAAO,CACjB;gBACE,SAAS;gBACT,MAAM;gBACN,MAAM,EAAE,UAAU;gBAClB,OAAO,EAAE,UAAU;aACpB,EACD,UAAU,EACV,GAAG,CACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnE,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;YACzC,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAUK,AAAN,KAAK,CAAC,wBAAwB,CACR,SAAiB,EAC3B,MAAc,EACb,GAAG;QAEd,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,wBAAwB,CAAC,wBAAwB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAEhF,OAAO,IAAI,CAAC,OAAO,CACjB;gBACE,SAAS;gBACT,MAAM;gBACN,MAAM,EAAE,UAAU;aACnB,EACD,UAAU,EACV,GAAG,CACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YACrC,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IASK,AAAN,KAAK,CAAC,uBAAuB,CACjB,MAAc,EACf,KAAyD,EACvD,GAAG;QAEd,IAAI,CAAC;YAGH,MAAM,eAAe,GAAG;gBACtB,IAAI,EAAE,EAAE;gBACR,KAAK,EAAE,CAAC;gBACR,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC;gBACrB,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,EAAE;aACzB,CAAC;YAEF,OAAO,IAAI,CAAC,SAAS,CACnB,eAAe,CAAC,IAAI,EACpB,eAAe,CAAC,KAAK,EACrB,eAAe,CAAC,IAAI,EACpB,eAAe,CAAC,KAAK,EACrB,YAAY,EACZ,GAAG,CACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IASK,AAAN,KAAK,CAAC,aAAa,CACP,MAAc,EACf,KAAwE,EACtE,GAAG;QAEd,IAAI,CAAC;YAGH,MAAM,YAAY,GAAG;gBACnB,IAAI,EAAE,EAAE;gBACR,KAAK,EAAE,CAAC;gBACR,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC;gBACrB,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,EAAE;aACzB,CAAC;YAEF,OAAO,IAAI,CAAC,SAAS,CACnB,YAAY,CAAC,IAAI,EACjB,YAAY,CAAC,KAAK,EAClB,YAAY,CAAC,IAAI,EACjB,YAAY,CAAC,KAAK,EAClB,YAAY,EACZ,GAAG,CACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IASK,AAAN,KAAK,CAAC,uBAAuB,CAAgB,IAAiB,EAAa,GAAG;QAC5E,IAAI,CAAC;YACH,MAAM,KAAK,GAAG;gBACZ,KAAK,EAAE;oBACL;wBACE,IAAI,EAAE,WAAW;wBACjB,IAAI,EAAE,KAAK;wBACX,WAAW,EAAE,aAAa;wBAC1B,YAAY,EAAE;4BACZ,WAAW;4BACX,UAAU;4BACV,UAAU;yBACX;wBACD,iBAAiB,EAAE;4BACjB,kBAAkB;4BAClB,kBAAkB;4BAClB,eAAe;4BACf,cAAc;4BACd,gBAAgB;yBACjB;qBACF;oBACD;wBACE,IAAI,EAAE,OAAO;wBACb,IAAI,EAAE,IAAI;wBACV,WAAW,EAAE,aAAa;wBAC1B,YAAY,EAAE;4BACZ,UAAU;4BACV,OAAO;4BACP,UAAU;yBACX;wBACD,iBAAiB,EAAE;4BACjB,YAAY;4BACZ,gBAAgB;4BAChB,QAAQ;yBACT;qBACF;oBACD;wBACE,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,QAAQ;wBACd,WAAW,EAAE,UAAU;wBACvB,YAAY,EAAE;4BACZ,SAAS;4BACT,UAAU;yBACX;wBACD,iBAAiB,EAAE;4BACjB,YAAY;4BACZ,QAAQ;yBACT;qBACF;iBACF;gBACD,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;oBAClB,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,YAAY,EAAE,IAAI,CAAC,KAAK;oBACxB,WAAW,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC;iBAChD,CAAC,CAAC,CAAC,IAAI;gBACR,kBAAkB,EAAE;oBAClB,QAAQ;oBACR,QAAQ;oBACR,SAAS;oBACT,aAAa;iBACd;aACF,CAAC;YAEF,OAAO,IAAI,CAAC,OAAO,CACjB,KAAK,EACL,YAAY,EACZ,GAAG,CACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,iBAAiB,CAAC,YAAsB;QAC9C,MAAM,QAAQ,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAClD,OAAO,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/D,CAAC;CACF,CAAA;AAjVY,4EAAgC;AAgBrC;IALL,IAAA,6BAAY,GAAE;IACd,IAAA,aAAI,GAAE;IACN,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IACnC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC;IACnD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC;IAExD,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,uBAAM,GAAE,CAAA;IACR,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCAFQ,wCAAkB;;0EA8BrC;AASK;IAJL,IAAA,6BAAY,GAAE;IACd,IAAA,YAAG,EAAC,iBAAiB,CAAC;IACtB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACrC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAE/C,WAAA,IAAA,uBAAM,GAAE,CAAA;IACR,WAAA,IAAA,cAAK,GAAE,CAAA;IACP,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;yEAmBX;AASK;IAJL,IAAA,6BAAY,GAAE;IACd,IAAA,YAAG,EAAC,UAAU,CAAC;IACf,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACrC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAC1B,WAAA,IAAA,uBAAM,GAAE,CAAA;IAAkB,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;wEAuB1D;AAUK;IALL,IAAA,6BAAY,GAAE;IACd,IAAA,aAAI,EAAC,uCAAuC,CAAC;IAC7C,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IACnC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC;IACnD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;IAEpD,WAAA,IAAA,cAAK,EAAC,WAAW,CAAC,CAAA;IAClB,WAAA,IAAA,uBAAM,GAAE,CAAA;IACR,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;+EAqBX;AAUK;IALL,IAAA,6BAAY,GAAE;IACd,IAAA,aAAI,EAAC,wCAAwC,CAAC;IAC9C,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IACnC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC;IACnD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC;IAEhD,WAAA,IAAA,cAAK,EAAC,WAAW,CAAC,CAAA;IAClB,WAAA,IAAA,uBAAM,GAAE,CAAA;IACR,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;gFAoBX;AASK;IAJL,IAAA,6BAAY,GAAE;IACd,IAAA,YAAG,EAAC,qBAAqB,CAAC;IAC1B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACrC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAE/C,WAAA,IAAA,uBAAM,GAAE,CAAA;IACR,WAAA,IAAA,cAAK,GAAE,CAAA;IACP,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;+EAuBX;AASK;IAJL,IAAA,6BAAY,GAAE;IACd,IAAA,YAAG,EAAC,aAAa,CAAC;IAClB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACrC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAE/C,WAAA,IAAA,uBAAM,GAAE,CAAA;IACR,WAAA,IAAA,cAAK,GAAE,CAAA;IACP,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;qEAuBX;AASK;IAJL,IAAA,6BAAY,GAAE;IACd,IAAA,YAAG,EAAC,OAAO,CAAC;IACZ,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACrC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACnB,WAAA,IAAA,4BAAW,GAAE,CAAA;IAAqB,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;+EAwEzE;2CAxUU,gCAAgC;IAF5C,IAAA,iBAAO,EAAC,SAAS,CAAC;IAClB,IAAA,mBAAU,EAAC,0BAA0B,CAAC;qCAGK,+CAAqB;QAClB,qDAAwB;GAH1D,gCAAgC,CAiV5C"}