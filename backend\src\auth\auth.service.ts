import {
  Injectable,
  UnauthorizedException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { LoginDto } from './dto/login.dto';
import { WechatLoginDto } from './dto/wechat-login.dto';
import { RegisterDto } from './dto/register.dto';
import { User, LoginType } from '../users/entities/user.entity';
import * as bcrypt from 'bcryptjs';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
  ) {}

  async login(loginDto: LoginDto) {
    const { username, password } = loginDto;
    
    // 查找用户（支持用户名或邮箱登录）
    let user = await this.usersService.findByUsername(username);
    if (!user) {
      user = await this.usersService.findByEmail(username);
    }

    if (!user) {
      throw new UnauthorizedException('用户名或密码错误');
    }

    // 验证密码
    const isPasswordValid = await this.usersService.validatePassword(user, password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('用户名或密码错误');
    }

    // 检查用户状态
    if (user.status !== 1) {
      throw new UnauthorizedException('账户已被禁用');
    }

    return this.generateTokens(user);
  }

  async register(registerDto: RegisterDto) {
    const { username, email, password } = registerDto;

    // 检查用户名是否已存在
    const existingUser = await this.usersService.findByUsername(username);
    if (existingUser) {
      throw new ConflictException('用户名已存在');
    }

    // 检查邮箱是否已存在
    if (email) {
      const existingEmail = await this.usersService.findByEmail(email);
      if (existingEmail) {
        throw new ConflictException('邮箱已存在');
      }
    }

    // 创建用户
    const user = await this.usersService.create({
      username,
      email,
      password,
      loginType: LoginType.WEBSITE,
    });

    return this.generateTokens(user);
  }

  async wechatLogin(wechatLoginDto: WechatLoginDto) {
    const { code, userInfo } = wechatLoginDto;

    // 这里应该调用微信API获取用户信息
    // 简化处理，假设已经获取到了openid
    const openid = `mock_openid_${Date.now()}`;

    let user = await this.usersService.findByOpenid(openid);

    if (!user) {
      // 创建新用户
      user = await this.usersService.create({
        openid,
        nickname: userInfo?.nickname,
        avatarUrl: userInfo?.avatarUrl,
        loginType: LoginType.WECHAT,
      });
    } else {
      // 更新用户信息
      if (userInfo) {
        await this.usersService.update(user.id, {
          nickname: userInfo.nickname,
          avatarUrl: userInfo.avatarUrl,
        });
      }
    }

    return this.generateTokens(user);
  }

  async getProfile(userId: string) {
    const user = await this.usersService.findOne(userId);
    const { password, ...profile } = user;
    return profile;
  }

  async refreshToken(user: User) {
    return this.generateTokens(user);
  }

  async logout(userId: string) {
    // 这里可以实现token黑名单逻辑
    return { message: '退出登录成功' };
  }

  private generateTokens(user: User) {
    const payload = {
      sub: user.id,
      username: user.username,
      role: user.role,
    };

    const accessToken = this.jwtService.sign(payload);
    const refreshToken = this.jwtService.sign(payload, { expiresIn: '7d' });

    return {
      accessToken,
      refreshToken,
      user: {
        id: user.id,
        username: user.username,
        nickname: user.nickname,
        role: user.role,
        avatarUrl: user.avatarUrl,
      },
    };
  }

  async validateUser(userId: string): Promise<User> {
    const user = await this.usersService.findOne(userId);
    if (!user || user.status !== 1) {
      throw new UnauthorizedException('用户不存在或已被禁用');
    }
    return user;
  }
}
