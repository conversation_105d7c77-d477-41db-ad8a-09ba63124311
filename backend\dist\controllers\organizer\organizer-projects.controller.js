"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizerProjectsController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const swagger_1 = require("@nestjs/swagger");
const base_controller_1 = require("../../common/base/base.controller");
const projects_service_1 = require("../../projects/projects.service");
const create_project_dto_1 = require("../../projects/dto/create-project.dto");
const update_project_dto_1 = require("../../projects/dto/update-project.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../../auth/guards/roles.guard");
const roles_decorator_1 = require("../../auth/decorators/roles.decorator");
const user_entity_1 = require("../../users/entities/user.entity");
const project_entity_1 = require("../../projects/entities/project.entity");
let OrganizerProjectsController = class OrganizerProjectsController extends base_controller_1.BaseController {
    projectsService;
    constructor(projectsService) {
        super();
        this.projectsService = projectsService;
    }
    async create(createProjectDto, coverImage, req) {
        try {
            const project = await this.projectsService.create(createProjectDto, req.user.id, coverImage);
            return this.created(project, '项目创建成功', req);
        }
        catch (error) {
            if (error.message.includes('验证失败')) {
                return this.validationError(error.details, error.message, req);
            }
            throw error;
        }
    }
    async findMyProjects(query, req) {
        const { page, limit } = this.parsePagination(query);
        const { sort, order } = this.parseSort(query);
        const result = await this.projectsService.findByOrganizer(req.user.id, {
            page,
            limit,
            status: query.status,
            search: query.search,
            sort,
            order
        });
        return this.paginated(result.data, result.total, result.page, result.limit, '项目列表获取成功', req);
    }
    async findOne(id, req) {
        try {
            const project = await this.projectsService.findOne(id);
            if (req.user.role !== user_entity_1.UserRole.SUPER_ADMIN &&
                req.user.role !== user_entity_1.UserRole.ADMIN &&
                project.organizerId !== req.user.id) {
                return this.forbidden('无权限查看此项目', req);
            }
            return this.success(project, '项目详情获取成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('项目不存在', req);
            }
            throw error;
        }
    }
    async update(id, updateProjectDto, coverImage, req) {
        try {
            const project = await this.projectsService.update(id, updateProjectDto, req.user, coverImage);
            return this.success(project, '项目信息更新成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('项目不存在', req);
            }
            if (error.message.includes('权限')) {
                return this.forbidden(error.message, req);
            }
            if (error.message.includes('验证失败')) {
                return this.validationError(error.details, error.message, req);
            }
            throw error;
        }
    }
    async updateStatus(id, status, req) {
        try {
            const project = await this.projectsService.updateStatus(id, status, req.user);
            const statusMap = {
                preparing: '准备中',
                collecting: '征集中',
                reviewing: '审核中',
                judging: '评选中',
                displaying: '展示中',
                finished: '已结束'
            };
            return this.success(project, `项目状态已更新为：${statusMap[status]}`, req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('项目不存在', req);
            }
            if (error.message.includes('权限')) {
                return this.forbidden(error.message, req);
            }
            if (error.message.includes('状态转换')) {
                return this.error(error.message, 400, 'BUSINESS_ERROR', undefined, undefined, req);
            }
            throw error;
        }
    }
    async generateQrCode(id, req) {
        try {
            const project = await this.projectsService.generateQrCode(id, req.user);
            return this.success({ qrCode: project.qrCode }, '项目二维码生成成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('项目不存在', req);
            }
            if (error.message.includes('权限')) {
                return this.forbidden(error.message, req);
            }
            throw error;
        }
    }
    async remove(id, req) {
        try {
            await this.projectsService.remove(id, req.user);
            return this.success(null, '项目删除成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('项目不存在', req);
            }
            if (error.message.includes('权限')) {
                return this.forbidden(error.message, req);
            }
            if (error.message.includes('不能删除')) {
                return this.error(error.message, 400, 'BUSINESS_ERROR', undefined, undefined, req);
            }
            throw error;
        }
    }
    async copyProject(id, req) {
        try {
            const newProject = await this.projectsService.copyProject(id, req.user);
            return this.created(newProject, '项目复制成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('项目不存在', req);
            }
            if (error.message.includes('权限')) {
                return this.forbidden(error.message, req);
            }
            throw error;
        }
    }
    async getProjectStats(id, req) {
        try {
            const stats = await this.projectsService.getProjectStats(id, req.user);
            return this.success(stats, '项目统计信息获取成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('项目不存在', req);
            }
            if (error.message.includes('权限')) {
                return this.forbidden(error.message, req);
            }
            throw error;
        }
    }
    async exportProject(id, type = 'all', format = 'excel', req) {
        try {
            const exportData = await this.projectsService.exportProject(id, req.user, { type, format });
            return this.success(exportData, '项目数据导出成功', req);
        }
        catch (error) {
            if (error.message.includes('不存在')) {
                return this.notFound('项目不存在', req);
            }
            if (error.message.includes('权限')) {
                return this.forbidden(error.message, req);
            }
            throw error;
        }
    }
};
exports.OrganizerProjectsController = OrganizerProjectsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建项目' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '项目创建成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数验证失败' }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('coverImage')),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.UploadedFile)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_project_dto_1.CreateProjectDto, typeof (_b = typeof Express !== "undefined" && (_a = Express.Multer) !== void 0 && _a.File) === "function" ? _b : Object, Object]),
    __metadata("design:returntype", Promise)
], OrganizerProjectsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)('my'),
    (0, swagger_1.ApiOperation)({ summary: '获取我的项目列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], OrganizerProjectsController.prototype, "findMyProjects", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取项目详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '项目不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], OrganizerProjectsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新项目信息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '项目不存在' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: '权限不足' }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('coverImage')),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.UploadedFile)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_project_dto_1.UpdateProjectDto, typeof (_d = typeof Express !== "undefined" && (_c = Express.Multer) !== void 0 && _c.File) === "function" ? _d : Object, Object]),
    __metadata("design:returntype", Promise)
], OrganizerProjectsController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, swagger_1.ApiOperation)({ summary: '更新项目状态' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '状态更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '状态流转不合法' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '项目不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('status')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], OrganizerProjectsController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Post)(':id/qrcode'),
    (0, swagger_1.ApiOperation)({ summary: '生成项目二维码' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '二维码生成成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '项目不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], OrganizerProjectsController.prototype, "generateQrCode", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除项目' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '项目不存在' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '项目状态不允许删除' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], OrganizerProjectsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/copy'),
    (0, swagger_1.ApiOperation)({ summary: '复制项目' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '项目复制成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '项目不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], OrganizerProjectsController.prototype, "copyProject", null);
__decorate([
    (0, common_1.Get)(':id/stats'),
    (0, swagger_1.ApiOperation)({ summary: '获取项目统计信息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '项目不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], OrganizerProjectsController.prototype, "getProjectStats", null);
__decorate([
    (0, common_1.Get)(':id/export'),
    (0, swagger_1.ApiOperation)({ summary: '导出项目数据' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '导出成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '项目不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('type')),
    __param(2, (0, common_1.Query)('format')),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, Object]),
    __metadata("design:returntype", Promise)
], OrganizerProjectsController.prototype, "exportProject", null);
exports.OrganizerProjectsController = OrganizerProjectsController = __decorate([
    (0, swagger_1.ApiTags)('办展方-项目管理'),
    (0, common_1.Controller)('org/projects'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ORGANIZER, user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.SUPER_ADMIN),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [projects_service_1.ProjectsService])
], OrganizerProjectsController);
//# sourceMappingURL=organizer-projects.controller.js.map