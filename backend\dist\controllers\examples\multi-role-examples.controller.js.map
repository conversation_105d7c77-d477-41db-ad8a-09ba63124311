{"version": 3, "file": "multi-role-examples.controller.js", "sourceRoot": "", "sources": ["../../../src/controllers/examples/multi-role-examples.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CASwB;AACxB,6CAAqE;AACrE,uEAAmE;AAGnE,yEAAoE;AACpE,yFAasD;AAGtD,yEAG8C;AAE9C,kEAAwD;AAExD,+EAA2E;AAQpE,IAAM,2BAA2B,GAAjC,MAAM,2BAA4B,SAAQ,gCAAc;IAChC;IAA7B,YAA6B,iBAAoC;QAC/D,KAAK,EAAE,CAAC;QADmB,sBAAiB,GAAjB,iBAAiB,CAAmB;IAEjE,CAAC;IAQK,AAAN,KAAK,CAAC,UAAU,CAAgB,IAAU,EAAa,GAAG;QACxD,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,WAAW,EAAE,IAAI,CAAC,KAAK;YACvB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;SAC5C,EACD,YAAY,EACZ,GAAG,CACJ,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,iBAAiB,CACR,SAAiB,EACf,IAAU,EACH,YAA2B,EACtC,GAAG;QAEd,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,OAAO,EAAE,UAAU;YACnB,SAAS;YACT,KAAK,EAAE;gBACL,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB;YACD,YAAY;YACZ,aAAa,EAAE;gBACb,QAAQ;gBACR,QAAQ;gBACR,MAAM;gBACN,MAAM;aACP;SACF,EACD,YAAY,EACZ,GAAG,CACJ,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,oBAAoB,CACX,SAAiB,EACf,IAAU,EACP,OAAgB,EACd,SAAkB,EAC3B,GAAG;QAEd,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,OAAO,EAAE,QAAQ;YACjB,SAAS;YACT,OAAO,EAAE;gBACP,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB;YACD,WAAW,EAAE;gBACX,OAAO;gBACP,SAAS;aACV;YACD,kBAAkB,EAAE;gBAClB,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;aACP;SACF,EACD,YAAY,EACZ,GAAG,CACJ,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,eAAe,CACN,SAAiB,EACf,IAAU,EACM,QAAiB,EACX,aAAsB,EAChD,GAAG;QAEd,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,OAAO,EAAE,QAAQ;YACjB,SAAS;YACT,KAAK,EAAE;gBACL,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,WAAW,EAAE,IAAI,CAAC,WAAW;aAC9B;YACD,WAAW,EAAE;gBACX,QAAQ;gBACR,aAAa;aACd;YACD,eAAe,EAAE;gBACf,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;aACP;SACF,EACD,UAAU,EACV,GAAG,CACJ,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,aAAa,CACJ,SAAiB,EACf,IAAU,EACH,YAA2B,EACpB,WAAgB,EAClC,GAAG;QAEd,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,OAAO,EAAE,QAAQ;YACjB,SAAS;YACT,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB;YACD,YAAY;YACZ,WAAW;YACX,cAAc,EAAE;gBACd,QAAQ;gBACR,MAAM;gBACN,MAAM;aACP;SACF,EACD,UAAU,EACV,GAAG,CACJ,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,iBAAiB,CACR,SAAiB,EACf,IAAU,EACd,GAAG;QAEd,OAAO,IAAI,CAAC,OAAO,CACjB;YACE,OAAO,EAAE,QAAQ;YACjB,SAAS;YACT,QAAQ,EAAE;gBACR,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB;YACD,UAAU,EAAE;gBACV,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,MAAM;gBAChB,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,MAAM;aAChB;SACF,EACD,QAAQ,EACR,GAAG,CACJ,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,UAAU,CACD,SAAiB,EACtB,UAKP,EACc,IAAU,EACd,GAAG;QAEd,IAAI,CAAC;YAEH,MAAM,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,CAAC,4BAA4B,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAChG,MAAM,WAAW,GAAG,EAAE,GAAG,kBAAkB,EAAE,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;YAGzE,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CACjE,SAAS,EACT,UAAU,CAAC,MAAM,EACjB,UAAU,CAAC,IAAI,EACf,WAAW,EACX,IAAI,CAAC,EAAE,EACP;gBACE,gBAAgB,EAAE,OAAO,UAAU,CAAC,IAAI,EAAE;gBAC1C,OAAO,EAAE,UAAU,CAAC,OAAO;aAC5B,CACF,CAAC;YAEF,OAAO,IAAI,CAAC,OAAO,CACjB;gBACE,UAAU;gBACV,OAAO,EAAE;oBACP,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;iBACxB;gBACD,WAAW,EAAE,UAAU,CAAC,IAAI;gBAC5B,WAAW;aACZ,EACD,UAAU,EACV,GAAG,CACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;YACvC,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAQK,AAAN,KAAK,CAAC,gBAAgB,CACP,SAAiB,EACpB,MAAc,EACb,GAAG;QAEd,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAExE,OAAO,IAAI,CAAC,OAAO,CACjB;gBACE,SAAS;gBACT,MAAM;gBACN,MAAM,EAAE,UAAU;aACnB,EACD,UAAU,EACV,GAAG,CACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;YACzC,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAQK,AAAN,KAAK,CAAC,uBAAuB,CACd,SAAiB,EACb,YAAoB,EAC7B,cAAmB,EACZ,IAAU,EACd,GAAG;QAEd,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,iBAAiB,CAAC,4BAA4B,CACvD,YAAY,EACZ,SAAS,EACT,cAAc,CACf,CAAC;YAEF,OAAO,IAAI,CAAC,OAAO,CACjB;gBACE,SAAS;gBACT,YAAY;gBACZ,cAAc;gBACd,SAAS,EAAE;oBACT,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;iBACxB;aACF,EACD,UAAU,EACV,GAAG,CACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;YACvC,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAQK,AAAN,KAAK,CAAC,iBAAiB,CACR,SAAiB,EACO,aAAsB,EAChD,GAAG;QAEd,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAG1E,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAC7C,EAAE,EAAE,MAAM,CAAC,EAAE;gBACb,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,IAAI,EAAE;oBACJ,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;oBAClB,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ;oBAC9B,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ;oBAC9B,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS;iBAC9B;gBAED,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;gBAC3D,QAAQ,EAAE;oBACR,SAAS,EAAE,MAAM,CAAC,QAAQ,EAAE,SAAS;oBACrC,UAAU,EAAE,MAAM,CAAC,QAAQ,EAAE,UAAU;iBACxC;aACF,CAAC,CAAC,CAAC;YAEJ,OAAO,IAAI,CAAC,OAAO,CACjB;gBACE,SAAS;gBACT,OAAO,EAAE,eAAe;gBACxB,YAAY,EAAE,OAAO,CAAC,MAAM;gBAC5B,cAAc,EAAE,aAAa;aAC9B,EACD,YAAY,EACZ,GAAG,CACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAQK,AAAN,KAAK,CAAC,kBAAkB,CACT,SAAiB,EACf,IAAU,EACP,OAAgB,EACd,SAAkB,EACP,QAAiB,EACX,aAAsB,EAC3B,SAAkB,EACvC,GAAG;QAEd,MAAM,YAAY,GAAQ;YACxB,SAAS;YACT,IAAI,EAAE;gBACJ,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB;YACD,WAAW,EAAE;gBACX,OAAO;gBACP,SAAS;gBACT,QAAQ;gBACR,aAAa;gBACb,SAAS;aACV;SACF,CAAC;QAGF,IAAI,OAAO,EAAE,CAAC;YACZ,YAAY,CAAC,SAAS,GAAG;gBACvB,eAAe,EAAE,QAAQ;gBACzB,aAAa,EAAE,MAAM;gBACrB,gBAAgB,EAAE,MAAM;aACzB,CAAC;QACJ,CAAC;QAED,IAAI,SAAS,EAAE,CAAC;YACd,YAAY,CAAC,WAAW,GAAG;gBACzB,UAAU,EAAE,MAAM;gBAClB,WAAW,EAAE,MAAM;gBACnB,iBAAiB,EAAE,MAAM;aAC1B,CAAC;QACJ,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACb,YAAY,CAAC,SAAS,GAAG;gBACvB,YAAY,EAAE,OAAO;gBACrB,cAAc,EAAE,MAAM;gBACtB,iBAAiB,EAAE,MAAM;aAC1B,CAAC;QACJ,CAAC;QAED,IAAI,aAAa,EAAE,CAAC;YAClB,YAAY,CAAC,UAAU,GAAG;gBACxB,SAAS,EAAE,MAAM;gBACjB,UAAU,EAAE,MAAM;gBAClB,QAAQ,EAAE,MAAM;aACjB,CAAC;QACJ,CAAC;QAED,IAAI,SAAS,EAAE,CAAC;YACd,YAAY,CAAC,aAAa,GAAG;gBAC3B,OAAO,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC;gBAChC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;aAC9B,CAAC;QACJ,CAAC;QAGD,YAAY,CAAC,SAAS,GAAG;YACvB,WAAW,EAAE,QAAQ;YACrB,cAAc,EAAE,MAAM;YACtB,aAAa,EAAE,MAAM;SACtB,CAAC;QAEF,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;IACrD,CAAC;CACF,CAAA;AAzcY,kEAA2B;AAWhC;IAHL,IAAA,6BAAY,GAAE;IACd,IAAA,YAAG,EAAC,UAAU,CAAC;IACf,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACpB,WAAA,IAAA,4BAAW,GAAE,CAAA;IAAc,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCAAhB,kBAAI;;6DAczC;AAQK;IAHL,IAAA,qCAAY,GAAE;IACd,IAAA,YAAG,EAAC,qCAAqC,CAAC;IAC1C,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IAEnC,WAAA,IAAA,kCAAS,GAAE,CAAA;IACX,WAAA,IAAA,4BAAW,GAAE,CAAA;IACb,WAAA,IAAA,2CAAkB,GAAE,CAAA;IACpB,WAAA,IAAA,gBAAO,GAAE,CAAA;;6CAFW,kBAAI;;oEAuB1B;AAQK;IAHL,IAAA,uCAAc,GAAE;IAChB,IAAA,YAAG,EAAC,gCAAgC,CAAC;IACrC,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IAEjC,WAAA,IAAA,kCAAS,GAAE,CAAA;IACX,WAAA,IAAA,4BAAW,GAAE,CAAA;IACb,WAAA,IAAA,uCAAc,GAAE,CAAA;IAChB,WAAA,IAAA,yCAAgB,GAAE,CAAA;IAClB,WAAA,IAAA,gBAAO,GAAE,CAAA;;6CAHW,kBAAI;;uEA2B1B;AAQK;IAHL,IAAA,qCAAY,GAAE;IACd,IAAA,YAAG,EAAC,6BAA6B,CAAC;IAClC,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IAEjC,WAAA,IAAA,kCAAS,GAAE,CAAA;IACX,WAAA,IAAA,4BAAW,GAAE,CAAA;IACb,WAAA,IAAA,6CAAoB,EAAC,OAAO,CAAC,CAAA;IAC7B,WAAA,IAAA,6CAAoB,EAAC,aAAa,CAAC,CAAA;IACnC,WAAA,IAAA,gBAAO,GAAE,CAAA;;6CAHW,kBAAI;;kEA4B1B;AAQK;IAHL,IAAA,sCAAa,GAAE;IACf,IAAA,YAAG,EAAC,iCAAiC,CAAC;IACtC,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IAEjC,WAAA,IAAA,kCAAS,GAAE,CAAA;IACX,WAAA,IAAA,4BAAW,GAAE,CAAA;IACb,WAAA,IAAA,2CAAkB,GAAE,CAAA;IACpB,WAAA,IAAA,kDAAyB,GAAE,CAAA;IAC3B,WAAA,IAAA,gBAAO,GAAE,CAAA;;6CAHW,kBAAI;;gEAwB1B;AAQK;IAHL,IAAA,0CAAiB,EAAC,QAAQ,CAAC;IAC3B,IAAA,YAAG,EAAC,iCAAiC,CAAC;IACtC,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IAEjC,WAAA,IAAA,kCAAS,GAAE,CAAA;IACX,WAAA,IAAA,4BAAW,GAAE,CAAA;IACb,WAAA,IAAA,gBAAO,GAAE,CAAA;;6CADW,kBAAI;;oEAqB1B;AAQK;IAHL,IAAA,0CAAiB,EAAC,QAAQ,CAAC;IAC3B,IAAA,aAAI,EAAC,4BAA4B,CAAC;IAClC,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IAEnC,WAAA,IAAA,kCAAS,GAAE,CAAA;IACX,WAAA,IAAA,aAAI,GAAE,CAAA;IAMN,WAAA,IAAA,4BAAW,GAAE,CAAA;IACb,WAAA,IAAA,gBAAO,GAAE,CAAA;;qDADW,kBAAI;;6DAwC1B;AAQK;IAHL,IAAA,6BAAY,GAAE;IACd,IAAA,aAAI,EAAC,uCAAuC,CAAC;IAC7C,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IAEjC,WAAA,IAAA,kCAAS,GAAE,CAAA;IACX,WAAA,IAAA,uBAAM,GAAE,CAAA;IACR,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;mEAoBX;AAQK;IAHL,IAAA,uCAAc,GAAE;IAChB,IAAA,cAAK,EAAC,iDAAiD,CAAC;IACxD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IAEjC,WAAA,IAAA,kCAAS,GAAE,CAAA;IACX,WAAA,IAAA,cAAK,EAAC,QAAQ,CAAC,CAAA;IACf,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,4BAAW,GAAE,CAAA;IACb,WAAA,IAAA,gBAAO,GAAE,CAAA;;6DADW,kBAAI;;0EA6B1B;AAQK;IAHL,IAAA,sCAAa,GAAE;IACf,IAAA,YAAG,EAAC,6BAA6B,CAAC;IAClC,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IAEnC,WAAA,IAAA,kCAAS,GAAE,CAAA;IACX,WAAA,IAAA,6CAAoB,EAAC,aAAa,CAAC,CAAA;IACnC,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;oEAsCX;AAQK;IAHL,IAAA,sCAAa,GAAE;IACf,IAAA,YAAG,EAAC,sCAAsC,CAAC;IAC3C,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;IAErC,WAAA,IAAA,kCAAS,GAAE,CAAA;IACX,WAAA,IAAA,4BAAW,GAAE,CAAA;IACb,WAAA,IAAA,uCAAc,GAAE,CAAA;IAChB,WAAA,IAAA,yCAAgB,GAAE,CAAA;IAClB,WAAA,IAAA,6CAAoB,EAAC,OAAO,CAAC,CAAA;IAC7B,WAAA,IAAA,6CAAoB,EAAC,aAAa,CAAC,CAAA;IACnC,WAAA,IAAA,6CAAoB,EAAC,QAAQ,CAAC,CAAA;IAC9B,WAAA,IAAA,gBAAO,GAAE,CAAA;;6CANW,kBAAI;;qEAuE1B;sCAxcU,2BAA2B;IAFvC,IAAA,iBAAO,EAAC,SAAS,CAAC;IAClB,IAAA,mBAAU,EAAC,qBAAqB,CAAC;qCAEgB,sCAAiB;GADtD,2BAA2B,CAycvC"}