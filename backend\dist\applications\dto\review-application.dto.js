"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewApplicationDto = void 0;
const class_validator_1 = require("class-validator");
const organizer_application_entity_1 = require("../entities/organizer-application.entity");
class ReviewApplicationDto {
    status;
    reviewComment;
}
exports.ReviewApplicationDto = ReviewApplicationDto;
__decorate([
    (0, class_validator_1.IsEnum)([organizer_application_entity_1.ApplicationStatus.APPROVED, organizer_application_entity_1.ApplicationStatus.REJECTED]),
    __metadata("design:type", String)
], ReviewApplicationDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ReviewApplicationDto.prototype, "reviewComment", void 0);
//# sourceMappingURL=review-application.dto.js.map