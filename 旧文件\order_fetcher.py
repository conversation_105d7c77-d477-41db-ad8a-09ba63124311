import requests
import json
from typing import Dict, List
import time

class OrderFetcher:
    def __init__(self):
        self.base_url = "https://api.zhiyuedaojia.com/demo/api"
        self.headers = {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "application/x-www-form-urlencoded",
            "DNT": "1",
            "Host": "api.zhiyuedaojia.com",
            "Origin": "https://sj.zhiyuedaojia.com",
            "Pragma": "no-cache",
            "Referer": "https://sj.zhiyuedaojia.com/",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-site",
            "token": "28d8ea4993f8ab4cc869a7bf7a467d34",
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Mobile/15E148 Safari/604.1"
        }
        self.request_interval = 1  # 设置请求间隔为3秒
        
    def get_order_list(self, store_id: str, page_size: int = 100, page_index: int = 1) -> List[str]:
        """获取订单列表"""
        url = f"{self.base_url}/order/y/base/listAllServiceOrderForStore"
        params = {
            "store_id": store_id,
            "virtual_flag": "",
            "page_size": page_size,
            "page_index": page_index
        }
        
        try:
            print(f"\n请求URL: {url}")
            print(f"请求参数: {params}")
            
            response = requests.get(url, headers=self.headers, params=params)
            print(f"响应状态码: {response.status_code}")
            
            try:
                data = response.json()
                if isinstance(data, dict) and isinstance(data.get("data"), list):
                    order_ids = [order.get("id") for order in data["data"] if order.get("id")]
                    print(f"本页获取到 {len(order_ids)} 个订单ID")
                    return order_ids
                else:
                    print("响应数据格式不正确")
                    print(f"响应内容: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    return []
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {str(e)}")
                print(f"响应内容: {response.text}")
                return []
            
        except requests.exceptions.RequestException as e:
            print(f"请求错误: {str(e)}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"错误响应状态码: {e.response.status_code}")
                print(f"错误响应内容: {e.response.text}")
            return []
        except Exception as e:
            print(f"其他错误: {str(e)}")
            return []

    def get_order_detail(self, order_id: str) -> Dict:
        """获取订单详情"""
        url = f"{self.base_url}/order/y/base/getServiceOrderById"
        params = {"order_id": order_id}
        
        try:
            print(f"\n请求订单详情URL: {url}")
            print(f"请求参数: {params}")
            
            response = requests.get(url, headers=self.headers, params=params)
            print(f"响应状态码: {response.status_code}")
            
            try:
                data = response.json()
                if isinstance(data, dict) and isinstance(data.get("data"), dict):
                    order_data = data["data"]
                    return {
                        "id": order_data.get("id", ""),
                        "user_name": order_data.get("user_name", ""),
                        "phone": order_data.get("phone", ""),
                        "address": order_data.get("address", ""),
                        "title": order_data.get("title", ""),
                        "total_amount": order_data.get("total_amount", 0),
                        "actual_amount": order_data.get("actual_amount", 0),
                        "tech_user_id": order_data.get("tech_user_id", ""),
                        "assigned_tech_user_id": order_data.get("assigned_tech_user_id", ""),
                        "tech_nick_name": order_data.get("tech_nick_name", ""),
                        "assigned_tech_nick_name": order_data.get("assigned_tech_nick_name", ""),
                        "tech_user_phone": order_data.get("tech_user_phone", ""),
                        "assigned_tech_user_phone": order_data.get("assigned_tech_user_phone", ""),
                        "cd_code": order_data.get("cd_code", ""),
                        "city_cd_code": order_data.get("city_cd_code", ""),
                        "detail_address": order_data.get("detail_address", ""),
                        "user_lng": order_data.get("user_lng", ""),
                        "user_lat": order_data.get("user_lat", ""),
                        "create_time": order_data.get("create_time", ""),
                        "predict_service_time": order_data.get("predict_service_time", "")
                    }
                else:
                    print("响应数据格式不正确")
                    print(f"响应内容: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    return {}
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {str(e)}")
                print(f"响应内容: {response.text}")
                return {}
            
        except requests.exceptions.RequestException as e:
            print(f"请求错误: {str(e)}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"错误响应状态码: {e.response.status_code}")
                print(f"错误响应内容: {e.response.text}")
            return {}
        except Exception as e:
            print(f"其他错误: {str(e)}")
            return {}

    def get_all_orders(self, store_id: str, page_size: int = 100) -> List[Dict]:
        """获取所有订单数据"""
        all_orders = []
        page = 1
        total_order_ids = []
        
        while True:
            print(f"\n正在获取第 {page} 页的订单ID...")
            order_ids = self.get_order_list(store_id, page_size, page)
            
            # 如果返回的订单数量小于页面大小，说明已经是最后一页
            if not order_ids or len(order_ids) < page_size:
                total_order_ids.extend(order_ids)  # 添加最后一页的订单
                break
                
            total_order_ids.extend(order_ids)
            page += 1
            print(f"等待 {self.request_interval} 秒后继续...")
            time.sleep(self.request_interval)  # 添加延迟，避免请求过于频繁
        
        print(f"\n共获取到 {len(total_order_ids)} 个订单ID")
        print("开始获取订单详细信息...")
        
        # 获取所有订单的详细信息
        for i, order_id in enumerate(total_order_ids, 1):
            print(f"\n正在获取订单 {i}/{len(total_order_ids)} 的详细信息...")
            order_detail = self.get_order_detail(order_id)
            if order_detail:
                all_orders.append(order_detail)
            print(f"等待 {self.request_interval} 秒后继续...")
            time.sleep(self.request_interval)  # 添加延迟，避免请求过于频繁
            
            # 每获取50个订单保存一次数据
            if i % 50 == 0:
                self.save_to_file(all_orders, f"orders_backup_{i}.json")
        
        # 最后保存完整数据
        self.save_to_file(all_orders, "orders_complete.json")
        return all_orders
    
    def save_to_file(self, data: List[Dict], filename: str):
        """保存数据到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"数据已保存到文件: {filename}")
        except Exception as e:
            print(f"保存文件出错: {e}")

def main():
    # 创建OrderFetcher实例
    fetcher = OrderFetcher()
    
    # 商店ID
    store_id = "4186fcec-56d5-4b81-a67f-6bb0232385c2"
    
    # 获取所有订单数据
    all_orders = fetcher.get_all_orders(store_id)
    print(f"\n成功获取 {len(all_orders)} 个订单的详细信息")

if __name__ == "__main__":
    main() 