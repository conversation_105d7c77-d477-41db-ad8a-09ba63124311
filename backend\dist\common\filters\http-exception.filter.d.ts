import { ExceptionFilter, ArgumentsHost, HttpException } from '@nestjs/common';
import { BusinessCode } from '../base/base.controller';
export declare class HttpExceptionFilter implements ExceptionFilter {
    private readonly logger;
    catch(exception: unknown, host: ArgumentsHost): void;
    private createErrorResponse;
    private createValidationErrorResponse;
    private getErrorType;
    private logError;
}
export declare class BusinessException extends HttpException {
    constructor(message: string, code?: BusinessCode, details?: any);
}
export declare class ValidationException extends HttpException {
    constructor(message: string, details?: any);
}
