"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityInterceptor = exports.CacheInterceptor = exports.LoggingInterceptor = exports.ResponseInterceptor = void 0;
const common_1 = require("@nestjs/common");
const operators_1 = require("rxjs/operators");
const uuid_1 = require("uuid");
let ResponseInterceptor = class ResponseInterceptor {
    intercept(context, next) {
        const ctx = context.switchToHttp();
        const request = ctx.getRequest();
        const response = ctx.getResponse();
        const startTime = Date.now();
        const requestId = request.headers['x-request-id'] || (0, uuid_1.v4)();
        return next.handle().pipe((0, operators_1.map)(data => {
            const responseTime = Date.now() - startTime;
            response.setHeader('X-Request-ID', requestId);
            response.setHeader('X-Response-Time', `${responseTime}ms`);
            response.setHeader('Content-Type', 'application/json; charset=utf-8');
            if (data && typeof data === 'object' && 'success' in data) {
                return data;
            }
            return {
                success: true,
                code: response.statusCode,
                message: '操作成功',
                data,
                timestamp: Date.now(),
                requestId,
            };
        }));
    }
};
exports.ResponseInterceptor = ResponseInterceptor;
exports.ResponseInterceptor = ResponseInterceptor = __decorate([
    (0, common_1.Injectable)()
], ResponseInterceptor);
let LoggingInterceptor = class LoggingInterceptor {
    intercept(context, next) {
        const ctx = context.switchToHttp();
        const request = ctx.getRequest();
        const response = ctx.getResponse();
        const { method, url, ip, headers } = request;
        const userAgent = headers['user-agent'] || '';
        const userId = request.user?.id || 'anonymous';
        const requestId = headers['x-request-id'] || (0, uuid_1.v4)();
        const startTime = Date.now();
        console.log(`[${new Date().toISOString()}] ${method} ${url} - ${ip} - ${userId} - ${requestId}`);
        return next.handle().pipe((0, operators_1.map)(data => {
            const responseTime = Date.now() - startTime;
            console.log(`[${new Date().toISOString()}] ${method} ${url} - ${response.statusCode} - ${responseTime}ms - ${requestId}`);
            return data;
        }));
    }
};
exports.LoggingInterceptor = LoggingInterceptor;
exports.LoggingInterceptor = LoggingInterceptor = __decorate([
    (0, common_1.Injectable)()
], LoggingInterceptor);
let CacheInterceptor = class CacheInterceptor {
    intercept(context, next) {
        const ctx = context.switchToHttp();
        const request = ctx.getRequest();
        const response = ctx.getResponse();
        return next.handle().pipe((0, operators_1.map)(data => {
            if (request.method === 'GET') {
                if (request.url.includes('/stats') || request.url.includes('/export')) {
                    response.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
                }
                else if (request.url.includes('/artworks') || request.url.includes('/projects')) {
                    response.setHeader('Cache-Control', 'public, max-age=300');
                }
                else {
                    response.setHeader('Cache-Control', 'public, max-age=60');
                }
                response.setHeader('Pragma', 'cache');
                response.setHeader('Expires', new Date(Date.now() + 60000).toUTCString());
            }
            return data;
        }));
    }
};
exports.CacheInterceptor = CacheInterceptor;
exports.CacheInterceptor = CacheInterceptor = __decorate([
    (0, common_1.Injectable)()
], CacheInterceptor);
let SecurityInterceptor = class SecurityInterceptor {
    intercept(context, next) {
        const ctx = context.switchToHttp();
        const response = ctx.getResponse();
        return next.handle().pipe((0, operators_1.map)(data => {
            response.setHeader('X-Content-Type-Options', 'nosniff');
            response.setHeader('X-Frame-Options', 'DENY');
            response.setHeader('X-XSS-Protection', '1; mode=block');
            response.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
            response.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
            response.removeHeader('X-Powered-By');
            response.removeHeader('Server');
            return data;
        }));
    }
};
exports.SecurityInterceptor = SecurityInterceptor;
exports.SecurityInterceptor = SecurityInterceptor = __decorate([
    (0, common_1.Injectable)()
], SecurityInterceptor);
//# sourceMappingURL=response.interceptor.js.map