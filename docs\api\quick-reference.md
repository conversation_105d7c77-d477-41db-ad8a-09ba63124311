# API 开发快速参考

## 🚀 快速开始

### 路由前缀
```
管理端：   /api/v1/admin/*
办展方端： /api/v1/org/*
小程序端： /api/v1/mp/*
公共接口： /api/v1/common/*
```

### 控制器继承
```typescript
import { BaseController } from '../../common/base/base.controller';

@Controller('admin/users')
export class AdminUsersController extends BaseController {
  constructor(private readonly service: Service) {
    super();
  }
}
```

## 📝 标准响应格式

### 成功响应
```typescript
// 单个数据
return this.success(data, '操作成功', req);

// 创建成功
return this.created(data, '创建成功', req);

// 分页数据
return this.paginated(items, total, page, limit, '获取成功', req);
```

### 错误响应
```typescript
// 通用错误
return this.error('错误信息', 400, 'BUSINESS_ERROR', details, field, req);

// 常用错误
return this.notFound('资源不存在', req);
return this.forbidden('权限不足', req);
return this.conflict('资源冲突', req);
return this.validationError(details, '验证失败', req);
```

## 🔧 常用工具方法

### 分页解析
```typescript
const { page, limit } = this.parsePagination(query);
const { sort, order } = this.parseSort(query);
const { startDate, endDate } = this.parseDateRange(query);
const fields = this.parseFields(query);
```

### 权限控制
```typescript
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
```

## 📊 分页查询模板

```typescript
@Get()
async findAll(@Query() query: any, @Request() req) {
  const { page, limit } = this.parsePagination(query);
  const { sort, order } = this.parseSort(query);
  
  const result = await this.service.findAll({
    page,
    limit,
    sort,
    order,
    status: query.status,
    search: query.search,
  });

  return this.paginated(
    result.data,
    result.total,
    result.page,
    result.limit,
    '获取成功',
    req
  );
}
```

## 📁 文件上传模板

```typescript
@Post('upload')
@UseInterceptors(FileInterceptor('file'))
async upload(
  @UploadedFile() file: Express.Multer.File,
  @Request() req
) {
  try {
    const result = await this.service.upload(file);
    return this.success(result, '上传成功', req);
  } catch (error) {
    if (error.message.includes('格式')) {
      return this.error('文件格式不支持', 400, 'VALIDATION_ERROR', undefined, undefined, req);
    }
    throw error;
  }
}
```

## 🔐 认证模板

```typescript
@Post('login')
async login(@Body() loginDto: LoginDto, @Request() req) {
  try {
    const result = await this.authService.login(loginDto);
    return this.success(result, '登录成功', req);
  } catch (error) {
    if (error.message.includes('密码错误')) {
      return this.error('用户名或密码错误', 401, 'AUTHENTICATION_ERROR', undefined, undefined, req);
    }
    throw error;
  }
}
```

## 📋 CRUD 操作模板

### 创建
```typescript
@Post()
async create(@Body() createDto: CreateDto, @Request() req) {
  try {
    const result = await this.service.create(createDto);
    return this.created(result, '创建成功', req);
  } catch (error) {
    if (error.message.includes('已存在')) {
      return this.conflict(error.message, req);
    }
    throw error;
  }
}
```

### 查询详情
```typescript
@Get(':id')
async findOne(@Param('id') id: string, @Request() req) {
  try {
    const result = await this.service.findOne(id);
    return this.success(result, '获取成功', req);
  } catch (error) {
    if (error.message.includes('不存在')) {
      return this.notFound('资源不存在', req);
    }
    throw error;
  }
}
```

### 更新
```typescript
@Patch(':id')
async update(
  @Param('id') id: string,
  @Body() updateDto: UpdateDto,
  @Request() req
) {
  try {
    const result = await this.service.update(id, updateDto);
    return this.success(result, '更新成功', req);
  } catch (error) {
    if (error.message.includes('不存在')) {
      return this.notFound('资源不存在', req);
    }
    if (error.message.includes('权限')) {
      return this.forbidden(error.message, req);
    }
    throw error;
  }
}
```

### 删除
```typescript
@Delete(':id')
async remove(@Param('id') id: string, @Request() req) {
  try {
    await this.service.remove(id);
    return this.success(null, '删除成功', req);
  } catch (error) {
    if (error.message.includes('不存在')) {
      return this.notFound('资源不存在', req);
    }
    throw error;
  }
}
```

## 🏷️ Swagger 文档模板

```typescript
@ApiTags('用户管理')
@Controller('admin/users')
@ApiBearerAuth()
export class AdminUsersController extends BaseController {
  
  @Post()
  @ApiOperation({ summary: '创建用户' })
  @ApiResponse({ status: 201, description: '用户创建成功' })
  @ApiResponse({ status: 400, description: '参数验证失败' })
  @ApiResponse({ status: 409, description: '用户已存在' })
  async create(@Body() createUserDto: CreateUserDto, @Request() req) {
    // 实现
  }
}
```

## ⚠️ 错误处理最佳实践

### Service 层抛出异常
```typescript
// 使用业务异常
throw new BusinessException('用户不存在', BusinessCode.USER_NOT_FOUND);

// 使用验证异常
throw new ValidationException('参数验证失败', validationDetails);
```

### Controller 层处理
```typescript
try {
  const result = await this.service.method();
  return this.success(result, '操作成功', req);
} catch (error) {
  // 具体业务错误处理
  if (error instanceof BusinessException) {
    return this.error(error.message, error.getStatus(), 'BUSINESS_ERROR', undefined, undefined, req);
  }
  
  // 通用错误处理
  if (error.message.includes('不存在')) {
    return this.notFound('资源不存在', req);
  }
  
  // 其他异常交给全局过滤器
  throw error;
}
```

## 🔍 查询参数规范

### 分页参数
```
?page=1&limit=10&sort=createdAt&order=DESC
```

### 筛选参数
```
?status=active&search=关键词&startDate=2024-01-01&endDate=2024-12-31
```

### 字段选择
```
?fields=id,name,email
```

## 📈 性能优化建议

1. **分页查询**：始终使用分页，默认限制每页数量
2. **字段选择**：支持字段选择，减少数据传输
3. **缓存策略**：GET请求添加适当的缓存头
4. **索引优化**：为常用查询字段添加数据库索引
5. **批量操作**：提供批量处理接口

## 🛡️ 安全检查清单

- [ ] 输入参数验证
- [ ] 权限检查
- [ ] SQL注入防护
- [ ] XSS攻击防护
- [ ] 文件上传安全
- [ ] 请求频率限制
- [ ] 敏感信息脱敏
- [ ] HTTPS传输

## 🧪 测试模板

```typescript
describe('UsersController', () => {
  test('should create user successfully', async () => {
    const createUserDto = { username: 'test', email: '<EMAIL>' };
    const response = await request(app)
      .post('/api/v1/admin/users')
      .set('Authorization', `Bearer ${token}`)
      .send(createUserDto);
    
    expect(response.status).toBe(201);
    expect(response.body.success).toBe(true);
    expect(response.body.data.username).toBe('test');
  });
});
```
