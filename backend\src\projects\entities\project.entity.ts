import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum ProjectStatus {
  PREPARING = 'preparing',
  COLLECTING = 'collecting',
  REVIEWING = 'reviewing',
  JUDGING = 'judging',
  DISPLAYING = 'displaying',
  FINISHED = 'finished',
}

@Entity('projects')
export class Project {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 100 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ name: 'cover_image', nullable: true })
  coverImage: string;

  @Column({ name: 'organizer_id' })
  organizerId: string;

  @Column({
    type: 'enum',
    enum: ProjectStatus,
    default: ProjectStatus.PREPARING,
  })
  status: ProjectStatus;

  @Column({ name: 'collect_start_time', type: 'datetime', nullable: true })
  collectStartTime: Date;

  @Column({ name: 'collect_end_time', type: 'datetime', nullable: true })
  collectEndTime: Date;

  @Column({ name: 'judge_start_time', type: 'datetime', nullable: true })
  judgeStartTime: Date;

  @Column({ name: 'judge_end_time', type: 'datetime', nullable: true })
  judgeEndTime: Date;

  @Column({ name: 'display_start_time', type: 'datetime', nullable: true })
  displayStartTime: Date;

  @Column({ name: 'display_end_time', type: 'datetime', nullable: true })
  displayEndTime: Date;

  @Column({ name: 'qr_code', nullable: true })
  qrCode: string;

  @Column({ name: 'is_public', default: 1 })
  isPublic: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User)
  @JoinColumn({ name: 'organizer_id' })
  organizer: User;

  // @OneToMany(() => Artwork, artwork => artwork.project)
  // artworks: Artwork[];

  // @OneToMany(() => ProjectMember, member => member.project)
  // members: ProjectMember[];

  // @OneToMany(() => ProjectCriteria, criteria => criteria.project)
  // criteria: ProjectCriteria[];
}
