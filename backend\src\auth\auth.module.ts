import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { UsersModule } from '../users/users.module';

// 新的守卫
import {
  NoAuthGuard,
  RequiredAuthGuard,
  OptionalAuthGuard,
  JwtAuthGuard,
  SmartAuthGuard
} from './guards/auth.guard';
import { RolesGuard } from './guards/roles.guard';

// 策略
import { JwtStrategy } from './strategies/jwt.strategy';

@Module({
  imports: [
    UsersModule,
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET') || 'your-secret-key',
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '24h'),
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    JwtStrategy,
    // 守卫
    NoAuthGuard,
    RequiredAuthGuard,
    OptionalAuthGuard,
    JwtAuthGuard,
    SmartAuthGuard,
    RolesGuard,
  ],
  exports: [
    AuthService,
    JwtModule,
    // 导出守卫供其他模块使用
    NoAuthGuard,
    RequiredAuthGuard,
    OptionalAuthGuard,
    JwtAuthGuard,
    SmartAuthGuard,
    RolesGuard,
  ],
})
export class AuthModule {}
