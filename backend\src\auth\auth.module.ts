import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

// 服务
import { AuthService } from './auth.service';
import { RoleManagementService } from './services/role-management.service';
import { ProjectPermissionService } from './services/project-permission.service';

// 控制器
import { AuthController } from './auth.controller';
import { UsersModule } from '../users/users.module';

// 实体
import { User } from '../users/entities/user.entity';
import {
  UserGlobalRole,
  UserProjectRole,
  RoleApplication,
  PermissionTemplate
} from '../users/entities/user-roles.entity';

// 守卫
import {
  NoAuthGuard,
  RequiredAuthGuard,
  OptionalAuthGuard,
  JwtAuthGuard,
  SmartAuthGuard
} from './guards/auth.guard';
import { RolesGuard } from './guards/roles.guard';
import { ProjectPermissionGuard, MultiRoleGuard, ResourceOwnerGuard } from './guards/project-permission.guard';

// 策略
import { JwtStrategy } from './strategies/jwt.strategy';

@Module({
  imports: [
    UsersModule,
    TypeOrmModule.forFeature([
      User,
      UserGlobalRole,
      UserProjectRole,
      RoleApplication,
      PermissionTemplate,
    ]),
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET') || 'your-secret-key',
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '24h'),
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [AuthController],
  providers: [
    // 服务
    AuthService,
    RoleManagementService,
    ProjectPermissionService,
    JwtStrategy,

    // 守卫
    NoAuthGuard,
    RequiredAuthGuard,
    OptionalAuthGuard,
    JwtAuthGuard,
    SmartAuthGuard,
    RolesGuard,
    ProjectPermissionGuard,
    MultiRoleGuard,
    ResourceOwnerGuard,
  ],
  exports: [
    // 服务
    AuthService,
    RoleManagementService,
    ProjectPermissionService,
    JwtModule,

    // 守卫
    NoAuthGuard,
    RequiredAuthGuard,
    OptionalAuthGuard,
    JwtAuthGuard,
    SmartAuthGuard,
    RolesGuard,
    ProjectPermissionGuard,
    MultiRoleGuard,
    ResourceOwnerGuard,
  ],
})
export class AuthModule {}
