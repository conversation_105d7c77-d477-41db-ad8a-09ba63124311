{"version": 3, "file": "role-management.service.js", "sourceRoot": "", "sources": ["../../../src/auth/services/role-management.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAkF;AAClF,6CAAmD;AACnD,qCAAqC;AACrC,kEAAwD;AACxD,8EAOgD;AAOzC,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAGtB;IAEA;IAEA;IAEA;IAEA;IAVV,YAEU,cAAgC,EAEhC,oBAAgD,EAEhD,qBAAkD,EAElD,qBAAkD,EAElD,kBAAkD;QARlD,mBAAc,GAAd,cAAc,CAAkB;QAEhC,yBAAoB,GAApB,oBAAoB,CAA4B;QAEhD,0BAAqB,GAArB,qBAAqB,CAA6B;QAElD,0BAAqB,GAArB,qBAAqB,CAA6B;QAElD,uBAAkB,GAAlB,kBAAkB,CAAgC;IACzD,CAAC;IAKJ,KAAK,CAAC,kBAAkB,CACtB,MAAc,EACd,UAAsB,EACtB,eAAoB;QAGpB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YAC3D,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE;SACpD,CAAC,CAAC;QAEH,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,IAAI,0BAAiB,CAAC,UAAU,CAAC,CAAC;QAC1C,CAAC;QAGD,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YAClE,KAAK,EAAE;gBACL,MAAM;gBACN,UAAU;gBACV,MAAM,EAAE,SAAS;aAClB;SACF,CAAC,CAAC;QAEH,IAAI,kBAAkB,EAAE,CAAC;YACvB,MAAM,IAAI,0BAAiB,CAAC,WAAW,CAAC,CAAC;QAC3C,CAAC;QAGD,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;YACpD,MAAM;YACN,UAAU;YACV,eAAe;YACf,MAAM,EAAE,SAAS;SAClB,CAAC,CAAC;QAEH,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC5D,CAAC;IAKD,KAAK,CAAC,qBAAqB,CACzB,aAAqB,EACrB,UAAkB,EAClB,QAAiB,EACjB,aAAsB;QAEtB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE;YAC5B,SAAS,EAAE,CAAC,MAAM,CAAC;SACpB,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,0BAAiB,CAAC,SAAS,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,WAAW,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACrC,MAAM,IAAI,0BAAiB,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC;QAGD,WAAW,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC;QACxD,WAAW,CAAC,UAAU,GAAG,UAAU,CAAC;QACpC,WAAW,CAAC,aAAa,GAAG,aAAa,CAAC;QAC1C,WAAW,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAEpC,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAGnD,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,IAAI,CAAC,aAAa,CACtB,WAAW,CAAC,MAAM,EAClB,WAAW,CAAC,UAAU,EACtB,UAAU,EACV;gBACE,MAAM,EAAE,UAAU;gBAClB,aAAa,EAAE,aAAa;aAC7B,CACF,CAAC;QACJ,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAKD,KAAK,CAAC,aAAa,CACjB,MAAc,EACd,IAAgB,EAChB,SAAiB,EACjB,QAAc;QAGd,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,0BAAiB,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC;QAGD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YAC3D,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;SACxB,CAAC,CAAC;QAEH,IAAI,YAAY,EAAE,CAAC;YACjB,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC;gBAC1B,MAAM,IAAI,0BAAiB,CAAC,UAAU,CAAC,CAAC;YAC1C,CAAC;iBAAM,CAAC;gBAEN,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC;gBAC7B,YAAY,CAAC,QAAQ,GAAG;oBACtB,GAAG,YAAY,CAAC,QAAQ;oBACxB,GAAG,QAAQ;oBACX,SAAS;oBACT,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;gBACF,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;QAGD,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;YAClD,MAAM;YACN,IAAI;YACJ,QAAQ,EAAE;gBACR,SAAS;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,GAAG,QAAQ;aACZ;YACD,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;QAEH,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC1D,CAAC;IAKD,KAAK,CAAC,gBAAgB,CACpB,MAAc,EACd,IAAgB,EAChB,SAAiB,EACjB,MAAe;QAEf,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YACzD,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE;SACxC,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,0BAAiB,CAAC,SAAS,CAAC,CAAC;QACzC,CAAC;QAGD,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC;QAC5B,UAAU,CAAC,QAAQ,GAAG;YACpB,GAAG,UAAU,CAAC,QAAQ;YACtB,SAAS;YACT,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,MAAM;SACP,CAAC;QAEF,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACnD,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,MAAc;QACrC,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;YAC1C,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE;YACjC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;SAC5B,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,mBAAmB,CACvB,OAMC;QAED,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;QAErE,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB;aAC5C,kBAAkB,CAAC,KAAK,CAAC;aACzB,iBAAiB,CAAC,UAAU,EAAE,MAAM,CAAC;aACrC,iBAAiB,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QAEjD,IAAI,MAAM,EAAE,CAAC;YACX,YAAY,CAAC,QAAQ,CAAC,sBAAsB,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACf,YAAY,CAAC,QAAQ,CAAC,8BAA8B,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACX,YAAY,CAAC,QAAQ,CAAC,sBAAsB,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QAC5D,CAAC;QAED,YAAY;aACT,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC;aAChC,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;aACxB,IAAI,CAAC,KAAK,CAAC,CAAC;QAEf,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM,YAAY,CAAC,eAAe,EAAE,CAAC;QAE3D,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IACtC,CAAC;IAKD,KAAK,CAAC,sBAAsB,CAAC,IAAkB;QAC7C,MAAM,KAAK,GAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;QACtC,IAAI,IAAI,EAAE,CAAC;YACT,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QACpB,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YACxC,KAAK;YACL,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE;SAC/C,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,wBAAwB,CAC5B,IAAY,EACZ,IAAiB,EACjB,WAAgB,EAChB,WAAoB,EACpB,YAAqB,KAAK;QAG1B,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAClC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,EACzB,EAAE,SAAS,EAAE,KAAK,EAAE,CACrB,CAAC;QACJ,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC9C,IAAI;YACJ,IAAI;YACJ,WAAW;YACX,WAAW;YACX,SAAS;SACV,CAAC,CAAC;QAEH,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtD,CAAC;IAKD,KAAK,CAAC,qBAAqB,CAAC,IAAiB;QAC3C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE;SACjD,CAAC,CAAC;QAEH,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,QAAQ,CAAC,WAAW,CAAC;QAC9B,CAAC;QAGD,OAAO,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,CAAC;IACnD,CAAC;IAKO,8BAA8B,CAAC,IAAiB;QACtD,MAAM,kBAAkB,GAAG;YACzB,CAAC,+BAAW,CAAC,aAAa,CAAC,EAAE;gBAC3B,gBAAgB,EAAE,IAAI;gBACtB,cAAc,EAAE,IAAI;gBACpB,iBAAiB,EAAE,IAAI;gBACvB,QAAQ,EAAE,KAAK;gBACf,aAAa,EAAE,IAAI;gBACnB,aAAa,EAAE,IAAI;gBACnB,kBAAkB,EAAE,IAAI;gBACxB,iBAAiB,EAAE,IAAI;gBACvB,iBAAiB,EAAE,EAAE;aACtB;YACD,CAAC,+BAAW,CAAC,aAAa,CAAC,EAAE;gBAC3B,gBAAgB,EAAE,IAAI;gBACtB,cAAc,EAAE,IAAI;gBACpB,iBAAiB,EAAE,IAAI;gBACvB,QAAQ,EAAE,KAAK;gBACf,aAAa,EAAE,IAAI;gBACnB,aAAa,EAAE,IAAI;gBACnB,kBAAkB,EAAE,IAAI;gBACxB,iBAAiB,EAAE,IAAI;gBACvB,iBAAiB,EAAE,EAAE;aACtB;YACD,CAAC,+BAAW,CAAC,aAAa,CAAC,EAAE;gBAC3B,gBAAgB,EAAE,KAAK;gBACvB,cAAc,EAAE,KAAK;gBACrB,iBAAiB,EAAE,KAAK;gBACxB,QAAQ,EAAE,IAAI;gBACd,aAAa,EAAE,IAAI;gBACnB,aAAa,EAAE,KAAK;gBACpB,kBAAkB,EAAE,KAAK;gBACzB,iBAAiB,EAAE,KAAK;gBACxB,iBAAiB,EAAE,EAAE;aACtB;YACD,CAAC,+BAAW,CAAC,cAAc,CAAC,EAAE;gBAC5B,gBAAgB,EAAE,KAAK;gBACvB,cAAc,EAAE,KAAK;gBACrB,iBAAiB,EAAE,KAAK;gBACxB,QAAQ,EAAE,KAAK;gBACf,aAAa,EAAE,KAAK;gBACpB,aAAa,EAAE,KAAK;gBACpB,kBAAkB,EAAE,KAAK;gBACzB,iBAAiB,EAAE,KAAK;gBACxB,iBAAiB,EAAE,EAAE;aACtB;YACD,CAAC,+BAAW,CAAC,cAAc,CAAC,EAAE;gBAC5B,gBAAgB,EAAE,KAAK;gBACvB,cAAc,EAAE,KAAK;gBACrB,iBAAiB,EAAE,KAAK;gBACxB,QAAQ,EAAE,KAAK;gBACf,aAAa,EAAE,KAAK;gBACpB,aAAa,EAAE,KAAK;gBACpB,kBAAkB,EAAE,KAAK;gBACzB,iBAAiB,EAAE,KAAK;gBACxB,iBAAiB,EAAE,EAAE;aACtB;YACD,CAAC,+BAAW,CAAC,iBAAiB,CAAC,EAAE;gBAC/B,gBAAgB,EAAE,KAAK;gBACvB,cAAc,EAAE,KAAK;gBACrB,iBAAiB,EAAE,IAAI;gBACvB,QAAQ,EAAE,KAAK;gBACf,aAAa,EAAE,IAAI;gBACnB,aAAa,EAAE,KAAK;gBACpB,kBAAkB,EAAE,IAAI;gBACxB,iBAAiB,EAAE,KAAK;gBACxB,iBAAiB,EAAE,EAAE;aACtB;YACD,CAAC,+BAAW,CAAC,iBAAiB,CAAC,EAAE;gBAC/B,gBAAgB,EAAE,KAAK;gBACvB,cAAc,EAAE,KAAK;gBACrB,iBAAiB,EAAE,IAAI;gBACvB,QAAQ,EAAE,KAAK;gBACf,aAAa,EAAE,KAAK;gBACpB,aAAa,EAAE,KAAK;gBACpB,kBAAkB,EAAE,IAAI;gBACxB,iBAAiB,EAAE,IAAI;gBACvB,iBAAiB,EAAE,EAAE;aACtB;SACF,CAAC;QAEF,OAAO,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IACxC,CAAC;IAKD,KAAK,CAAC,sBAAsB,CAC1B,WAIE,EACF,SAAiB;QAEjB,MAAM,OAAO,GAAqB,EAAE,CAAC;QAErC,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACrC,IAAI,CAAC;gBACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CACzC,UAAU,CAAC,MAAM,EACjB,UAAU,CAAC,IAAI,EACf,SAAS,EACT,UAAU,CAAC,QAAQ,CACpB,CAAC;gBACF,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBAEf,OAAO,CAAC,KAAK,CAAC,yBAAyB,UAAU,CAAC,IAAI,YAAY,UAAU,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YACzG,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,KAAK,CAAC,iBAAiB;QACrB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,oBAAoB;aACpD,kBAAkB,CAAC,IAAI,CAAC;aACxB,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC;aACzB,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;aAC9B,KAAK,CAAC,yBAAyB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;aACpD,OAAO,CAAC,SAAS,CAAC;aAClB,UAAU,EAAE,CAAC;QAEhB,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,qBAAqB;aACtD,kBAAkB,CAAC,KAAK,CAAC;aACzB,MAAM,CAAC,YAAY,EAAE,QAAQ,CAAC;aAC9B,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;aAC9B,OAAO,CAAC,YAAY,CAAC;aACrB,UAAU,EAAE,CAAC;QAEhB,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,qBAAqB;aACtD,kBAAkB,CAAC,IAAI,CAAC;aACxB,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC;aACzB,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;aAC9B,KAAK,CAAC,qBAAqB,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;aACpD,OAAO,CAAC,SAAS,CAAC;aAClB,UAAU,EAAE,CAAC;QAEhB,OAAO;YACL,WAAW,EAAE,eAAe;YAC5B,YAAY,EAAE,gBAAgB;YAC9B,YAAY,EAAE,gBAAgB;YAC9B,UAAU,EAAE,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;YAC7C,gBAAgB,EAAE,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,CAAC;YACtF,mBAAmB,EAAE,MAAM,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,CAAC;SAC/F,CAAC;IACJ,CAAC;CACF,CAAA;AA9bY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,kBAAI,CAAC,CAAA;IAEtB,WAAA,IAAA,0BAAgB,EAAC,kCAAc,CAAC,CAAA;IAEhC,WAAA,IAAA,0BAAgB,EAAC,mCAAe,CAAC,CAAA;IAEjC,WAAA,IAAA,0BAAgB,EAAC,mCAAe,CAAC,CAAA;IAEjC,WAAA,IAAA,0BAAgB,EAAC,sCAAkB,CAAC,CAAA;yDAPb,oBAAU,oBAAV,oBAAU,oDAEJ,oBAAU,oBAAV,oBAAU,oDAET,oBAAU,oBAAV,oBAAU,oDAEV,oBAAU,oBAAV,oBAAU,oDAEb,oBAAU,oBAAV,oBAAU;GAX7B,qBAAqB,CA8bjC"}