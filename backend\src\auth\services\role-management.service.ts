import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { 
  UserGlobalRole, 
  UserProjectRole, 
  RoleApplication, 
  PermissionTemplate,
  GlobalRole, 
  ProjectRole 
} from '../../users/entities/user-roles.entity';

/**
 * 角色管理服务
 * 处理用户角色申请、审批、分配等功能
 */
@Injectable()
export class RoleManagementService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(UserGlobalRole)
    private globalRoleRepository: Repository<UserGlobalRole>,
    @InjectRepository(UserProjectRole)
    private projectRoleRepository: Repository<UserProjectRole>,
    @InjectRepository(RoleApplication)
    private applicationRepository: Repository<RoleApplication>,
    @InjectRepository(PermissionTemplate)
    private templateRepository: Repository<PermissionTemplate>,
  ) {}

  /**
   * 申请全局角色
   */
  async applyForGlobalRole(
    userId: string,
    targetRole: GlobalRole,
    applicationData: any
  ): Promise<RoleApplication> {
    // 检查是否已有相同角色
    const existingRole = await this.globalRoleRepository.findOne({
      where: { userId, role: targetRole, isActive: true }
    });

    if (existingRole) {
      throw new ConflictException('您已经拥有此角色');
    }

    // 检查是否有待处理的申请
    const pendingApplication = await this.applicationRepository.findOne({
      where: { 
        userId, 
        targetRole, 
        status: 'pending' 
      }
    });

    if (pendingApplication) {
      throw new ConflictException('您已有待处理的申请');
    }

    // 创建申请记录
    const application = this.applicationRepository.create({
      userId,
      targetRole,
      applicationData,
      status: 'pending'
    });

    return await this.applicationRepository.save(application);
  }

  /**
   * 审批角色申请
   */
  async reviewRoleApplication(
    applicationId: string,
    reviewerId: string,
    approved: boolean,
    reviewComment?: string
  ): Promise<RoleApplication> {
    const application = await this.applicationRepository.findOne({
      where: { id: applicationId },
      relations: ['user']
    });

    if (!application) {
      throw new NotFoundException('申请记录不存在');
    }

    if (application.status !== 'pending') {
      throw new ConflictException('申请已被处理');
    }

    // 更新申请状态
    application.status = approved ? 'approved' : 'rejected';
    application.reviewerId = reviewerId;
    application.reviewComment = reviewComment;
    application.reviewedAt = new Date();

    await this.applicationRepository.save(application);

    // 如果批准，则分配角色
    if (approved) {
      await this.addGlobalRole(
        application.userId,
        application.targetRole,
        reviewerId,
        {
          reason: '角色申请审批通过',
          applicationId: applicationId
        }
      );
    }

    return application;
  }

  /**
   * 添加全局角色
   */
  async addGlobalRole(
    userId: string,
    role: GlobalRole,
    grantedBy: string,
    metadata?: any
  ): Promise<UserGlobalRole> {
    // 检查用户是否存在
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 检查是否已有此角色
    const existingRole = await this.globalRoleRepository.findOne({
      where: { userId, role }
    });

    if (existingRole) {
      if (existingRole.isActive) {
        throw new ConflictException('用户已拥有此角色');
      } else {
        // 重新激活角色
        existingRole.isActive = true;
        existingRole.metadata = {
          ...existingRole.metadata,
          ...metadata,
          grantedBy,
          grantedAt: new Date()
        };
        return await this.globalRoleRepository.save(existingRole);
      }
    }

    // 创建新角色
    const globalRole = this.globalRoleRepository.create({
      userId,
      role,
      metadata: {
        grantedBy,
        grantedAt: new Date(),
        ...metadata
      },
      isActive: true
    });

    return await this.globalRoleRepository.save(globalRole);
  }

  /**
   * 移除全局角色
   */
  async removeGlobalRole(
    userId: string,
    role: GlobalRole,
    removedBy: string,
    reason?: string
  ): Promise<void> {
    const globalRole = await this.globalRoleRepository.findOne({
      where: { userId, role, isActive: true }
    });

    if (!globalRole) {
      throw new NotFoundException('用户没有此角色');
    }

    // 软删除：设置为非活跃状态
    globalRole.isActive = false;
    globalRole.metadata = {
      ...globalRole.metadata,
      removedBy,
      removedAt: new Date(),
      reason
    };

    await this.globalRoleRepository.save(globalRole);
  }

  /**
   * 获取用户的所有全局角色
   */
  async getUserGlobalRoles(userId: string): Promise<UserGlobalRole[]> {
    return await this.globalRoleRepository.find({
      where: { userId, isActive: true },
      order: { createdAt: 'ASC' }
    });
  }

  /**
   * 获取角色申请列表
   */
  async getRoleApplications(
    filters: {
      status?: string;
      targetRole?: GlobalRole;
      userId?: string;
      page?: number;
      limit?: number;
    }
  ): Promise<{ data: RoleApplication[]; total: number; page: number; limit: number }> {
    const { status, targetRole, userId, page = 1, limit = 10 } = filters;

    const queryBuilder = this.applicationRepository
      .createQueryBuilder('app')
      .leftJoinAndSelect('app.user', 'user')
      .leftJoinAndSelect('app.reviewer', 'reviewer');

    if (status) {
      queryBuilder.andWhere('app.status = :status', { status });
    }

    if (targetRole) {
      queryBuilder.andWhere('app.targetRole = :targetRole', { targetRole });
    }

    if (userId) {
      queryBuilder.andWhere('app.userId = :userId', { userId });
    }

    queryBuilder
      .orderBy('app.appliedAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();

    return { data, total, page, limit };
  }

  /**
   * 获取权限模板
   */
  async getPermissionTemplates(role?: ProjectRole): Promise<PermissionTemplate[]> {
    const where: any = { isActive: true };
    if (role) {
      where.role = role;
    }

    return await this.templateRepository.find({
      where,
      order: { isDefault: 'DESC', createdAt: 'ASC' }
    });
  }

  /**
   * 创建权限模板
   */
  async createPermissionTemplate(
    name: string,
    role: ProjectRole,
    permissions: any,
    description?: string,
    isDefault: boolean = false
  ): Promise<PermissionTemplate> {
    // 如果设置为默认模板，先取消其他默认模板
    if (isDefault) {
      await this.templateRepository.update(
        { role, isDefault: true },
        { isDefault: false }
      );
    }

    const template = this.templateRepository.create({
      name,
      role,
      permissions,
      description,
      isDefault
    });

    return await this.templateRepository.save(template);
  }

  /**
   * 获取默认权限配置
   */
  async getDefaultPermissions(role: ProjectRole): Promise<any> {
    const template = await this.templateRepository.findOne({
      where: { role, isDefault: true, isActive: true }
    });

    if (template) {
      return template.permissions;
    }

    // 如果没有模板，返回硬编码的默认权限
    return this.getHardcodedDefaultPermissions(role);
  }

  /**
   * 硬编码的默认权限配置
   */
  private getHardcodedDefaultPermissions(role: ProjectRole): any {
    const defaultPermissions = {
      [ProjectRole.PROJECT_OWNER]: {
        canManageProject: true,
        canInviteUsers: true,
        canManageArtworks: true,
        canScore: false,
        canViewScores: true,
        canExportData: true,
        canModerateContent: true,
        canManageSchedule: true,
        customPermissions: []
      },
      [ProjectRole.PROJECT_ADMIN]: {
        canManageProject: true,
        canInviteUsers: true,
        canManageArtworks: true,
        canScore: false,
        canViewScores: true,
        canExportData: true,
        canModerateContent: true,
        canManageSchedule: true,
        customPermissions: []
      },
      [ProjectRole.PROJECT_JUDGE]: {
        canManageProject: false,
        canInviteUsers: false,
        canManageArtworks: false,
        canScore: true,
        canViewScores: true,
        canExportData: false,
        canModerateContent: false,
        canManageSchedule: false,
        customPermissions: []
      },
      [ProjectRole.PROJECT_ARTIST]: {
        canManageProject: false,
        canInviteUsers: false,
        canManageArtworks: false,
        canScore: false,
        canViewScores: false,
        canExportData: false,
        canModerateContent: false,
        canManageSchedule: false,
        customPermissions: []
      },
      [ProjectRole.PROJECT_VIEWER]: {
        canManageProject: false,
        canInviteUsers: false,
        canManageArtworks: false,
        canScore: false,
        canViewScores: false,
        canExportData: false,
        canModerateContent: false,
        canManageSchedule: false,
        customPermissions: []
      },
      [ProjectRole.PROJECT_MODERATOR]: {
        canManageProject: false,
        canInviteUsers: false,
        canManageArtworks: true,
        canScore: false,
        canViewScores: true,
        canExportData: false,
        canModerateContent: true,
        canManageSchedule: false,
        customPermissions: []
      },
      [ProjectRole.PROJECT_ASSISTANT]: {
        canManageProject: false,
        canInviteUsers: false,
        canManageArtworks: true,
        canScore: false,
        canViewScores: false,
        canExportData: false,
        canModerateContent: true,
        canManageSchedule: true,
        customPermissions: []
      }
    };

    return defaultPermissions[role] || {};
  }

  /**
   * 批量分配角色
   */
  async batchAssignGlobalRoles(
    assignments: Array<{
      userId: string;
      role: GlobalRole;
      metadata?: any;
    }>,
    grantedBy: string
  ): Promise<UserGlobalRole[]> {
    const results: UserGlobalRole[] = [];

    for (const assignment of assignments) {
      try {
        const globalRole = await this.addGlobalRole(
          assignment.userId,
          assignment.role,
          grantedBy,
          assignment.metadata
        );
        results.push(globalRole);
      } catch (error) {
        // 记录错误但继续处理其他分配
        console.error(`Failed to assign role ${assignment.role} to user ${assignment.userId}:`, error.message);
      }
    }

    return results;
  }

  /**
   * 获取角色统计信息
   */
  async getRoleStatistics(): Promise<any> {
    const globalRoleStats = await this.globalRoleRepository
      .createQueryBuilder('gr')
      .select('gr.role', 'role')
      .addSelect('COUNT(*)', 'count')
      .where('gr.isActive = :isActive', { isActive: true })
      .groupBy('gr.role')
      .getRawMany();

    const applicationStats = await this.applicationRepository
      .createQueryBuilder('app')
      .select('app.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('app.status')
      .getRawMany();

    const projectRoleStats = await this.projectRoleRepository
      .createQueryBuilder('pr')
      .select('pr.role', 'role')
      .addSelect('COUNT(*)', 'count')
      .where('pr.status = :status', { status: 'accepted' })
      .groupBy('pr.role')
      .getRawMany();

    return {
      globalRoles: globalRoleStats,
      applications: applicationStats,
      projectRoles: projectRoleStats,
      totalUsers: await this.userRepository.count(),
      totalActiveRoles: await this.globalRoleRepository.count({ where: { isActive: true } }),
      totalProjectMembers: await this.projectRoleRepository.count({ where: { status: 'accepted' } })
    };
  }
}
